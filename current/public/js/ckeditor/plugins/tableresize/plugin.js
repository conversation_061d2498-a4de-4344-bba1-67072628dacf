/*
 Copyright (c) 2003-2014, CKSource - <PERSON><PERSON>. All rights reserved.
 For licensing, see LICENSE.md or http://ckeditor.com/license
*/
(function(){function t(b){return CKEDITOR.env.ie?b.$.clientWidth:parseInt(b.getComputedStyle("width"),10)}function n(b,i){var a=b.getComputedStyle("border-"+i+"-width"),g={thin:"0px",medium:"1px",thick:"2px"};0>a.indexOf("px")&&(a=a in g&&"none"!=b.getComputedStyle("border-style")?g[a]:0);return parseInt(a,10)}function v(b){var i=[],a=-1,g="rtl"==b.getComputedStyle("direction"),d;d=b.$.rows;for(var p=0,e,f,c,h=0,o=d.length;h<o;h++)c=d[h],e=c.cells.length,e>p&&(p=e,f=c);d=f;p=new CKEDITOR.dom.element(b.$.tBodies[0]);
e=p.getDocumentPosition();f=0;for(c=d.cells.length;f<c;f++){var h=new CKEDITOR.dom.element(d.cells[f]),o=d.cells[f+1]&&new CKEDITOR.dom.element(d.cells[f+1]),a=a+(h.$.colSpan||1),k,j,l=h.getDocumentPosition().x;g?j=l+n(h,"left"):k=l+h.$.offsetWidth-n(h,"right");o?(l=o.getDocumentPosition().x,g?k=l+o.$.offsetWidth-n(o,"right"):j=l+n(o,"left")):(l=b.getDocumentPosition().x,g?k=l:j=l+b.$.offsetWidth);h=Math.max(j-k,3);i.push({table:b,index:a,x:k,y:e.y,width:h,height:p.$.offsetHeight,rtl:g})}return i}
function u(b){(b.data||b).preventDefault()}function z(b){function i(){h=0;c.setOpacity(0);k&&a();var A=e.table;setTimeout(function(){A.removeCustomData("_cke_table_pillars")},0);f.removeListener("dragstart",u)}function a(){for(var a=e.rtl,b=a?l.length:w.length,c=0;c<b;c++){var d=w[c],f=l[c],g=e.table;CKEDITOR.tools.setTimeout(function(b,c,d,e,f,h){b&&b.setStyle("width",j(Math.max(c+h,0)));d&&d.setStyle("width",j(Math.max(e-h,0)));f&&g.setStyle("width",j(f+h*(a?-1:1)))},0,this,[d,d&&t(d),f,f&&t(f),
(!d||!f)&&t(g)+n(g,"left")+n(g,"right"),k])}}function g(a){u(a);for(var a=e.index,b=CKEDITOR.tools.buildTableMap(e.table),g=[],i=[],j=Number.MAX_VALUE,n=j,s=e.rtl,r=0,v=b.length;r<v;r++){var m=b[r],q=m[a+(s?1:0)],m=m[a+(s?0:1)],q=q&&new CKEDITOR.dom.element(q),m=m&&new CKEDITOR.dom.element(m);if(!q||!m||!q.equals(m))q&&(j=Math.min(j,t(q))),m&&(n=Math.min(n,t(m))),g.push(q),i.push(m)}w=g;l=i;x=e.x-j;y=e.x+n;c.setOpacity(0.5);o=parseInt(c.getStyle("left"),10);k=0;h=1;c.on("mousemove",p);f.on("dragstart",
u);f.on("mouseup",d,this)}function d(a){a.removeListener();i()}function p(a){r(a.data.getPageOffset().x)}var e,f,c,h,o,k,w,l,x,y;f=b.document;c=CKEDITOR.dom.element.createFromHtml('<div data-cke-temp=1 contenteditable=false unselectable=on style="position:absolute;cursor:col-resize;filter:alpha(opacity=0);opacity:0;padding:0;background-color:#004;background-image:none;border:0px none;z-index:10"></div>',f);b.on("destroy",function(){c.remove()});s||f.getDocumentElement().append(c);this.attachTo=function(a){h||
(s&&(f.getBody().append(c),k=0),e=a,c.setStyles({width:j(a.width),height:j(a.height),left:j(a.x),top:j(a.y)}),s&&c.setOpacity(0.25),c.on("mousedown",g,this),f.getBody().setStyle("cursor","col-resize"),c.show())};var r=this.move=function(a){if(!e)return 0;if(!h&&(a<e.x||a>e.x+e.width))return e=null,h=k=0,f.removeListener("mouseup",d),c.removeListener("mousedown",g),c.removeListener("mousemove",p),f.getBody().setStyle("cursor","auto"),s?c.remove():c.hide(),0;a-=Math.round(c.$.offsetWidth/2);if(h){if(a==
x||a==y)return 1;a=Math.max(a,x);a=Math.min(a,y);k=a-o}c.setStyle("left",j(a));return 1}}function r(b){var i=b.data.getTarget();if("mouseout"==b.name){if(!i.is("table"))return;for(var a=new CKEDITOR.dom.element(b.data.$.relatedTarget||b.data.$.toElement);a&&a.$&&!a.equals(i)&&!a.is("body");)a=a.getParent();if(!a||a.equals(i))return}i.getAscendant("table",1).removeCustomData("_cke_table_pillars");b.removeListener()}var j=CKEDITOR.tools.cssLength,s=CKEDITOR.env.ie&&(CKEDITOR.env.ie7Compat||CKEDITOR.env.quirks);
CKEDITOR.plugins.add("tableresize",{requires:"tabletools",init:function(b){b.on("contentDom",function(){var i;b.document.getBody().on("mousemove",function(a){var a=a.data,g=a.getPageOffset().x;if(i&&i.move(g))u(a);else{var a=a.getTarget(),d;if(a.is("table")||a.getAscendant("tbody",1)){d=a.getAscendant("table",1);if(!(a=d.getCustomData("_cke_table_pillars")))d.setCustomData("_cke_table_pillars",a=v(d)),d.on("mouseout",r),d.on("mousedown",r);a:{d=0;for(var j=a.length;d<j;d++){var e=a[d];if(g>=e.x&&
g<=e.x+e.width){g=e;break a}}g=null}g&&(!i&&(i=new z(b)),i.attachTo(g))}}})})}})})();