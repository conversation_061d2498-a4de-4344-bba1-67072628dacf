<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<link rel="stylesheet" type="text/css" href="/interactives/general/css/style.css"/>
	<link rel="stylesheet" type="text/css" href="css/jquery-ui.min.css" />
	<link rel="stylesheet" type="text/css" href="css/style.css" />
</head>
<body>
<script>
	(function(i,s,o,g,r,a,m){i['GoogleAnalyticsObject']=r;i[r]=i[r]||function(){
			(i[r].q=i[r].q||[]).push(arguments)},i[r].l=1*new Date();a=s.createElement(o),
		m=s.getElementsByTagName(o)[0];a.async=1;a.src=g;m.parentNode.insertBefore(a,m)
	})(window,document,'script','https://www.google-analytics.com/analytics.js','ga');

	ga('create', 'UA-2014879-79', 'auto');
	ga('send', 'pageview');

</script>

<?php
$configString = file_get_contents("config.json");
$config = json_decode($configString);
$title = $config->title;
$description = $config->description;
?>

<div class="content">
	<!--<div class="heading">
<h1><?php echo $title ?></h1>
<p><?php echo $description ?></p>
</div>-->

	<div class="scenario-count">
		<ul>
			<li>1</li>
			<li>2</li>
		</ul>
	</div>

	<!-- Step 1 -->
	<div class="steps step1">
		<div class='top-stories-backgrounds'>
			<div class='story first'></div>
			<div class='story'></div>
			<div class='story'></div>
			<div class='story'></div>
			<div class='story last'></div>
		</div>

		<div class='top-stories'>
			<div class='story first'>
				<img src='' />
			</div>
			<div class='story'>
				<img src='' />
			</div>
			<div class='story'>
				<img src='' />
			</div>
			<div class='story'>
				<img src='' />
			</div>
			<div class='story last'>
				<img src='' />
			</div>
		</div>

		<div class='arrow'></div>

		<div class='bottom-stories'>
			<div class='story first story1'>
				<img src='' />
			</div>
			<div class='story story2'>
				<img src='' />
			</div>
			<div class='story story3'>
				<img src='' />
			</div>
			<div class='story story4'>
				<img src='' />
			</div>
			<div class='story last story5'>
				<img src='' />
			</div>
		</div>

		<div class='confirmBtnHolder'>
			<p>&nbsp;</p>

			<a class='big-btn btn-reset' href="#" id='resetSequence'>Ath Shocraigh!</a>
			<a class='big-btn' href="#" id='verifySequence1'>Seiceáil do fhreagra!</a>
		</div>
	</div>

	<!-- Step 2 -->
	<div class="steps step2">
		<div class="compass"></div>

		<div class="top-stories">
			<div class="bg">
				<div class="story first">
					<img src="" />
				</div>
			</div>
			<div class="bg">
				<div class="story">
					<img src="" />
				</div>
			</div>
			<div class="bg">
				<div class="story">
					<img src="" />
				</div>
			</div>
			<div class="bg">
				<div class="story">
					<img src="" />
				</div>
			</div>
			<div class="bg">
				<div class="story last">
					<img src="" />
				</div>
			</div>
		</div>

		<!--        <div class="arrow"></div>-->

		<div class="bottom-stories">
			<div class="wrap">
				<div class="story first story1" data-location="bethelem">
					<p>Beithil</p>
					<img src="" />
				</div>
				<div class="story story2" data-location="nazareth">
					<p>Nazarat</p>
					<img src="" />
				</div>
				<div class="story story3" data-location="jerusalem">
					<p>Iarúsailéim</p>
					<img src="" />
				</div>
				<div class="story story4" data-location="sea-of-galilee">
					<p>Muir na Gailíle</p>
					<img src="" />
				</div>
				<div class="story last story5" data-location="jerusalem">
					<p>Iarúsailéim</p>
					<img src="" />
				</div>
			</div>
		</div>

		<div class="arrow_right"></div>

		<div class="confirmBtnHolder">
			<p>&nbsp;</p>
			<a class="big-btn" href="#" id="verifySequence2">Seiceáil do fhreagra!</a>
		</div>
	</div>

	<?php $referer = isset($_SERVER["HTTP_REFERER"]) ? $_SERVER["HTTP_REFERER"] : ''; ?>

	<?php if($referer != ""): ?>
		<div class="cancelBtnHolder">
			<a class="big-btn" onclick="window.close(); return false;" href="<?php echo $referer ?>"></a>
		</div>
	<?php endif ?>
</div>

<div class="win-screen">
	<div class="container">
		<div class="win">
			<img src="img/thumb-up.png" />
		</div>
		<h2>An-mhaith!</h2>
		<a class="big-btn replay" href="#">Imir Arís</a>
	</div>
</div>

<div class="start-screen">
	<div class="container">
		<h3>Ag cur scéal Íosa san ord ceart</h3>
		<p>Tarraing agus Scaoil scéalta chun an seicheamh ceart a mheaitseáil</p>
		<a class="big-btn start" href="#">Imir Anois</a>
	</div>
</div>

<div class="start-screen2">
	<div class="container">
		<h3>Ag cur scéal Íosa san ord ceart</h3>
		<p>Drag and drop the illustrations to the places where the events happened</p>
		<a class="big-btn start2" href="#">Leanúint ar aghaidh</a>
	</div>
</div>

<script type="text/javascript" src="js/jquery-1.11.2.min.js"></script>
<script type="text/javascript" src="js/jquery-ui.min.js"></script>
<script type="text/javascript" src="js/script.js"></script>
<script type="text/javascript" src="/interactives/general/js/script.js"></script>
</body>
</html>
