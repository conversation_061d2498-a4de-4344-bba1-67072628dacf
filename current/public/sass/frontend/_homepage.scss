
.home-panel {
     @include clearfix();
     background-position: 50% bottom;
     background-repeat: no-repeat;
     height: 500px;
     overflow-y: hidden;
     z-index: 1;
     .graphic, .text {
          background-repeat: no-repeat;
     }
     .text {
          background-position: 0 bottom;
          padding-top: 90px;
          font-family: 'CambridgeRound-Regular', "Helvetica Neue", Helvetica, Arial, sans-serif;
          font-size: 18px;
          line-height: 28px;
          letter-spacing: 0.020em;
          color: $grey;
          h2{
               margin-top: 0;
               margin-bottom: 12px;
          }
          h1{
               margin-bottom: 8px;
          }
     }
     .text-body{
          height: 153px;
     }
     .graphic {
          text-align: center;
          display: flex;
          justify-content: center; /* align horizontal */
          align-items: center;
          height: 100%;
     }
     .graphic img{
          width: 80%;
          position: relative;
     }
     h1 {
          font-weight: normal;
     }
     hr {
          background-color: $dark-pink;
     }
}

.home-panel-wide {
     .text {
          text-align: center;
     }
}

#intro-panel {
      width: 100%;
      background-color: $white;
      height: 625px;
      .graphic, .text {
           background-repeat: no-repeat;
      }
     .text {
          height: 600px;
          font-size: 20px;
          line-height: 32px;
          letter-spacing: 0.030em;
          color: $grey;
          h1 {
               color: $dark-blue;
          }
          .text-spacer{
               background-color: $dark-blue;
               display: inline-block;
          }
     }
     .text-body{
          height: 250px;
     }
     .carousel-pane{
          position: relative;
          bottom: 6px;
          right: 4px;
     }
 }

#first-panel {
     .text{
          background-color: $dark-blue;
          color: $white;
     }
     .graphic{
          img{
               top: 70px;
          }
     }
}

#second-panel {
     .text{
          background-color: $purple;
          color: $white;
     }
     .graphic{
          img{
               top: 55px;
          }
     }
}

#first-panel, #second-panel{
     .text{
          height: 100%;
          text-align: center;
          padding-left: 0px;
          cursor: pointer;
          &:hover{
               opacity: 0.9;
          }
     }
     .graphic{
          background-color: $beige;
     }
}

#third-panel {
     height: 450px;
     .text-spacer{
          background-color: $grey;
     }
     .text{
          padding-top: 70px;
     }
}

.slick-slider{

     .slick-dots {
          position: relative;
          bottom: 95px;
          li button{
               &:before{
                    font-family: 'slick';
                    font-size: 20px;
                    line-height: 20px;
                    color: $light-grey;
                    opacity: 1;
               }
               &:hover{
                    &:before {
                         opacity: 0.75;
                         color: $grey;
                    }
               }
          }
     }

     .slick-dots li.slick-active button:before{
          color: $grey;
     }

     .slick-next{
          right: 60px;
     }

     .slick-prev{
          left: 10px;
     }

     .slick-slide {
          outline: none
     }

     .slick-next, .slick-prev{
          top: 50%;
          z-index: 100;
          &:before {
               font-size: 0px;
               top: 6px;
          }
     }

     .icon-right-open-big, .icon-left-open-big{
          &:before {
               font-size: 50px;
               color: $light-grey;
          }
          &:hover{
               &:before {
                    opacity: 0.75;
                    color: $grey;
               }
          }
     }
}