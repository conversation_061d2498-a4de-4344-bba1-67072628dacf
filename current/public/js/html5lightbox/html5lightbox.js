/** HTML5 LightBox - jQuery Image and Video LightBox Plugin
 * Copyright 2014 Magic Hills Pty Ltd All Rights Reserved
 * Website: http://html5box.com
 * Version 4.9 
 */
(function(){var scripts=document.getElementsByTagName("script");var jsFolder="";for(var i=0;i<scripts.length;i++)if(scripts[i].src&&scripts[i].src.match(/html5lightbox\.js/i))jsFolder=scripts[i].src.substr(0,scripts[i].src.lastIndexOf("/")+1);var loadjQuery=false;if(typeof jQuery=="undefined")loadjQuery=true;else{var jVersion=jQuery.fn.jquery.split(".");if(jVersion[0]<1||jVersion[0]==1&&jVersion[1]<6)loadjQuery=true}if(loadjQuery){var head=document.getElementsByTagName("head")[0];var script=document.createElement("script");
script.setAttribute("type","text/javascript");if(script.readyState)script.onreadystatechange=function(){if(script.readyState=="loaded"||script.readyState=="complete"){script.onreadystatechange=null;loadHtml5LightBox(jsFolder)}};else script.onload=function(){loadHtml5LightBox(jsFolder)};script.setAttribute("src",jsFolder+"jquery.js");head.appendChild(script)}else loadHtml5LightBox(jsFolder)})();
function loadHtml5LightBox(jsFolder){(function($){$.fn.html5lightbox=function(options){var inst=this;inst.options=jQuery.extend({freelink:"http://html5box.com/",defaultvideovolume:1,autoplay:true,html5player:true,responsive:true,nativehtml5controls:false,videohidecontrols:false,useflashonie9:true,useflashonie10:false,useflashonie11:false,googleanalyticsaccount:"",arrowloop:true,shownavigation:true,thumbwidth:96,thumbheight:72,thumbgap:4,thumbtopmargin:12,thumbbottommargin:12,thumbborder:1,thumbbordercolor:"transparent",
thumbhighlightbordercolor:"#fff",thumbopacity:1,navbuttonwidth:32,overlaybgcolor:"#000",overlayopacity:0.8,bgcolor:"#fff",bordersize:8,borderradius:0,bordermargin:16,barautoheight:true,barheight:48,loadingwidth:64,loadingheight:64,resizespeed:400,fadespeed:400,jsfolder:jsFolder,skinsfoldername:"skins/default/",loadingimage:"lightbox-loading.gif",nextimage:"lightbox-next.png",previmage:"lightbox-prev.png",closeimage:"lightbox-close.png",playvideoimage:"lightbox-playvideo.png",titlebgimage:"lightbox-titlebg.png",
navarrowsprevimage:"nav-arrows-prev.png",navarrowsnextimage:"nav-arrows-next.png",showtitle:true,titlestyle:"bottom",titleinsidecss:"{color:#fff; font-size:14px; font-family:Arial,Helvetica,sans-serif; overflow:hidden; text-align:left;}",titlebottomcss:"{color:#333; font-size:14px; font-family:Arial,Helvetica,sans-serif; overflow:hidden; text-align:left;}",showdescription:true,descriptioninsidecss:"{color:#fff; font-size:12px; font-family:Arial,Helvetica,sans-serif; overflow:hidden; text-align:left; margin:4px 0px 0px; padding: 0px;}",
descriptionbottomcss:"{color:#333; font-size:12px; font-family:Arial,Helvetica,sans-serif; overflow:hidden; text-align:left; margin:4px 0px 0px; padding: 0px;}",errorwidth:280,errorheight:48,errorcss:"{text-align:center; color:#ff0000; font-size:14px; font-family:Arial, sans-serif;}",supportesckey:true,supportarrowkeys:true,version:"3.3",stamp:false,freemark:"72,84,77,76,53,32,76,105,103,104,116,98,111,120,32,70,114,101,101,32,86,101,114,115,105,111,110",watermark:"",watermarklink:""},options);if(typeof html5lightbox_options!=
"undefined"&&html5lightbox_options)jQuery.extend(inst.options,html5lightbox_options);if($("div#html5lightbox_options").length)$.each($("div#html5lightbox_options").data(),function(key,value){inst.options[key.toLowerCase()]=value});inst.options.htmlfolder=window.location.href.substr(0,window.location.href.lastIndexOf("/")+1);inst.options.skinsfolder=inst.options.skinsfoldername;if(inst.options.skinsfolder.length>0&&inst.options.skinsfolder[inst.options.skinsfolder.length-1]!="/")inst.options.skinsfolder+=
"/";if(inst.options.skinsfolder.charAt(0)!="/"&&inst.options.skinsfolder.substring(0,5)!="http:"&&inst.options.skinsfolder.substring(0,6)!="https:")inst.options.skinsfolder=inst.options.jsfolder+inst.options.skinsfolder;var i;var l;var mark="";var bytes=inst.options.freemark.split(",");for(i=0;i<bytes.length;i++)mark+=String.fromCharCode(bytes[i]);inst.options.freemark=mark;var d0="hmtamgli5cboxh.iclolms";for(i=1;i<=5;i++)d0=d0.slice(0,i)+d0.slice(i+1);l=d0.length;for(i=0;i<5;i++)d0=d0.slice(0,l-
9+i)+d0.slice(l-8+i);if(inst.options.htmlfolder.indexOf(d0)!=-1)inst.options.stamp=false;inst.options.navheight=0;inst.options.thumbgap+=2*inst.options.thumbborder;inst.options.types=["IMAGE","FLASH","VIDEO","YOUTUBE","VIMEO","PDF","MP3","WEB","FLV","DAILYMOTION","DIV"];inst.elemArray=new Array;inst.options.curElem=-1;inst.options.flashInstalled=false;try{if(new ActiveXObject("ShockwaveFlash.ShockwaveFlash"))inst.options.flashInstalled=true}catch(e){if(navigator.mimeTypes["application/x-shockwave-flash"])inst.options.flashInstalled=
true}inst.options.html5VideoSupported=!!document.createElement("video").canPlayType;inst.options.isChrome=navigator.userAgent.match(/Chrome/i)!=null;inst.options.isFirefox=navigator.userAgent.match(/Firefox/i)!=null;inst.options.isOpera=navigator.userAgent.match(/Opera/i)!=null||navigator.userAgent.match(/OPR\//i)!=null;inst.options.isSafari=navigator.userAgent.match(/Safari/i)!=null;inst.options.isIE11=navigator.userAgent.match(/Trident\/7/)!=null&&navigator.userAgent.match(/rv:11/)!=null;inst.options.isIE=
navigator.userAgent.match(/MSIE/i)!=null&&!inst.options.isOpera;inst.options.isIE10=navigator.userAgent.match(/MSIE 10/i)!=null&&!this.options.isOpera;inst.options.isIE9=navigator.userAgent.match(/MSIE 9/i)!=null&&!inst.options.isOpera;inst.options.isIE8=navigator.userAgent.match(/MSIE 8/i)!=null&&!inst.options.isOpera;inst.options.isIE7=navigator.userAgent.match(/MSIE 7/i)!=null&&!inst.options.isOpera;inst.options.isIE6=navigator.userAgent.match(/MSIE 6/i)!=null&&!inst.options.isOpera;inst.options.isIE678=
inst.options.isIE6||inst.options.isIE7||inst.options.isIE8;inst.options.isIE6789=inst.options.isIE6||inst.options.isIE7||inst.options.isIE8||inst.options.isIE9;inst.options.isAndroid=navigator.userAgent.match(/Android/i)!=null;inst.options.isIPad=navigator.userAgent.match(/iPad/i)!=null;inst.options.isIPhone=navigator.userAgent.match(/iPod/i)!=null||navigator.userAgent.match(/iPhone/i)!=null;inst.options.isIOS=inst.options.isIPad||inst.options.isIPhone;inst.options.isMobile=inst.options.isAndroid||
inst.options.isIPad||inst.options.isIPhone;inst.options.isIOSLess5=inst.options.isIPad&&inst.options.isIPhone&&(navigator.userAgent.match(/OS 4/i)!=null||navigator.userAgent.match(/OS 3/i)!=null);inst.options.supportCSSPositionFixed=!inst.options.isIE6&&!inst.options.isIOSLess5;inst.options.iequirksmode=inst.options.isIE6789&&!jQuery.support.boxModel;inst.options.resizeTimeout=-1;if(inst.options.isMobile)inst.options.autoplay=false;if(inst.options.googleanalyticsaccount&&!window._gaq){window._gaq=
window._gaq||[];window._gaq.push(["_setAccount",inst.options.googleanalyticsaccount]);window._gaq.push(["_trackPageview"]);$.getScript(("https:"==document.location.protocol?"https://ssl":"http://www")+".google-analytics.com/ga.js")}inst.init=function(){inst.showing=false;inst.readData();inst.createMarkup();inst.supportKeyboard()};var ELEM_TYPE=0,ELEM_HREF=1,ELEM_TITLE=2,ELEM_GROUP=3,ELEM_WIDTH=4,ELEM_HEIGHT=5,ELEM_HREF_WEBM=6,ELEM_HREF_OGG=7,ELEM_THUMBNAIL=8,ELEM_DESCRIPTION=9,ELEM_DIV=10;inst.readData=
function(){inst.each(function(){if(this.nodeName.toLowerCase()!="a"&&this.nodeName.toLowerCase()!="area")return;var $this=$(this);var fileType="mediatype"in $this.data()?$this.data("mediatype"):inst.checkType($this.attr("href"));if(fileType<0)return;for(var i=0;i<inst.elemArray.length;i++)if($this.attr("href")==inst.elemArray[i][ELEM_HREF])return;inst.elemArray.push(new Array(fileType,$this.attr("href"),$this.attr("title"),$this.data("group"),$this.data("width"),$this.data("height"),$this.data("webm"),
$this.data("ogg"),$this.data("thumbnail"),$this.data("description")))})};inst.createMarkup=function(){if(inst.options.titlestyle=="inside"){inst.options.titlecss=inst.options.titleinsidecss;inst.options.descriptioncss=inst.options.descriptioninsidecss}else if(inst.options.titlestyle=="bottom"){inst.options.titlecss=inst.options.titlebottomcss;inst.options.descriptioncss=inst.options.descriptionbottomcss}var styleCss="#html5-text "+inst.options.titlecss;styleCss+=".html5-description "+inst.options.descriptioncss;
styleCss+=".html5-error "+inst.options.errorcss;$("head").append("<style type='text/css'>"+styleCss+"</style>");inst.$lightbox=jQuery("<div id='html5-lightbox' style='display:none;top:0px;left:0px;width:100%;height:100%;z-index:9999998;text-align:center;'>"+"<div id='html5-lightbox-overlay' style='display:block;position:absolute;top:0px;left:0px;width:100%;min-height:100%;background-color:"+inst.options.overlaybgcolor+";opacity:"+inst.options.overlayopacity+";filter:alpha(opacity="+Math.round(inst.options.overlayopacity*
100)+");'></div>"+"<div id='html5-lightbox-box' style='display:block;position:relative;margin:0px auto;'>"+"<div id='html5-elem-box' style='display:block;position:relative;margin:0px auto;text-align:center;overflow:hidden;box-sizing:content-box;-moz-box-sizing:content-box;-webkit-box-sizing:content-box;'>"+"<div id='html5-elem-wrap' style='display:block;position:relative;margin:0px auto;text-align:center;box-sizing:content-box;-moz-box-sizing:content-box;-webkit-box-sizing:content-box;background-color:"+
inst.options.bgcolor+";'>"+"<div id='html5-loading' style='display:none;position:absolute;top:0px;left:0px;text-align:center;width:100%;height:100%;background:url(\""+inst.options.skinsfolder+inst.options.loadingimage+"\") no-repeat center center;'></div>"+"<div id='html5-error' class='html5-error' style='display:none;position:absolute;padding:"+inst.options.bordersize+"px;text-align:center;width:"+inst.options.errorwidth+"px;height:"+inst.options.errorheight+"px;'>"+"The requested content cannot be loaded.<br />Please try again later."+
"</div>"+"<div id='html5-image' style='display:none;position:absolute;top:0px;left:0px;"+(inst.options.iequirksmode?"margin":"padding")+":"+inst.options.bordersize+"px;text-align:center;box-sizing:content-box;-moz-box-sizing:content-box;-webkit-box-sizing:content-box;'></div>"+"<div id='html5-next' style='display:none;cursor:pointer;position:absolute;right:"+inst.options.bordersize+"px;top:50%;margin-top:-16px;'><img src='"+inst.options.skinsfolder+inst.options.nextimage+"'></div>"+"<div id='html5-prev' style='display:none;cursor:pointer;position:absolute;left:"+
inst.options.bordersize+"px;top:50%;margin-top:-16px;'><img src='"+inst.options.skinsfolder+inst.options.previmage+"'></div>"+"</div>"+"</div>"+"<div id='html5-watermark' style='display:none;position:absolute;left:"+String(inst.options.bordersize+2)+"px;top:"+String(inst.options.bordersize+2)+"px;'></div>"+"</div>"+"</div>");inst.$lightbox.css({position:inst.options.supportCSSPositionFixed&&inst.options.responsive&&!inst.options.iequirksmode?"fixed":"absolute"});inst.$lightbox.appendTo("body");inst.$lightboxBox=
$("#html5-lightbox-box",inst.$lightbox);inst.$elem=$("#html5-elem-box",inst.$lightbox);inst.$elemWrap=$("#html5-elem-wrap",inst.$lightbox);inst.$loading=$("#html5-loading",inst.$lightbox);inst.$error=$("#html5-error",inst.$lightbox);inst.$image=$("#html5-image",inst.$lightbox);inst.$next=$("#html5-next",inst.$lightbox);inst.$prev=$("#html5-prev",inst.$lightbox);var elemText="<div id='html5-elem-data-box' style='display:none;'><div id='html5-text' style='display:block;overflow:hidden;'></div></div>";
inst.$elem.append(elemText);inst.$elemData=$("#html5-elem-data-box",inst.$lightbox);inst.$text=$("#html5-text",inst.$lightbox);if(inst.options.borderradius>0){inst.$elem.css({"border-radius":inst.options.borderradius+"px","-moz-border-radius":inst.options.borderradius+"px","-webkit-border-radius":inst.options.borderradius+"px"});if(inst.options.titlestyle=="inside")inst.$elemWrap.css({"border-radius":inst.options.borderradius+"px","-moz-border-radius":inst.options.borderradius+"px","-webkit-border-radius":inst.options.borderradius+
"px"});else{inst.$elemWrap.css({"border-top-left-radius":inst.options.borderradius+"px","-moz-top-left-border-radius":inst.options.borderradius+"px","-webkit-top-left-border-radius":inst.options.borderradius+"px","border-top-right-radius":inst.options.borderradius+"px","-moz-top-right-border-radius":inst.options.borderradius+"px","-webkit-top-right-border-radius":inst.options.borderradius+"px"});inst.$elemData.css({"border-bottom-left-radius":inst.options.borderradius+"px","-moz-top-bottom-border-radius":inst.options.borderradius+
"px","-webkit-bottom-left-border-radius":inst.options.borderradius+"px","border-bottom-right-radius":inst.options.borderradius+"px","-moz-bottom-right-border-radius":inst.options.borderradius+"px","-webkit-bottom-right-border-radius":inst.options.borderradius+"px"})}}if(inst.options.titlestyle=="inside"){inst.$elemData.css({position:"absolute",margin:inst.options.bordersize+"px",bottom:0,left:0,"background-color":"#333","background-color":"rgba(51, 51, 51, 0.6)"});inst.$text.css({padding:inst.options.bordersize+
"px "+2*inst.options.bordersize+"px"})}else{inst.$elemData.css({position:"relative",width:"100%",height:inst.options.barautoheight?"auto":inst.options.barheight+"px","padding":"0 0 "+inst.options.bordersize+"px"+" 0","background-color":inst.options.bgcolor,"text-align":"left"});inst.$text.css({"margin":"0 "+inst.options.bordersize+"px"})}inst.$lightboxBox.append("<div id='html5-close' style='display:none;cursor:pointer;position:absolute;top:0;right:0;margin-top:-16px;margin-right:-16px;'><img src='"+
inst.options.skinsfolder+inst.options.closeimage+"'></div>");inst.$close=$("#html5-close",inst.$lightbox);inst.$watermark=$("#html5-watermark",inst.$lightbox);if(inst.options.stamp)inst.$watermark.html("<a href='"+inst.options.freelink+"' style='text-decoration:none;' title='jQuery Lightbox'><div style='display:block;width:170px;height:20px;text-align:center;border-radius:3px;-moz-border-radius:3px;-webkit-border-radius:3px;background-color:#fff;color:#333;font:12px Arial,sans-serif;'><div style='line-height:20px;'>"+
inst.options.freemark+"</div></div></a>");else if(inst.options.watermark){var html="<img src='"+inst.options.watermark+"' style='border:none;' />";if(inst.options.watermarklink)html="<a href='"+inst.options.watermarklink+"' target='_blank'>"+html+"</a>";inst.$watermark.html(html)}$("#html5-lightbox-overlay",inst.$lightbox).click(inst.finish);inst.$close.click(inst.finish);inst.$next.click(function(){if(inst.options.nextElem<=inst.options.curElem)if(inst.options.onlastarrowclicked&&window[inst.options.onlastarrowclicked]&&
typeof window[inst.options.onlastarrowclicked]=="function")window[inst.options.onlastarrowclicked]();inst.gotoSlide(-1)});inst.$prev.click(function(){if(inst.options.prevElem>=inst.options.curElem)if(inst.options.onfirstarrowclicked&&window[inst.options.onfirstarrowclicked]&&typeof window[inst.options.onfirstarrowclicked]=="function")window[inst.options.onfirstarrowclicked]();inst.gotoSlide(-2)});$(window).resize(function(){if(!inst.options.isMobile){clearTimeout(inst.options.resizeTimeout);inst.options.resizeTimeout=
setTimeout(function(){inst.resizeWindow()},500)}});$(window).scroll(function(){inst.scrollBox()});$(window).bind("orientationchange",function(e){if(inst.options.isMobile)inst.resizeWindow()});if(inst.options.isIPhone){inst.options.windowInnerHeight=window.innerHeight;setInterval(function(){if(inst.options.windowInnerHeight!=window.innerHeight){inst.options.windowInnerHeight=window.innerHeight;inst.resizeWindow()}},500)}inst.enableSwipe()};inst.calcNextPrevElem=function(){inst.options.nextElem=-1;
inst.options.prevElem=-1;var j,curGroup=inst.elemArray[inst.options.curElem][ELEM_GROUP];if(curGroup!=undefined&&curGroup!=null){for(j=inst.options.curElem+1;j<inst.elemArray.length;j++)if(inst.elemArray[j][ELEM_GROUP]==curGroup){inst.options.nextElem=j;break}if(inst.options.nextElem<0)for(j=0;j<inst.options.curElem;j++)if(inst.elemArray[j][ELEM_GROUP]==curGroup){inst.options.nextElem=j;break}if(inst.options.nextElem>=0){for(j=inst.options.curElem-1;j>=0;j--)if(inst.elemArray[j][ELEM_GROUP]==curGroup){inst.options.prevElem=
j;break}if(inst.options.prevElem<0)for(j=inst.elemArray.length-1;j>inst.options.curElem;j--)if(inst.elemArray[j][ELEM_GROUP]==curGroup){inst.options.prevElem=j;break}}}};inst.clickHandler=function(){if(inst.elemArray.length<=0)return true;var $this=$(this);inst.hideObjects();for(var i=0;i<inst.elemArray.length;i++)if(inst.elemArray[i][ELEM_HREF]==$this.attr("href"))break;if(i==inst.elemArray.length)return true;inst.options.curElem=i;inst.options.nextElem=-1;inst.options.prevElem=-1;inst.calcNextPrevElem();
inst.$next.hide();inst.$prev.hide();inst.reset();inst.$lightbox.show();if(!inst.options.supportCSSPositionFixed)inst.$lightbox.css("top",$(window).scrollTop());var boxW=inst.options.loadingwidth+2*inst.options.bordersize;var boxH=inst.options.loadingheight+2*inst.options.bordersize;var winH=window.innerHeight?window.innerHeight:$(window).height();var boxT=Math.round(winH/2-boxH/2);if(inst.options.titlestyle!="inside")boxT-=Math.round(inst.options.barheight/2);if(boxT<16)boxT=16;if(inst.options.iequirksmode)inst.$lightboxBox.css({"top":boxT});
else inst.$lightboxBox.css({"margin-top":boxT});inst.$lightboxBox.css({"width":boxW,"height":boxH});inst.$elemWrap.css({"width":boxW,"height":boxH});inst.loadCurElem();return false};inst.loadThumbnail=function(src,index){var imgLoader=new Image;$(imgLoader).load(function(){var style;if(this.width/this.height<=inst.options.thumbwidth/inst.options.thumbheight)style="width:100%;";else style="height:100%;";$(".html5-nav-thumb").eq(index).html("<img style='"+style+"' src='"+src+"' />")});imgLoader.src=
src};inst.showNavigation=function(){if(!inst.options.shownavigation)return;if(!inst.currentElem||!inst.currentElem[ELEM_GROUP])return;var i;var showNav=false;var group=inst.currentElem[ELEM_GROUP];for(i=0;i<inst.elemArray.length;i++)if(group==inst.elemArray[i][ELEM_GROUP])if(inst.elemArray[i][ELEM_THUMBNAIL]&&inst.elemArray[i][ELEM_THUMBNAIL].length>0){showNav=true;break}if(!showNav)return;inst.options.navheight=inst.options.thumbheight+inst.options.thumbtopmargin+inst.options.thumbbottommargin;if($(".html5-nav").length>
0)return;$("body").append("<div class='html5-nav' style='display:block;position:fixed;bottom:0;left:0;width:100%;height:"+inst.options.navheight+"px;z-index:9999999;'>"+"<div class='html5-nav-container' style='position:relative;margin:"+inst.options.thumbtopmargin+"px auto "+inst.options.thumbbottommargin+"px;'>"+"<div class='html5-nav-prev' style='display:block;position:absolute;cursor:pointer;width:"+inst.options.navbuttonwidth+'px;height:100%;left:0;top:0;background:url("'+inst.options.skinsfolder+
inst.options.navarrowsprevimage+"\") no-repeat left center;'></div>"+"<div class='html5-nav-mask' style='display:block;position:relative;margin:0 auto;overflow:hidden;'>"+"<div class='html5-nav-list'></div>"+"</div>"+"<div class='html5-nav-next' style='display:block;position:absolute;cursor:pointer;width:"+inst.options.navbuttonwidth+'px;height:100%;right:0;top:0;background:url("'+inst.options.skinsfolder+inst.options.navarrowsnextimage+"\") no-repeat right center;'></div>"+"</div>"+"</div>");var index=
0;for(i=0;i<inst.elemArray.length;i++)if(group==inst.elemArray[i][ELEM_GROUP])if(inst.elemArray[i][ELEM_THUMBNAIL]&&inst.elemArray[i][ELEM_THUMBNAIL].length>0){$(".html5-nav-list").append("<div class='html5-nav-thumb' data-arrayindex='"+i+"' style='float:left;overflow:hidden;cursor:pointer;opacity:"+inst.options.thumbopacity+";margin: 0 "+inst.options.thumbgap/2+"px;width:"+inst.options.thumbwidth+"px;height:"+inst.options.thumbheight+"px;border:"+inst.options.thumbborder+"px solid "+inst.options.thumbbordercolor+
";'></div>");this.loadThumbnail(inst.elemArray[i][ELEM_THUMBNAIL],index);index++}$(".html5-nav-thumb").hover(function(){$(this).css({opacity:1});$(this).css({border:inst.options.thumbborder+"px solid "+inst.options.thumbhighlightbordercolor})},function(){$(this).css({opacity:inst.options.thumbopacity});$(this).css({border:inst.options.thumbborder+"px solid "+inst.options.thumbbordercolor})});$(".html5-nav-thumb").click(function(){var index=$(this).data("arrayindex");if(index>=0)inst.gotoSlide(index)});
inst.options.totalwidth=index*(inst.options.thumbgap+inst.options.thumbwidth+2*inst.options.thumbborder);$(".html5-nav-list").css({display:"block",position:"relative","margin-left":0,width:inst.options.totalwidth+"px"}).append("<div style='clear:both;'></div>");var $navMask=$(".html5-nav-mask");var $navPrev=$(".html5-nav-prev");var $navNext=$(".html5-nav-next");$navPrev.click(function(){var $navList=$(".html5-nav-list");var $navNext=$(".html5-nav-next");var winWidth=inst.options.isMobile?Math.max($(window).width(),
$(document).width()):$(window).width();var maskWidth=winWidth-2*inst.options.navbuttonwidth;var marginLeft=parseInt($navList.css("margin-left"))+maskWidth;if(marginLeft>=0){marginLeft=0;$(this).css({"background-position":"center left"})}else $(this).css({"background-position":"center right"});if(marginLeft<=maskWidth-inst.options.totalwidth)$navNext.css({"background-position":"center left"});else $navNext.css({"background-position":"center right"});$navList.animate({"margin-left":marginLeft})});$navNext.click(function(){var $navList=
$(".html5-nav-list");var $navPrev=$(".html5-nav-prev");var winWidth=inst.options.isMobile?Math.max($(window).width(),$(document).width()):$(window).width();var maskWidth=winWidth-2*inst.options.navbuttonwidth;var marginLeft=parseInt($navList.css("margin-left"))-maskWidth;if(marginLeft<=maskWidth-inst.options.totalwidth){marginLeft=maskWidth-inst.options.totalwidth;$(this).css({"background-position":"center left"})}else $(this).css({"background-position":"center right"});if(marginLeft>=0)$navPrev.css({"background-position":"center left"});
else $navPrev.css({"background-position":"center right"});$navList.animate({"margin-left":marginLeft})});var winWidth=inst.options.isMobile?Math.max($(window).width(),$(document).width()):$(window).width();if(inst.options.totalwidth<=winWidth){$navMask.css({width:inst.options.totalwidth+"px"});$navPrev.hide();$navNext.hide()}else{$navMask.css({width:winWidth-2*inst.options.navbuttonwidth+"px"});$navPrev.show();$navNext.show()}};inst.loadElem=function(elem){inst.currentElem=elem;inst.showing=true;
inst.showNavigation();inst.$elem.unbind("mouseenter").unbind("mouseleave").unbind("mousemove");inst.$loading.show();if(inst.options.onshowitem&&window[inst.options.onshowitem]&&typeof window[inst.options.onshowitem]=="function")window[inst.options.onshowitem](elem);switch(elem[ELEM_TYPE]){case 0:var imgLoader=new Image;$(imgLoader).load(function(){inst.showImage(elem,imgLoader.width,imgLoader.height)});$(imgLoader).error(function(){inst.showError()});imgLoader.src=elem[ELEM_HREF];break;case 1:inst.showSWF(elem);
break;case 2:case 8:inst.showVideo(elem);break;case 3:case 4:case 9:inst.showYoutubeVimeo(elem);break;case 5:inst.showPDF(elem);break;case 6:inst.showMP3(elem);break;case 7:inst.showWeb(elem);break;case 10:inst.showDiv(elem);break}if(inst.options.googleanalyticsaccount&&window._gaq)window._gaq.push(["_trackEvent","Lightbox","Open",elem[ELEM_HREF]])};inst.loadCurElem=function(){inst.loadElem(inst.elemArray[inst.options.curElem])};inst.showError=function(){inst.$loading.hide();inst.resizeLightbox(inst.options.errorwidth,
inst.options.errorheight,true,function(){inst.$error.show();inst.$elem.fadeIn(inst.options.fadespeed,function(){inst.showData()})})};inst.calcTextWidth=function(objW){return objW-36};inst.showTitle=function(w,t,description){if(inst.options.titlestyle=="inside")inst.$elemData.css({width:w+"px"});var text="";if(inst.options.showtitle&&t&&t.length>0)text+=t;if(inst.options.showdescription&&description&&description.length>0)text+='<p class="html5-description">'+description+"</p>";inst.$text.html(text)},
inst.showImage=function(elem,imgW,imgH){var elemW,elemH;if(elem[ELEM_WIDTH])elemW=elem[ELEM_WIDTH];else{elemW=imgW;elem[ELEM_WIDTH]=imgW}if(elem[ELEM_HEIGHT])elemH=elem[ELEM_HEIGHT];else{elemH=imgH;elem[ELEM_HEIGHT]=imgH}var sizeObj=inst.calcElemSize({w:elemW,h:elemH});inst.resizeLightbox(sizeObj.w,sizeObj.h,true,function(){inst.showTitle(sizeObj.w,elem[ELEM_TITLE],elem[ELEM_DESCRIPTION]);inst.$image.css({width:sizeObj.w,height:sizeObj.h}).show();inst.$image.html("<img src='"+elem[ELEM_HREF]+"' width='100%' height='100%' />");
inst.$elem.fadeIn(inst.options.fadespeed,function(){inst.showData()})})};inst.showSWF=function(elem){var dataW=elem[ELEM_WIDTH]?elem[ELEM_WIDTH]:960;var dataH=elem[ELEM_HEIGHT]?elem[ELEM_HEIGHT]:540;var sizeObj=inst.calcElemSize({w:dataW,h:dataH});dataW=sizeObj.w;dataH=sizeObj.h;inst.resizeLightbox(dataW,dataH,true,function(){inst.showTitle(sizeObj.w,elem[ELEM_TITLE],elem[ELEM_DESCRIPTION]);inst.$image.css({width:sizeObj.w,height:sizeObj.h}).html("<div id='html5lightbox-swf' style='display:block;width:100%;height:100%;'></div>").show();
inst.embedFlash($("#html5lightbox-swf"),elem[ELEM_HREF],"window",{width:dataW,height:dataH});inst.$elem.show();inst.showData()})};inst.showVideo=function(elem){var dataW=elem[ELEM_WIDTH]?elem[ELEM_WIDTH]:960;var dataH=elem[ELEM_HEIGHT]?elem[ELEM_HEIGHT]:540;var sizeObj=inst.calcElemSize({w:dataW,h:dataH});dataW=sizeObj.w;dataH=sizeObj.h;inst.resizeLightbox(dataW,dataH,true,function(){inst.showTitle(sizeObj.w,elem[ELEM_TITLE],elem[ELEM_DESCRIPTION]);inst.$image.css({width:sizeObj.w,height:sizeObj.h}).html("<div id='html5lightbox-video' style='display:block;width:100%;height:100%;background-color:#000;'></div>").show();
var isHTML5=false;if(inst.options.isIE678||elem[ELEM_TYPE]==8||inst.options.isIE9&&inst.options.useflashonie9||inst.options.isIE10&&inst.options.useflashonie10||inst.options.isIE11&&inst.options.useflashonie11)isHTML5=false;else if(inst.options.isMobile)isHTML5=true;else if((inst.options.html5player||!inst.options.flashInstalled)&&inst.options.html5VideoSupported)if(!inst.options.isFirefox&&!inst.options.isOpera||(inst.options.isFirefox||inst.options.isOpera)&&(elem[ELEM_HREF_OGG]||elem[ELEM_HREF_WEBM]))isHTML5=
true;if(isHTML5){var videoSrc=elem[ELEM_HREF];if(inst.options.isFirefox||inst.options.isOpera||!videoSrc)videoSrc=elem[ELEM_HREF_WEBM]?elem[ELEM_HREF_WEBM]:elem[ELEM_HREF_OGG];inst.embedHTML5Video($("#html5lightbox-video"),videoSrc,inst.options.autoplay)}else{var videoFile=elem[ELEM_HREF];if(videoFile.charAt(0)!="/"&&videoFile.substring(0,5)!="http:"&&videoFile.substring(0,6)!="https:")videoFile=inst.options.htmlfolder+videoFile;inst.embedFlash($("#html5lightbox-video"),inst.options.jsfolder+"html5boxplayer.swf",
"transparent",{width:dataW,height:dataH,hidecontrols:inst.options.videohidecontrols?"1":"0",hideplaybutton:"0",videofile:videoFile,hdfile:"",ishd:"0",defaultvolume:inst.options.defaultvideovolume,autoplay:inst.options.autoplay?"1":"0",errorcss:".html5box-error"+inst.options.errorcss,id:0})}inst.$elem.show();inst.showData()})};inst.getYoutubeParams=function(href){var result={};if(href.indexOf("?")<0)return result;var params=href.substring(href.indexOf("?")+1).split("&");for(var i=0;i<params.length;i++){var value=
params[i].split("=");if(value&&value.length==2&&value[0].toLowerCase()!="v")result[value[0].toLowerCase()]=value[1]}return result};inst.prepareYoutubeHref=function(href){var youtubeId="";var regExp=/^.*((youtu.be\/)|(v\/)|(\/u\/\w\/)|(embed\/)|(watch\??v?=?))([^#\&\?]*).*/;var match=href.match(regExp);if(match&&match[7]&&match[7].length==11)youtubeId=match[7];var protocol=window.location.protocol=="https:"?"https:":"http:";var result=protocol+"//www.youtube.com/embed/"+youtubeId;var params=this.getYoutubeParams(href);
var first=true;for(var key in params){if(first){result+="?";first=false}else result+="&";result+=key+"="+params[key]}return result};inst.prepareDailymotionHref=function(href){if(href.match(/\:\/\/.*(dai\.ly)/i)){var protocol=window.location.protocol=="https:"?"https:":"http:";var id=href.match(/(dai\.ly\/)([a-zA-Z0-9\-\_]+)/)[2];href=protocol+"//www.dailymotion.com/embed/video/"+id}return href};inst.showYoutubeVimeo=function(elem){var dataW=elem[ELEM_WIDTH]?elem[ELEM_WIDTH]:960;var dataH=elem[ELEM_HEIGHT]?
elem[ELEM_HEIGHT]:540;var sizeObj=inst.calcElemSize({w:dataW,h:dataH});dataW=sizeObj.w;dataH=sizeObj.h;inst.resizeLightbox(dataW,dataH,true,function(){inst.showTitle(sizeObj.w,elem[ELEM_TITLE],elem[ELEM_DESCRIPTION]);inst.$image.css({width:sizeObj.w,height:sizeObj.h}).html("<div id='html5lightbox-video' style='display:block;width:100%;height:100%;'></div>").show();var href=elem[ELEM_HREF];if(elem[ELEM_TYPE]==3)href=inst.prepareYoutubeHref(href);if(elem[ELEM_TYPE]==9)href=inst.prepareDailymotionHref(href);
if(inst.options.autoplay)if(href.indexOf("?")<0)href+="?autoplay=1";else href+="&autoplay=1";if(elem[ELEM_TYPE]==3){if(href.indexOf("?")<0)href+="?wmode=transparent&rel=0";else href+="&wmode=transparent&rel=0";if(inst.options.videohidecontrols)href+="&controls=0&showinfo=0"}$("#html5lightbox-video").html("<iframe width='100%' height='100%' src='"+href+"' frameborder='0' webkitAllowFullScreen mozallowfullscreen allowFullScreen></iframe>");inst.$elem.show();inst.showData()})};inst.showPDF=function(elem){};
inst.showMP3=function(elem){};inst.showDiv=function(elem){var winWidth=inst.options.isMobile?Math.max($(window).width(),$(document).width()):$(window).width();var winH=window.innerHeight?window.innerHeight:$(window).height();var dataW=elem[ELEM_WIDTH]?elem[ELEM_WIDTH]:winWidth;var dataH=elem[ELEM_HEIGHT]?elem[ELEM_HEIGHT]:winH-inst.options.navheight;var sizeObj=inst.calcElemSize({w:dataW,h:dataH});dataW=sizeObj.w;dataH=sizeObj.h;inst.resizeLightbox(dataW,dataH,true,function(){inst.$loading.hide();
inst.showTitle(sizeObj.w,elem[ELEM_TITLE],elem[ELEM_DESCRIPTION]);inst.$image.css({width:sizeObj.w,height:sizeObj.h}).html("<div id='html5lightbox-div' style='display:block;width:100%;height:100%;'></div>").show();var divID=elem[ELEM_HREF];if($(divID).length>0)$("#html5lightbox-div").html($(divID).html());else $("#html5lightbox-div").html("<div class='html5-error'>The specified div ID does not exist.</div>");inst.$elem.show();inst.showData()})};inst.showWeb=function(elem){var winWidth=inst.options.isMobile?
Math.max($(window).width(),$(document).width()):$(window).width();var winH=window.innerHeight?window.innerHeight:$(window).height();var dataW=elem[ELEM_WIDTH]?elem[ELEM_WIDTH]:winWidth;var dataH=elem[ELEM_HEIGHT]?elem[ELEM_HEIGHT]:winH-inst.options.navheight;var sizeObj=inst.calcElemSize({w:dataW,h:dataH});dataW=sizeObj.w;dataH=sizeObj.h;inst.resizeLightbox(dataW,dataH,true,function(){inst.$loading.hide();inst.showTitle(sizeObj.w,elem[ELEM_TITLE],elem[ELEM_DESCRIPTION]);inst.$image.css({width:sizeObj.w,
height:sizeObj.h}).html("<div id='html5lightbox-web' style='display:block;width:100%;height:100%;'></div>").show();$("#html5lightbox-web").html("<iframe width='100%' height='100%' src='"+elem[ELEM_HREF]+"' frameborder='0'></iframe>");inst.$elem.show();inst.showData()})};inst.scrollBox=function(){if(!inst.options.supportCSSPositionFixed)inst.$lightbox.css("top",$(window).scrollTop())};inst.resizeWindow=function(){if(!inst.currentElem)return;if(!inst.options.responsive)return;var elemW=inst.currentElem[ELEM_WIDTH]?
inst.currentElem[ELEM_WIDTH]:960;var elemH=inst.currentElem[ELEM_HEIGHT]?inst.currentElem[ELEM_HEIGHT]:540;var sizeObj=inst.calcElemSize({w:elemW,h:elemH});var winH=window.innerHeight?window.innerHeight:$(window).height();var boxW=sizeObj.w+2*inst.options.bordersize;var boxH=sizeObj.h+2*inst.options.bordersize;var boxT=Math.round((winH-inst.options.navheight)/2-boxH/2);if(inst.options.titlestyle!="inside")boxT-=Math.round(inst.options.barheight/2);if(boxT<16)boxT=16;inst.$lightboxBox.css({"margin-top":boxT});
inst.$lightboxBox.css({"width":boxW,"height":boxH});inst.$elemWrap.css({width:boxW,height:boxH});inst.$image.css({width:sizeObj.w,height:sizeObj.h});if($(".html5-nav").length<=0)return;$(".html5-nav-list").css({"margin-left":0});var $navMask=$(".html5-nav-mask");var $navPrev=$(".html5-nav-prev");var $navNext=$(".html5-nav-next");var winWidth=inst.options.isMobile?Math.max($(window).width(),$(document).width()):$(window).width();if(inst.options.totalwidth<=winWidth){$navMask.css({width:inst.options.totalwidth+
"px"});$navPrev.hide();$navNext.hide()}else{$navMask.css({width:winWidth-2*inst.options.navbuttonwidth+"px"});$navPrev.show();$navNext.show()}};inst.calcElemSize=function(sizeObj){if(!inst.options.responsive)return sizeObj;var winH=window.innerHeight?window.innerHeight:$(window).height();winH=winH?winH:$(document).height();var h0=winH-inst.options.navheight-2*inst.options.bordersize-2*inst.options.bordermargin;if(inst.options.titlestyle!="inside")h0-=inst.options.barheight;if(sizeObj.h>h0){sizeObj.w=
Math.round(sizeObj.w*h0/sizeObj.h);sizeObj.h=h0}var winWidth=inst.options.isMobile?Math.max($(window).width(),$(document).width()):$(window).width();winWidth=winWidth?winWidth:$(document).width();var w0=winWidth-2*inst.options.bordersize-2*inst.options.bordermargin;if(sizeObj.w>w0){sizeObj.h=Math.round(sizeObj.h*w0/sizeObj.w);sizeObj.w=w0}return sizeObj};inst.showData=function(){if(inst.$text.text().length>0)inst.$elemData.show();if(inst.$text.text().length>0&&inst.options.titlestyle!="inside")inst.$lightboxBox.css({height:String(inst.$lightboxBox.height()+
inst.options.barheight)+"px"});$("#html5-lightbox-overlay",inst.$lightbox).css({height:Math.max($(window).height(),$(document).height())})};inst.resizeLightbox=function(elemW,elemH,bAnimate,onFinish){var winH=window.innerHeight?window.innerHeight:$(window).height();var speed=bAnimate?inst.options.resizespeed:0;var boxW=elemW+2*inst.options.bordersize;var boxH=elemH+2*inst.options.bordersize;var boxT=Math.round((winH-inst.options.navheight)/2-boxH/2);if(inst.options.titlestyle!="inside")boxT-=Math.round(inst.options.barheight/
2);if(boxT<16)boxT=16;if(boxW==inst.$elemWrap.width()&&boxH==inst.$elemWrap.height())speed=0;inst.$loading.hide();inst.$watermark.hide();if(!inst.options.arrowloop){if(inst.options.prevElem>=inst.options.curElem)inst.$prev.hide();if(inst.options.nextElem<=inst.options.curElem)inst.$next.hide()}if(inst.options.nextElem<=inst.options.curElem)if(inst.options.onlastitem&&window[inst.options.onlastitem]&&typeof window[inst.options.onlastitem]=="function")window[inst.options.onlastitem](inst.currentElem);
if(inst.options.prevElem>=inst.options.curElem)if(inst.options.onfirstitem&&window[inst.options.onfirstitem]&&typeof window[inst.options.onfirstitem]=="function")window[inst.options.onfirstitem](inst.currentElem);inst.$elem.bind("mouseenter mousemove",function(){if(inst.options.arrowloop&&inst.options.prevElem>=0||!inst.options.arrowloop&&inst.options.prevElem>=0&&inst.options.prevElem<inst.options.curElem)inst.$prev.fadeIn();if(inst.options.arrowloop&&inst.options.nextElem>=0||!inst.options.arrowloop&&
inst.options.nextElem>=0&&inst.options.nextElem>inst.options.curElem)inst.$next.fadeIn()});inst.$elem.bind("mouseleave",function(){inst.$next.fadeOut();inst.$prev.fadeOut()});inst.$lightboxBox.css({"margin-top":boxT});inst.$lightboxBox.css({"width":boxW,"height":boxH});inst.$elemWrap.animate({width:boxW},speed).animate({height:boxH},speed,function(){inst.$loading.show();inst.$watermark.show();inst.$close.show();inst.$elem.css({"background-color":inst.options.bgcolor});onFinish()})};inst.reset=function(){if(inst.options.stamp)inst.$watermark.hide();
inst.showing=false;inst.$image.empty();inst.$text.empty();inst.$error.hide();inst.$loading.hide();inst.$image.hide();inst.$elemData.hide();inst.$close.hide();inst.$elem.css({"background-color":""})};inst.resetNavigation=function(){inst.options.navheight=0;$(".html5-nav").remove()};inst.finish=function(){inst.reset();inst.resetNavigation();inst.$lightbox.hide();inst.showObjects();if(inst.options.oncloselightbox&&window[inst.options.oncloselightbox]&&typeof window[inst.options.oncloselightbox]=="function")window[inst.options.oncloselightbox](inst.currentElem)};
inst.pauseSlide=function(){};inst.playSlide=function(){};inst.gotoSlide=function(slide){if(slide==-1){if(inst.options.nextElem<0)return;inst.options.curElem=inst.options.nextElem}else if(slide==-2){if(inst.options.prevElem<0)return;inst.options.curElem=inst.options.prevElem}else if(slide>=0)inst.options.curElem=slide;inst.calcNextPrevElem();inst.reset();inst.loadCurElem()};inst.supportKeyboard=function(){$(document).keyup(function(e){if(!inst.showing)return;if(inst.options.supportesckey&&e.keyCode==
27)inst.finish();else if(inst.options.supportarrowkeys)if(e.keyCode==39)inst.gotoSlide(-1);else if(e.keyCode==37)inst.gotoSlide(-2)})};inst.enableSwipe=function(){inst.$elem.touchSwipe({preventWebBrowser:true,swipeLeft:function(){inst.gotoSlide(-1)},swipeRight:function(){inst.gotoSlide(-2)}})};inst.hideObjects=function(){$("select, embed, object").css({"visibility":"hidden"})};inst.showObjects=function(){$("select, embed, object").css({"visibility":"visible"})};inst.embedHTML5Video=function($container,
src,autoplay){$container.html("<div style='display:block;width:100%;height:100%;position:relative;'><video id='html5-lightbox-video' width='100%' height='100%'"+(autoplay?" autoplay":"")+(inst.options.nativehtml5controls&&!inst.options.videohidecontrols?" controls='controls'":"")+" src='"+src+"'></div>");if(!inst.options.nativehtml5controls){$("video",$container).data("src",src);$("video",$container).lightboxHTML5VideoControls(inst.options.skinsfolder,inst,inst.options.videohidecontrols,false,inst.options.defaultvideovolume)}};
inst.embedFlash=function($container,src,wmode,flashVars){if(inst.options.flashInstalled){var htmlOptions={pluginspage:"http://www.adobe.com/go/getflashplayer",quality:"high",allowFullScreen:"true",allowScriptAccess:"always",type:"application/x-shockwave-flash"};htmlOptions.width="100%";htmlOptions.height="100%";htmlOptions.src=src;htmlOptions.flashVars=$.param(flashVars);htmlOptions.wmode=wmode;var htmlString="";for(var key in htmlOptions)htmlString+=key+"="+htmlOptions[key]+" ";$container.html("<embed "+
htmlString+"/>")}else $container.html("<div class='html5lightbox-flash-error' style='display:block; position:relative;text-align:center; width:100%; left:0px; top:40%;'><div class='html5-error'><div>The required Adobe Flash Player plugin is not installed</div><br /><div style='display:block;position:relative;text-align:center;width:112px;height:33px;margin:0px auto;'><a href='http://www.adobe.com/go/getflashplayer'><img src='http://www.adobe.com/images/shared/download_buttons/get_flash_player.gif' alt='Get Adobe Flash player' width='112' height='33'></img></a></div></div>")};
inst.checkType=function(href){if(!href)return-1;if(href.match(/\.(jpg|gif|png|bmp|jpeg)(.*)?$/i))return 0;if(href.match(/[^\.]\.(swf)\s*$/i))return 1;if(href.match(/\.(mp4|m4v|ogv|ogg|webm)(.*)?$/i))return 2;if(href.match(/\:\/\/.*(youtube\.com)/i)||href.match(/\:\/\/.*(youtu\.be)/i))return 3;if(href.match(/\:\/\/.*(vimeo\.com)/i))return 4;if(href.match(/\:\/\/.*(dailymotion\.com)/i)||href.match(/\:\/\/.*(dai\.ly)/i))return 9;if(href.match(/[^\.]\.(pdf)\s*$/i))return 5;if(href.match(/[^\.]\.(mp3)\s*$/i))return 6;
if(href.match(/[^\.]\.(flv)\s*$/i))return 8;if(href.match(/\#\w+/i))return 10;return 7};inst.showLightbox=function(type,href,title,width,height,webm,ogg,thumbnail,description){inst.$next.hide();inst.$prev.hide();inst.reset();inst.$lightbox.show();if(!inst.options.supportCSSPositionFixed)inst.$lightbox.css("top",$(window).scrollTop());var winH=window.innerHeight?window.innerHeight:$(window).height();var boxW=inst.options.loadingwidth+2*inst.options.bordersize;var boxH=inst.options.loadingheight+2*
inst.options.bordersize;var boxT=Math.round(winH/2-boxH/2);if(inst.options.titlestyle!="inside")boxT-=Math.round(inst.options.barheight/2);if(boxT<16)boxT=16;inst.$lightboxBox.css({"margin-top":boxT,"width":boxW,"height":boxH});inst.$elemWrap.css({"width":boxW,"height":boxH});inst.loadElem(new Array(type,href,title,null,width,height,webm,ogg,thumbnail,description))};inst.addItem=function(href,title,group,width,height,webm,ogg,thumbnail,description){type=inst.checkType(href);inst.elemArray.push(new Array(type,
href,title,group,width,height,webm,ogg,thumbnail,description))};inst.showItem=function(href){if(inst.elemArray.length<=0)return true;inst.hideObjects();for(var i=0;i<inst.elemArray.length;i++)if(inst.elemArray[i][ELEM_HREF]==href)break;if(i==inst.elemArray.length)return true;inst.options.curElem=i;inst.options.nextElem=-1;inst.options.prevElem=-1;inst.calcNextPrevElem();inst.$next.hide();inst.$prev.hide();inst.reset();inst.$lightbox.show();if(!inst.options.supportCSSPositionFixed)inst.$lightbox.css("top",
$(window).scrollTop());var winH=window.innerHeight?window.innerHeight:$(window).height();var boxW=inst.options.loadingwidth+2*inst.options.bordersize;var boxH=inst.options.loadingheight+2*inst.options.bordersize;var boxT=Math.round(winH/2-boxH/2);if(inst.options.titlestyle!="inside")boxT-=Math.round(inst.options.barheight/2);if(boxT<16)boxT=16;inst.$lightboxBox.css({"margin-top":boxT,"width":boxW,"height":boxH});inst.$elemWrap.css({"width":boxW,"height":boxH});inst.loadCurElem();return false};inst.init();
return inst.unbind("click").click(inst.clickHandler)}})(jQuery);(function($){$.fn.touchSwipe=function(options){var defaults={preventWebBrowser:false,swipeLeft:null,swipeRight:null,swipeTop:null,swipeBottom:null};if(options)$.extend(defaults,options);return this.each(function(){var startX=-1,startY=-1;var curX=-1,curY=-1;function touchStart(event){var e=event.originalEvent;if(e.targetTouches.length>=1){startX=e.targetTouches[0].pageX;startY=e.targetTouches[0].pageY}else touchCancel(event)}function touchMove(event){if(defaults.preventWebBrowser)event.preventDefault();
var e=event.originalEvent;if(e.targetTouches.length>=1){curX=e.targetTouches[0].pageX;curY=e.targetTouches[0].pageY}else touchCancel(event)}function touchEnd(event){if(curX>0||curY>0){triggerHandler();touchCancel(event)}else touchCancel(event)}function touchCancel(event){startX=-1;startY=-1;curX=-1;curY=-1}function triggerHandler(){if(curX>startX){if(defaults.swipeRight)defaults.swipeRight.call()}else if(defaults.swipeLeft)defaults.swipeLeft.call();if(curY>startY){if(defaults.swipeBottom)defaults.swipeBottom.call()}else if(defaults.swipeTop)defaults.swipeTop.call()}
try{$(this).bind("touchstart",touchStart);$(this).bind("touchmove",touchMove);$(this).bind("touchend",touchEnd);$(this).bind("touchcancel",touchCancel)}catch(e){}})}})(jQuery);(function($){$.fn.lightboxHTML5VideoControls=function(skinFolder,parentInst,hidecontrols,hideplaybutton,defaultvolume){var isTouch="ontouchstart"in window;var eStart=isTouch?"touchstart":"mousedown";var eMove=isTouch?"touchmove":"mousemove";var eCancel=isTouch?"touchcancel":"mouseup";var eClick=isTouch?"touchstart":"click";
var BUTTON_SIZE=32;var BAR_HEIGHT=isTouch?48:36;var hideControlsTimerId=null;var hideVolumeBarTimeoutId=null;var sliderDragging=false;var isFullscreen=false;var userActive=true;var isIPhone=navigator.userAgent.match(/iPod/i)!=null||navigator.userAgent.match(/iPhone/i)!=null;var isHd=$(this).data("ishd");var hd=$(this).data("hd");var src=$(this).data("src");var $videoObj=$(this);$videoObj.get(0).removeAttribute("controls");if(isIPhone){var h=$videoObj.height()-BAR_HEIGHT;$videoObj.css({height:h})}var $videoPlay=
$("<div class='html5boxVideoPlay'></div>");if(!isIPhone){$videoObj.after($videoPlay);$videoPlay.css({position:"absolute",top:"50%",left:"50%",display:"block",cursor:"pointer",width:64,height:64,"margin-left":-32,"margin-top":-32,"background-image":"url('"+skinFolder+"html5boxplayer_playvideo.png"+"')","background-position":"center center","background-repeat":"no-repeat"}).bind(eClick,function(){$videoObj.get(0).play()})}var $videoFullscreenBg=$("<div class='html5boxVideoFullscreenBg'></div>");var $videoControls=
$("<div class='html5boxVideoControls'>"+"<div class='html5boxVideoControlsBg'></div>"+"<div class='html5boxPlayPause'>"+"<div class='html5boxPlay'></div>"+"<div class='html5boxPause'></div>"+"</div>"+"<div class='html5boxTimeCurrent'>--:--</div>"+"<div class='html5boxFullscreen'></div>"+"<div class='html5boxHD'></div>"+"<div class='html5boxVolume'>"+"<div class='html5boxVolumeButton'></div>"+"<div class='html5boxVolumeBar'>"+"<div class='html5boxVolumeBarBg'>"+"<div class='html5boxVolumeBarActive'></div>"+
"</div>"+"</div>"+"</div>"+"<div class='html5boxTimeTotal'>--:--</div>"+"<div class='html5boxSeeker'>"+"<div class='html5boxSeekerBuffer'></div>"+"<div class='html5boxSeekerPlay'></div>"+"<div class='html5boxSeekerHandler'></div>"+"</div>"+"<div style='clear:both;'></div>"+"</div>");$videoObj.after($videoControls);$videoObj.after($videoFullscreenBg);$videoFullscreenBg.css({display:"none",position:"fixed",left:0,top:0,bottom:0,right:0,"z-index":2147483647});$videoControls.css({display:"block",position:"absolute",
width:"100%",height:BAR_HEIGHT,left:0,bottom:0,right:0,"max-width":"640px",margin:"0 auto"});var userActivate=function(){userActive=true};$videoObj.bind(eClick,function(){userActive=true}).hover(function(){userActive=true},function(){userActive=false});if(!hidecontrols)setInterval(function(){if(userActive){$videoControls.show();userActive=false;clearTimeout(hideControlsTimerId);hideControlsTimerId=setTimeout(function(){if(!$videoObj.get(0).paused)$videoControls.fadeOut()},5E3)}},250);$(".html5boxVideoControlsBg",
$videoControls).css({display:"block",position:"absolute",width:"100%",height:"100%",left:0,top:0,"background-color":"#000000",opacity:0.7,filter:"alpha(opacity=70)"});$(".html5boxPlayPause",$videoControls).css({display:"block",position:"relative",width:BUTTON_SIZE+"px",height:BUTTON_SIZE+"px",margin:Math.floor((BAR_HEIGHT-BUTTON_SIZE)/2),"float":"left"});var $videoBtnPlay=$(".html5boxPlay",$videoControls);var $videoBtnPause=$(".html5boxPause",$videoControls);$videoBtnPlay.css({display:"block",position:"absolute",
top:0,left:0,width:BUTTON_SIZE+"px",height:BUTTON_SIZE+"px",cursor:"pointer","background-image":"url('"+skinFolder+"html5boxplayer_playpause.png"+"')","background-position":"top left"}).hover(function(){$(this).css({"background-position":"bottom left"})},function(){$(this).css({"background-position":"top left"})}).bind(eClick,function(){$videoObj.get(0).play()});$videoBtnPause.css({display:"none",position:"absolute",top:0,left:0,width:BUTTON_SIZE+"px",height:BUTTON_SIZE+"px",cursor:"pointer","background-image":"url('"+
skinFolder+"html5boxplayer_playpause.png"+"')","background-position":"top right"}).hover(function(){$(this).css({"background-position":"bottom right"})},function(){$(this).css({"background-position":"top right"})}).bind(eClick,function(){$videoObj.get(0).pause()});var $videoTimeCurrent=$(".html5boxTimeCurrent",$videoControls);var $videoTimeTotal=$(".html5boxTimeTotal",$videoControls);var $videoSeeker=$(".html5boxSeeker",$videoControls);var $videoSeekerPlay=$(".html5boxSeekerPlay",$videoControls);
var $videoSeekerBuffer=$(".html5boxSeekerBuffer",$videoControls);var $videoSeekerHandler=$(".html5boxSeekerHandler",$videoControls);$videoTimeCurrent.css({display:"block",position:"relative","float":"left","line-height":BAR_HEIGHT+"px","font-weight":"normal","font-size":"12px",margin:"0 8px","font-family":"Arial, Helvetica, sans-serif",color:"#fff"});$videoTimeTotal.css({display:"block",position:"relative","float":"right","line-height":BAR_HEIGHT+"px","font-weight":"normal","font-size":"12px",margin:"0 8px",
"font-family":"Arial, Helvetica, sans-serif",color:"#fff"});$videoSeeker.css({display:"block",cursor:"pointer",overflow:"hidden",position:"relative",height:"10px","background-color":"#222",margin:Math.floor((BAR_HEIGHT-10)/2)+"px 4px"}).bind(eStart,function(e){var e0=isTouch?e.originalEvent.touches[0]:e;var pos=e0.pageX-$videoSeeker.offset().left;$videoSeekerPlay.css({width:pos});$videoObj.get(0).currentTime=pos*$videoObj.get(0).duration/$videoSeeker.width();$videoSeeker.bind(eMove,function(e){var e0=
isTouch?e.originalEvent.touches[0]:e;var pos=e0.pageX-$videoSeeker.offset().left;$videoSeekerPlay.css({width:pos});$videoObj.get(0).currentTime=pos*$videoObj.get(0).duration/$videoSeeker.width()})}).bind(eCancel,function(){$videoSeeker.unbind(eMove)});$videoSeekerBuffer.css({display:"block",position:"absolute",left:0,top:0,height:"100%","background-color":"#444"});$videoSeekerPlay.css({display:"block",position:"absolute",left:0,top:0,height:"100%","background-color":"#fcc500"});if(!isIPhone&&($videoObj.get(0).requestFullscreen||
$videoObj.get(0).webkitRequestFullScreen||$videoObj.get(0).mozRequestFullScreen||$videoObj.get(0).webkitEnterFullScreen||$videoObj.get(0).msRequestFullscreen)){var switchScreen=function(fullscreen){if(fullscreen){if($videoObj.get(0).requestFullscreen)$videoObj.get(0).requestFullscreen();else if($videoObj.get(0).webkitRequestFullScreen)$videoObj.get(0).webkitRequestFullScreen();else if($videoObj.get(0).mozRequestFullScreen)$videoObj.get(0).mozRequestFullScreen();else if($videoObj.get(0).webkitEnterFullScreen)$videoObj.get(0).webkitEnterFullScreen();
if($videoObj.get(0).msRequestFullscreen)$videoObj.get(0).msRequestFullscreen()}else if(document.cancelFullScreen)document.cancelFullScreen();else if(document.mozCancelFullScreen)document.mozCancelFullScreen();else if(document.webkitCancelFullScreen)document.webkitCancelFullScreen();else if(document.webkitExitFullscreen)document.webkitExitFullscreen();else if(document.msExitFullscreen)document.msExitFullscreen()};var switchScreenCSS=function(fullscreen){$videoControls.css({position:fullscreen?"fixed":
"absolute"});var backgroundPosY=$videoFullscreen.css("background-position")?$videoFullscreen.css("background-position").split(" ")[1]:$videoFullscreen.css("background-position-y");$videoFullscreen.css({"background-position":(fullscreen?"right":"left")+" "+backgroundPosY});$videoFullscreenBg.css({display:fullscreen?"block":"none"});if(fullscreen){$(document).bind("mousemove",userActivate);$videoControls.css({"z-index":2147483647})}else{$(document).unbind("mousemove",userActivate);$videoControls.css({"z-index":""})}};
document.addEventListener("fullscreenchange",function(){isFullscreen=document.fullscreen;switchScreenCSS(document.fullscreen)},false);document.addEventListener("mozfullscreenchange",function(){isFullscreen=document.mozFullScreen;switchScreenCSS(document.mozFullScreen)},false);document.addEventListener("webkitfullscreenchange",function(){isFullscreen=document.webkitIsFullScreen;switchScreenCSS(document.webkitIsFullScreen)},false);$videoObj.get(0).addEventListener("webkitbeginfullscreen",function(){isFullscreen=
true},false);$videoObj.get(0).addEventListener("webkitendfullscreen",function(){isFullscreen=false},false);$("head").append("<style type='text/css'>video::-webkit-media-controls { display:none !important; }</style>");var $videoFullscreen=$(".html5boxFullscreen",$videoControls);$videoFullscreen.css({display:"block",position:"relative","float":"right",width:BUTTON_SIZE+"px",height:BUTTON_SIZE+"px",margin:Math.floor((BAR_HEIGHT-BUTTON_SIZE)/2),cursor:"pointer","background-image":"url('"+skinFolder+"html5boxplayer_fullscreen.png"+
"')","background-position":"left top"}).hover(function(){var backgroundPosX=$(this).css("background-position")?$(this).css("background-position").split(" ")[0]:$(this).css("background-position-x");$(this).css({"background-position":backgroundPosX+" bottom"})},function(){var backgroundPosX=$(this).css("background-position")?$(this).css("background-position").split(" ")[0]:$(this).css("background-position-x");$(this).css({"background-position":backgroundPosX+" top"})}).bind(eClick,function(){isFullscreen=
!isFullscreen;switchScreen(isFullscreen)})}if(hd){var $videoHD=$(".html5boxHD",$videoControls);$videoHD.css({display:"block",position:"relative","float":"right",width:BUTTON_SIZE+"px",height:BUTTON_SIZE+"px",margin:Math.floor((BAR_HEIGHT-BUTTON_SIZE)/2),cursor:"pointer","background-image":"url('"+skinFolder+"html5boxplayer_hd.png"+"')","background-position":(isHd?"right":"left")+" center"}).bind(eClick,function(){isHd=!isHd;$(this).css({"background-position":(isHd?"right":"left")+" center"});parentInst.isHd=
isHd;var isPaused=$videoObj.get(0).isPaused;$videoObj.get(0).setAttribute("src",(isHd?hd:src)+"#t="+$videoObj.get(0).currentTime);if(!isPaused)$videoObj.get(0).play();else if(!isIPhone)$videoObj.get(0).pause()})}$videoObj.get(0).volume=defaultvolume;var volumeSaved=defaultvolume==0?1:defaultvolume;var volume=$videoObj.get(0).volume;$videoObj.get(0).volume=volume/2+0.1;if($videoObj.get(0).volume===volume/2+0.1){$videoObj.get(0).volume=volume;var $videoVolume=$(".html5boxVolume",$videoControls);var $videoVolumeButton=
$(".html5boxVolumeButton",$videoControls);var $videoVolumeBar=$(".html5boxVolumeBar",$videoControls);var $videoVolumeBarBg=$(".html5boxVolumeBarBg",$videoControls);var $videoVolumeBarActive=$(".html5boxVolumeBarActive",$videoControls);$videoVolume.css({display:"block",position:"relative","float":"right",width:BUTTON_SIZE+"px",height:BUTTON_SIZE+"px",margin:Math.floor((BAR_HEIGHT-BUTTON_SIZE)/2)}).hover(function(){clearTimeout(hideVolumeBarTimeoutId);var volume=$videoObj.get(0).volume;$videoVolumeBarActive.css({height:Math.round(volume*
100)+"%"});$videoVolumeBar.show()},function(){clearTimeout(hideVolumeBarTimeoutId);hideVolumeBarTimeoutId=setTimeout(function(){$videoVolumeBar.hide()},1E3)});$videoVolumeButton.css({display:"block",position:"absolute",top:0,left:0,width:BUTTON_SIZE+"px",height:BUTTON_SIZE+"px",cursor:"pointer","background-image":"url('"+skinFolder+"html5boxplayer_volume.png"+"')","background-position":"top "+(volume>0?"left":"right")}).hover(function(){var backgroundPosX=$(this).css("background-position")?$(this).css("background-position").split(" ")[0]:
$(this).css("background-position-x");$(this).css({"background-position":backgroundPosX+" bottom"})},function(){var backgroundPosX=$(this).css("background-position")?$(this).css("background-position").split(" ")[0]:$(this).css("background-position-x");$(this).css({"background-position":backgroundPosX+" top"})}).bind(eClick,function(){var volume=$videoObj.get(0).volume;if(volume>0){volumeSaved=volume;volume=0}else volume=volumeSaved;var backgroundPosY=$(this).css("background-position")?$(this).css("background-position").split(" ")[1]:
$(this).css("background-position-y");$videoVolumeButton.css({"background-position":(volume>0?"left":"right")+" "+backgroundPosY});$videoObj.get(0).volume=volume;$videoVolumeBarActive.css({height:Math.round(volume*100)+"%"})});$videoVolumeBar.css({display:"none",position:"absolute",left:4,bottom:"100%",width:24,height:80,"margin-bottom":Math.floor((BAR_HEIGHT-BUTTON_SIZE)/2),"background-color":"#000000",opacity:0.7,filter:"alpha(opacity=70)"});$videoVolumeBarBg.css({display:"block",position:"relative",
width:10,height:68,margin:7,cursor:"pointer","background-color":"#222"});$videoVolumeBarActive.css({display:"block",position:"absolute",bottom:0,left:0,width:"100%",height:"100%","background-color":"#fcc500"});$videoVolumeBarBg.bind(eStart,function(e){var e0=isTouch?e.originalEvent.touches[0]:e;var vol=1-(e0.pageY-$videoVolumeBarBg.offset().top)/$videoVolumeBarBg.height();vol=vol>1?1:vol<0?0:vol;$videoVolumeBarActive.css({height:Math.round(vol*100)+"%"});$videoVolumeButton.css({"background-position":"left "+
(vol>0?"top":"bottom")});$videoObj.get(0).volume=vol;$videoVolumeBarBg.bind(eMove,function(e){var e0=isTouch?e.originalEvent.touches[0]:e;var vol=1-(e0.pageY-$videoVolumeBarBg.offset().top)/$videoVolumeBarBg.height();vol=vol>1?1:vol<0?0:vol;$videoVolumeBarActive.css({height:Math.round(vol*100)+"%"});$videoVolumeButton.css({"background-position":"left "+(vol>0?"top":"bottom")});$videoObj.get(0).volume=vol})}).bind(eCancel,function(){$videoVolumeBarBg.unbind(eMove)})}var calcTimeFormat=function(seconds){var h0=
Math.floor(seconds/3600);var h=h0<10?"0"+h0:h0;var m0=Math.floor((seconds-h0*60)/60);var m=m0<10?"0"+m0:m0;var s0=Math.floor(seconds-(h0*3600+m0*60));var s=s0<10?"0"+s0:s0;var r=m+":"+s;if(h0>0)r=h+":"+r;return r};if(hideplaybutton)$videoPlay.hide();if(hidecontrols)$videoControls.hide();var onVideoPlay=function(){if(!hideplaybutton)$videoPlay.hide();if(!hidecontrols){$videoBtnPlay.hide();$videoBtnPause.show()}};var onVideoPause=function(){if(!hideplaybutton)$videoPlay.show();if(!hidecontrols){$videoControls.show();
clearTimeout(hideControlsTimerId);$videoBtnPlay.show();$videoBtnPause.hide()}};var onVideoEnded=function(){$(window).trigger("html5lightbox.videoended");if(!hideplaybutton)$videoPlay.show();if(!hidecontrols){$videoControls.show();clearTimeout(hideControlsTimerId);$videoBtnPlay.show();$videoBtnPause.hide()}};var onVideoUpdate=function(){var curTime=$videoObj.get(0).currentTime;if(curTime){$videoTimeCurrent.text(calcTimeFormat(curTime));var duration=$videoObj.get(0).duration;if(duration){$videoTimeTotal.text(calcTimeFormat(duration));
if(!sliderDragging){var sliderW=$videoSeeker.width();var pos=Math.round(sliderW*curTime/duration);$videoSeekerPlay.css({width:pos});$videoSeekerHandler.css({left:pos})}}}};var onVideoProgress=function(){if($videoObj.get(0).buffered&&$videoObj.get(0).buffered.length>0&&!isNaN($videoObj.get(0).buffered.end(0))&&!isNaN($videoObj.get(0).duration)){var sliderW=$videoSeeker.width();$videoSeekerBuffer.css({width:Math.round(sliderW*$videoObj.get(0).buffered.end(0)/$videoObj.get(0).duration)})}};try{$videoObj.bind("play",
onVideoPlay);$videoObj.bind("pause",onVideoPause);$videoObj.bind("ended",onVideoEnded);$videoObj.bind("timeupdate",onVideoUpdate);$videoObj.bind("progress",onVideoProgress)}catch(e){}}})(jQuery);jQuery(document).ready(function(){if(typeof html5Lightbox==="undefined")html5Lightbox=jQuery(".html5lightbox").html5lightbox()})};
