<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8"> 
    <link rel="stylesheet" type="text/css" href="/interactives/general/css/style.css"/>
    <link rel="stylesheet" type="text/css" href="css/jquery-ui.min.css" />
    <link rel="stylesheet" type="text/css" href="css/style.css" />
</head>
<body>
<script>
	(function(i,s,o,g,r,a,m){i['GoogleAnalyticsObject']=r;i[r]=i[r]||function(){
		(i[r].q=i[r].q||[]).push(arguments)},i[r].l=1*new Date();a=s.createElement(o),
		m=s.getElementsByTagName(o)[0];a.async=1;a.src=g;m.parentNode.insertBefore(a,m)
	})(window,document,'script','https://www.google-analytics.com/analytics.js','ga');

	ga('create', 'UA-2014879-79', 'auto');
	ga('send', 'pageview');

</script>

    <?php 
        $configString = file_get_contents("config.json");
        $config = json_decode($configString);
        $title = $config->title;
        $description = $config->description;
    ?>

    <div class='content'>
        <div class='heading'>
            <h1><?php echo $title; ?></h1>
            <p><?php echo $description; ?></p>
        </div>
        
        <div class="content-holder">
            <div class="diff-image-holder">
                <img src="img/stories/house_01.png" class="diff-img" usemap="#Map" >
                <map name="Map" id="Map">
                    <area alt="" title="" shape="rect" coords="111,141,192,221" id="window"/>
                    <area alt="" title="" shape="rect" coords="62,174,95,331" id="lamp" />
                    <area alt="" title="" shape="rect" coords="216,214,296,270" id="tv"/>
                    <area alt="" title="" shape="rect" coords="209,153,266,187" id="frames"/>
                    <area alt="" title="" shape="rect" coords="312,141,392,221" id="fruit" />
                    <area alt="" title="" shape="rect" coords="97,264,192,332" id="furniture"/>
                </map>

                <div class="zoom-images">
                    <img src="img/stories/difference_01.png" class="difference-frames">
                    <img src="img/stories/difference_02.png" class="difference-window">
                    <img src="img/stories/difference_03.png" class="difference-fruit">
                    <img src="img/stories/difference_04.png" class="difference-furniture">
                    <img src="img/stories/difference_05.png" class="difference-lamp">
                    <img src="img/stories/difference_06.png" class="difference-tv">
                </div>
            </div>

            <div class="diff-image-holder">
                <img src="img/stories/house_02.png">
            </div>
        </div>
        
        <?php
        $referer = isset($_SERVER['HTTP_REFERER']) ? $_SERVER['HTTP_REFERER'] : '';

        if($referer != '') {?>
            <div class='cancelBtnHolder'>
                <a class='big-btn' onclick="window.close(); return false;" href="<?= $referer ?>"></a>
            </div>
        <?php } ?>
    </div>
    
    <script type="text/javascript" src="js/jquery-1.11.2.min.js"></script>
    <script type="text/javascript" src="js/jquery-ui.min.js"></script>
    <script type="text/javascript" src="js/script.js"></script>
</body>
</html>