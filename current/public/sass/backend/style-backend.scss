.max img {
  height: auto;
}
.search label
{
  width: 200px;
}

.search input
{
  /*    margin-right: 10px;*/
}

.lessons table th,
.lessons table td {
  max-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;

}

/*
    Document   : bootstrap-custom
    Created on : 18-Aug-2014, 09:57:17
    Author     : <PERSON><PERSON><PERSON><PERSON>
    Description:
        Purpose of the stylesheet follows.
*/
root {
  display: block;
}

.max {
  min-height:20px;
  max-width: 20px;
}

.max img {
  height: 190px;
  width: 160px;
}

/*Vimeo Video Dimensions*/
.embed-responsive-item {
  margin-left: 5%;
  max-width: 20%;
  max-height: 20%;
}

.page-header {
  margin-top: 0;
  padding-left: 1%;
}

.panel-title {
  padding-bottom: 0.8%;
}

.panel-heading {
  padding-top:0.4%;
}

.preview-box {
  height: 20px!important;
}

img.displayed {
  display: block;
  margin-left: auto;
  margin-right: auto;
}