<!DOCTYPE html>
<html>
<head>
    <link rel="stylesheet" type="text/css" href="/interactives/general/css/style.css"/>
    <link rel="stylesheet" type="text/css" href="css/jquery-ui.min.css" />
    <link rel="stylesheet" type="text/css" href="css/style.css" />
</head>
<body>
<script>
    (function(i,s,o,g,r,a,m){i['GoogleAnalyticsObject']=r;i[r]=i[r]||function(){
            (i[r].q=i[r].q||[]).push(arguments)},i[r].l=1*new Date();a=s.createElement(o),
        m=s.getElementsByTagName(o)[0];a.async=1;a.src=g;m.parentNode.insertBefore(a,m)
    })(window,document,'script','https://www.google-analytics.com/analytics.js','ga');

    ga('create', 'UA-2014879-79', 'auto');
    ga('send', 'pageview');

</script>

<?php
$configString = file_get_contents("config.json");
$config = json_decode($configString);
$title = $config->title;
$description = $config->description;
?>

<div class='content'>
    <div class='heading'>
        <h1><?php echo $title; ?></h1>
        <p><?php echo $description; ?></p>
    </div>

    <div class="scenario-count">
        <ul>
            <li>1</li>
            <li>2</li>
            <li>3</li>
            <li>4</li>
        </ul>
    </div>

    <div class="scenario">
        <div class="main-holder">
            <img src="" alt="" class="main" />
        </div>
        <div class="choice-holder">
            <img src="" alt="" class="choice choice1" />
            <span class="or-text">or</span>
            <img src="" alt="" class="choice choice2" />
        </div>
    </div>

    <div class="summary">
        <div class="good">
            <span>Good Choices</span>
            <img src="img/stories/pic03.png">
            <img src="img/stories/pic06.png">
            <img src="img/stories/pic09.png">
            <img src="img/stories/pic11.png">
        </div>

        <div class="bad">
            <span>Bad Choices</span>
            <img src="img/stories/pic02.png">
            <img src="img/stories/pic05.png">
            <img src="img/stories/pic08.png">
            <img src="img/stories/pic12.png">
        </div>
    </div>

    <a class="big-btn" href="#" id="next">Next</a>
    <a class="big-btn" href="#" id="replay">Replay</a>

    <?php
    $referer = isset($_SERVER['HTTP_REFERER']) ? $_SERVER['HTTP_REFERER'] : '';

    if($referer != '') {?>
        <div class='cancelBtnHolder'>
            <a class='big-btn' onclick="window.close(); return false;" href="<?= $referer ?>"></a>
        </div>
    <?php } ?>
</div>

<script type="text/javascript" src="js/jquery-1.11.2.min.js"></script>
<script type="text/javascript" src="js/jquery-ui.min.js"></script>
<script type="text/javascript" src="js/script.js"></script>
<script type="text/javascript" src="/interactives/general/js/script.js"></script>
</body>
</html>