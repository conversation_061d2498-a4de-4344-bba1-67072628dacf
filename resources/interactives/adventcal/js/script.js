$(function() {
    var colors = [
        "pink",
        "purple",
        "orange",
        "blue"
    ];

    $('.window').each(function () {
        //pick random color 
        var color = getRandomColor();
        if($(this).prev().hasClass(color)) {
            color = getRandomColor();
        }
        $(this).addClass(color);
    });

    var today = new Date();
    var day = today.getDate();
    var month = today.getMonth()+1; //January is 0!

    var windows = $('.window-holders > .window');


    //if(month === 12) {
        windows.slice(0, day-1).removeClass('closed');
        windows.slice(0, day-1).addClass('opened');

        if (document.cookie) {
            var cookie = document.cookie.replace('calendar=', '');

            if(cookie == 1) {
                windows.slice(day-1, day).removeClass('closed');
                windows.slice(day-1, day).addClass('opened');
            } else {
                windows.slice(day-1, day).addClass('closed');
                windows.slice(day-1, day).removeClass('opened');
            }
        }

        windows.slice(day-1, day).click(function () {
            var $door = $(this).find('.door');
            if($(this).hasClass('closed')) {
                $(this).removeClass('closed');
                $(this).addClass('opened');
                document.cookie = 'calendar=1';
            } else {
                $(this).removeClass('opened');
                $(this).addClass('closed');

                document.cookie = 'calendar=';
            }
        });
    //}

    /*var total = $('.window .door').length;
    $('.window .door').each(function (i, item) {
        $(this).find('p').text(total--);
    });*/

    $('.window .door').each(function (i, item) {
        $(this).find('p').text(i+1);
    });

    
    function getRandomColor() {
        return colors[Math.floor(Math.random()*colors.length)];
    }
    
});