Array.prototype.clone = function() {
	return this.slice(0);
};

$(function() {

	$.ui.ddmanager.prepareOffsets = function( t, event ) {
		var i, j,
			m = $.ui.ddmanager.droppables[ t.options.scope ] || [],
			type = event ? event.type : null, // workaround for #2317
			list = ( t.currentItem || t.element ).find( ":data(ui-droppable)" ).addBack();

		droppablesLoop: for ( i = 0; i < m.length; i++ ) {

			// No disabled and non-accepted
			if ( m[ i ].options.disabled || ( t && !m[ i ].accept.call( m[ i ].element[ 0 ], ( t.currentItem || t.element ) ) ) ) {
				continue;
			}

			// Filter out elements in the current dragged item
			for ( j = 0; j < list.length; j++ ) {
				if ( list[ j ] === m[ i ].element[ 0 ] ) {
					m[ i ].proportions().height = 0;
					continue droppablesLoop;
				}
			}

			m[ i ].visible = m[ i ].element.css( "display" ) !== "none";
			if ( !m[ i ].visible ) {
				continue;
			}

			// Activate the droppable if used directly from draggables
			if ( type === "mousedown" ) {
				m[ i ]._activate.call( m[ i ], event );
			}

			m[ i ].offset = m[ i ].element.offset();
			m[ i ].proportions({ width: m[ i ].element[ 0 ].offsetWidth / $('body').css('zoom'), height: m[ i ].element[ 0 ].offsetHeight / $('body').css('zoom') });
		}

	};

	var pictureSequence = [
		[], // index 0
		[
			"img/stories/03_booksaboutjesus_interactivejigsaw_1b.png",
			"img/stories/03_booksaboutjesus_interactivejigsaw_1c.png",
			"img/stories/03_booksaboutjesus_interactivejigsaw_1d.png",
			"img/stories/03_booksaboutjesus_interactivejigsaw_1e.png"
		],
		[
			"img/stories/03_booksaboutjesus_interactivejigsaw_2b.png",
			"img/stories/03_booksaboutjesus_interactivejigsaw_2c.png",
			"img/stories/03_booksaboutjesus_interactivejigsaw_2d.png",
			"img/stories/03_booksaboutjesus_interactivejigsaw_2e.png"
		],
		[
			"img/stories/03_booksaboutjesus_interactivejigsaw_3b.png",
			"img/stories/03_booksaboutjesus_interactivejigsaw_3c.png",
			"img/stories/03_booksaboutjesus_interactivejigsaw_3d.png",
			"img/stories/03_booksaboutjesus_interactivejigsaw_3e.png"
		]
	];

	var angles = [90, 180, 270];

	var book = 1;

	var audiotypes={
		"mp3": "audio/mpeg",
		"mp4": "audio/mp4",
		"ogg": "audio/ogg",
		"wav": "audio/wav"
	}

	function ss_soundbits(sound, loop){
		sound = '/interactives/dragndrop/sounds/' + sound;

		var audio_element = document.createElement('audio')
		if (audio_element.canPlayType){
			for (var i=0; i == 0 && i<arguments.length; i++){
				for(var key in audiotypes) {
					var source_element = document.createElement('source')
					source_element.setAttribute('src', arguments[i] + "." + key)
					source_element.setAttribute('type', audiotypes[key])
					audio_element.appendChild(source_element);
				}
			}

			//audio_element.play()
			audio_element.playclip=function(){
				this.pause()
				//this.currentTime=0
				this.play()
			}

			if(loop) {
				audio_element.addEventListener('ended', function() {
					ss_soundbits(sound.replace('/interactives/dragndrop/sounds/', ''), loop).playclip();
				}, false);
			}

			return audio_element
		}
	}

	function shuffleImages() {

		var o = pictureSequence[book].clone();
		for(var j, x, i = o.length; i; j = Math.floor(Math.random() * i), x = o[--i], o[i] = o[j], o[j] = x);

		$('.center-drop').removeClass("win").addClass("book"+book);
		$('.drop-zone').removeClass("dropped");

		$('.images-holder img').each(function (i, item) {
			$(this).removeAttr('style').removeClass('dropped');
			var random_angle = angles[Math.floor(Math.random() * angles.length)];
			$(this).attr("src", o[i]);
			$(this).rotate(random_angle);
			$(this).data("angle", random_angle);
		});

		$(["b", "c", "d", "e"]).each(function (i, item) {
			$('.center-drop .drop-zone').eq(i).attr("item", book + item);
		});

		$('.images-holder img').draggable({
			cursor: 'move',
			scope: "book" + book,
			drag: function(event, ui) {

				var factor = (1 / $('body').css('zoom')) - 1

				ui.position.top += Math.round((ui.position.top - ui.originalPosition.top) * factor)
				ui.position.left += Math.round((ui.position.left - ui.originalPosition.left) * factor)
			},
			//			revert: true
			revert: function (event, ui) {
				$(this).data("uiDraggable").originalPosition = {
					top: 0,
					left: 0
				};

				return !event;
			}
		});

		$('.images-holder img').rotate({
			bind:
			{
				click: function() {
					var angle = $(this).data("angle") + 90;
					$(this).data("angle", angle);
					$(this).rotate({
						animateTo:angle,
						duration:300,
						callback: function() {
							checkWin();
						}
					})
				}
			}
		});

		$('.drop-zone').droppable({
			hoverClass: 'hovered',
			scope: "book" + book,
			create: handleCreateEvent,
			drop: handleDropEvent,
			out: handleOutEvent
		});

	}

	function updateSteps() {
		$('.scenario-count ul li').each(function (i, item) {

			var index = book - 1;

			if (index === i) {
				$(this).addClass('active');
			}

			if (index > i) {
				$(this).addClass('selected');
				$(this).removeClass('active');
			}
		});
	}

	function handleCreateEvent( event, ui ) {
		$(this).droppable('option', 'accept', '.images-holder img');
	}

	function handleOutEvent( event, ui ) {
		$(this).droppable('option', 'accept', '.images-holder img');
		var draggable = ui.draggable;
		draggable.removeClass('dropped');
		draggable.data("correct", "0");
		$(this).removeClass('dropped');
	}

	function handleDropEvent( event, ui ) {

		$(this).droppable('option', 'accept', ui.draggable);

		var draggable = ui.draggable;

		var itemDrag = draggable.attr('src').replace('img/stories/03_booksaboutjesus_interactivejigsaw_', '').replace('.png', '');
		var itemDrop = $(this).attr('item');

		var offset = $(this).offset();

		draggable.offset({ top: offset.top, left: offset.left });

		draggable.addClass('dropped');
		$(this).addClass('dropped');

		ss_soundbits('dragndrop').playclip();

		if (itemDrag === itemDrop) {
			draggable.data("correct", "1");
		}

		checkWin();

	}

	function checkWin() {

		var result = 0;
		var correctDrop = 0;

		$('.images-holder img').each(function () {
			correctDrop = correctDrop + parseInt($(this).data("correct"));
			result = result + ($(this).data("angle")%360);
		});

		if(result === 0 && correctDrop === 4) {

			// disable 'draggable' and 'rotate'
			$('.images-holder img').hide().data("correct", "0").draggable( "destroy" ).rotate({
				bind: {
					click: function () {
						setTimeout(function() {
							$('.images-holder img').stopRotate();
						}, 300)
					}
				}
			});

			$('.center-drop').addClass("win");

			book++;

			if(book != 4) {
				setTimeout(function () {
					$('.continue').fadeIn(300);
				}, 1500);
			} else {
				setTimeout(function () {
					$('.continue2').fadeIn(300);
				}, 1500);
			}

			ss_soundbits('win').playclip();

		}

	}

	var soundOnePlayed = false;

	$(document).ready(function () {

		$('.replay').click(function (e) {
			window.location.reload();
			e.preventDefault();
			e.stopPropagation();
		});

		$('.start').click(function (e) {
			e.preventDefault();
			e.stopPropagation();

			$('.start-screen').hide();
		});

		$('.continue').click(function (e) {
			e.preventDefault();
			e.stopPropagation();
			$('.drop-zone').droppable("destroy");
			updateSteps();
			shuffleImages();
			$(this).fadeOut(150);
		});

		$('.continue2').click(function (e) {
			window.location.reload();
			e.preventDefault();
			e.stopPropagation();
		});

		shuffleImages();

		updateSteps();


	});

});
