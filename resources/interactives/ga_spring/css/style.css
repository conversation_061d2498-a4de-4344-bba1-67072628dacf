.heading p {
    margin-bottom: 0
}

#verifyMatch:hover {
    border-bottom-color: #f9b46e;
}

.big-btn {
    background-color: #f9b46e;
    border-bottom: solid 3px #e59751;
}

.content-holder {
    padding: 0;
    text-align: center;
    width: 100%;
    margin: 0 auto;
    height: 100%;
}
.content-holder:after {
    content:"";
    display:table;
    clear:both;
}

.diff-image-holder {
    background-color: #fff;
    width: 49%;
    float: left;
    padding: 0;
    height: 90%;
}

.diff-image-holder img {
    width: 100%;
    height: 100%;
}

.content-holder .diff-image-holder:last-child {
    margin-left: 0;
    float: right;
}

.area:hover {
    background: red;
}

.zoom-images img {
    width: 150px;
    height: 150px;
    border-radius: 50%;
    border: 4px solid rgba(93,68,88,0.2);
    position: absolute;
    display: none;
}

.zoom-images .difference-clothes {
    top: 63%;
    left: 15%;
    width: 300px;
    height: 300px;
}

.zoom-images .difference-leaves {
    top: 20%;
    left: 8%;
}

.zoom-images .difference-birds {
    top: 37%;
    left: 9%;
}

.zoom-images .difference-plants {
    top: 74%;
    left: 5%;
    height: 200px;
    width: 200px;
}

.zoom-images .difference-sky {
    top: 11%;
    width: 300px;
    left: 25%;
    height: 300px;
}

.diff-img,
map area {
    cursor: pointer;
}