<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<link rel="stylesheet" type="text/css" href="/interactives/general/css/style.css" />
	<link rel="stylesheet" type="text/css" href="css/jquery-ui.min.css" />
	<link rel="stylesheet" type="text/css" href="css/style.css" />
</head>
<body>
<script>
	(function(i,s,o,g,r,a,m){i["GoogleAnalyticsObject"]=r;i[r]=i[r]||function(){
			(i[r].q=i[r].q||[]).push(arguments)},i[r].l=1*new Date();a=s.createElement(o),
		m=s.getElementsByTagName(o)[0];a.async=1;a.src=g;m.parentNode.insertBefore(a,m)
	})(window,document,"script",'//www.google-analytics.com/analytics.js','ga');

	ga('create', 'UA-2014879-79', 'auto');
	ga('send', 'pageview');

</script>

<?php
$configString = file_get_contents("config.json");
$config = json_decode($configString);
$title = $config->title;
$description = $config->description;
?>

<div class="content">

	<div class="heading">
		<h1><?php echo $title; ?></h1>
		<p><?php echo $description; ?></p>
	</div>

	<div class="score-board">
		<div>
			<img class="hidden" src="img/scorescroll.png">
			<img class="hidden" src="img/scorescroll.png">
			<img class="hidden" src="img/scorescroll.png">
		</div>
	</div>

	<div class="lane4">
		<div class="wrap">
			<img src="img/bg4.png" class="lane" />
		</div>
	</div>
	<div class="lane3">
		<div class="wrap">
			<img src="img/bg3.png" class="lane" />
			<img src="img/item.png" class="items item" />
			<img src="img/hole.png" class="holes hole" />
		</div>
	</div>
	<div class="lane2">
		<div class="wrap">
			<img src="img/bg2.png" class="lane" />
			<img src="img/item.png" class="items item" />
			<img src="img/hole.png" class="holes hole" />
		</div>
	</div>
	<div class="lane1">
		<div class="wrap">
			<img src="img/bg1.png" class="lane" />
			<img src="img/item.png" class="items item" />
			<img src="img/hole.png" class="holes hole" />
		</div>
	</div>

	<div class="character stopped" data-lane="1"></div>

	<?php
	$referer = $_SERVER["HTTP_REFERER"];

	if($referer != "") {?>
		<div class="cancelBtnHolder">
			<a class="big-btn" onclick="window.close(); return false;" href="<?= $referer ?>"></a>
		</div>
	<?php } ?>

</div>

<div class="question-screen">
	<div class="container">
		<h2 class="question"></h2>
		<input name="answer" type="hidden" value="" />
		<a class="big-btn answer false" href="#">False</a>
		<a class="big-btn answer true" href="#">True</a>
	</div>
</div>

<div class="win-screen">
	<div class="container">
		<img src="img/win.png" />
		<h2>An-mhaith!</h2>
		<a class="big-btn replay" href="#">Replay</a>
	</div>
</div>

<div class="loose-screen">
	<div class="container">
		<img src="img/loose.png" />
		<h2>Déan iarracht arís!</h2>
		<a class="big-btn replay" href="#">Replay</a>
	</div>
</div>

<div class="start-screen">
	<div class="container">
		<h3>Conas an cluiche a imirt</h3>
		<p>Bain úsáid as na saigheada ar do eochaircheap chun cuidiú le Naomh Pádraig bogadh ar aghaidh</p>
		<div class="row row1 clearfix">
			<div class="col1">
				<img class="" src="img/howto.png" />
			</div>
			<div class="col2">
				<div class="up">
					<img src="img/up.png" alt="Up arrow" />
				</div>
				<div class="left">
					<span class="disabled">x</span>
					<img src="img/left.png" alt="Left arrow" />
				</div>
				<div class="down">
					<img src="img/down.png" alt="Down arrow" />
				</div>
				<div class="right">
					<span class="disabled">x</span>
					<img src="img/right.png" alt="Right arrow" />
				</div>

			</div>
		</div>
		<div class="divider"></div>

		<div class="row row2 clearfix">
			<div class="col1">
				<p>Beir ar na 3 scrolla</p>
				<img class="" src="img/item.png" />
			</div>
			<div class="col2">

				<p>Seachain na poill</p>
				<img class="" src="img/hole.png" />

			</div>
		</div>

		<a class="big-btn start" href="#">Play now</a>
	</div>
</div>

<script type="text/javascript" src="js/jquery-1.11.2.min.js"></script>
<script type="text/javascript" src="js/jquery-ui.min.js"></script>
<script type="text/javascript" src="js/script.js"></script>
<script type="text/javascript" src="/interactives/general/js/script.js"></script>
</body>
</html>