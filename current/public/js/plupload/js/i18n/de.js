// German
plupload.addI18n({
    'Select files' : 'Dateien hochladen',
    'Add files to the upload queue and click the start button.' : '<PERSON><PERSON> hinzuf&uuml;gen und auf \'Hochladen\' klicken.',
    'Filename' : 'Dateiname',
    'Status' : 'Status',
    'Size' : 'Gr&ouml;&szlig;e',
    'Add files' : 'Dateien', // hinzuf&uuml;gen',
    'Stop current upload' : 'Aktuelles Hochladen stoppen',
    'Start uploading queue' : 'Hochladen starten',
    'Uploaded %d/%d files': '%d/%d Dateien sind hochgeladen',
    'N/A' : 'Nicht verf&uuml;gbar',
    'Drag files here.' : '<PERSON><PERSON>hen <PERSON>e die Dateien hier hin',
    'File extension error.': 'Fehler bei Dateiendung',
    'File size error.': 'Fehler bei Dateigr&ouml;ße',
    'Init error.': 'Initialisierungsfehler',
    'HTTP Error.': 'HTT<PERSON>-<PERSON><PERSON>',
    'Security error.': '<PERSON><PERSON><PERSON><PERSON><PERSON>hler',
    'Generic error.': '<PERSON><PERSON><PERSON>hler',
    'IO error.': 'Ein/Ausgabe-<PERSON>hler',
    'Stop Upload': 'Hochladen stoppen',
    'Start upload': 'Hochladen',
    '%d files queued': '%d Dateien in der Warteschlange'
});