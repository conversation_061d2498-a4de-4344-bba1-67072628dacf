body, html {
	background-color: #e59751;
	/*overflow: hidden;*/
}

.content,.content:before, .content:after {
	background-color: #f9b46e;
	overflow: hidden;
}

h1 {
	color: #fff;
}

h2 {
	color: #333;
}

.content {
	padding: 0;
}

.cancelBtnHolder .big-btn {
	background-image: url('../../general/img/close.png');
	border-color: #fff;
}


.heading {
	margin-top: -20px;
}

.heading p{
	margin-bottom: 40px;
	color: #fff;
}

.scenario-count {
	position: absolute;
	top: 85px;
	left: 0;
	right: 0;
	margin: 0 auto;
}

.scenario-count ul {
    margin-bottom: 40px;
}

.scenario-count ul li {
  list-style: none;
  color: white;
  font-size: 14px;
  display: inline;
  border: 3px solid white;
  border-radius: 50%;
  padding: 6px 10px;
  font-weight: bold;
  margin: 0 50px;
}

.scenario-count ul li.selected {
    background-color: white;
    color: black;
}

.scenario-count ul li.active {
    background-color: white;
    color: black;
    padding: 16px 21px;
    margin: 0 40px;
}

.scenario-count li:after {
  border-top: 3px solid white;
  width: 95px;
  content: "";
  position: absolute;
  margin-top: 10px;
  margin-left: 16px;
}

.scenario-count ul > li:last-child:after {
    border: none;
}

.center-drop {
	position: absolute;
	top: 130px;
	left: 0;
	right: 0;
	margin: 0 auto;
	width: 400px;
	height: 400px;
	border-radius: 10px;
	margin: 30px auto;
	z-index: 3;
}

.center-drop.book1 {
	background: url(../img/stories/03_booksaboutjesus_interactivejigsaw_sprite.png) no-repeat center center;
	background-position: 0 -400px;
}

.center-drop.book1.win {
	background: url(../img/stories/03_booksaboutjesus_interactivejigsaw_sprite.png) no-repeat center center;
	background-position: 0 0;
}

.center-drop.book2 {
	background: url(../img/stories/03_booksaboutjesus_interactivejigsaw_sprite.png) no-repeat center center;
	background-position: 0 -1200px;
}

.center-drop.book2.win {
	background: url(../img/stories/03_booksaboutjesus_interactivejigsaw_sprite.png) no-repeat center center;
	background-position: 0 -800px;
}

.center-drop.book3 {
	background: url(../img/stories/03_booksaboutjesus_interactivejigsaw_sprite.png) no-repeat center center;
	background-position: 0 -2000px;
}

.center-drop.book3.win {
	background: url(../img/stories/03_booksaboutjesus_interactivejigsaw_sprite.png) no-repeat center center;
	background-position: 0 -1600px;
}

.drop-zone {
	height: 180px;
	width: 180px;
	border-radius: 50%;
	position: absolute;
	/*	border: 1px solid black;*/
}

.drop-zone.piece-01 {
	top: 7px;
	left: 6px;
}

.drop-zone.piece-02 {
	top: 6px;
	left: 213px;
}

.drop-zone.piece-03 {
	top: 214px;
	left: 6px;
}

.drop-zone.piece-04 {
	top: 215px;
	left: 213px;
}

.images-holder {
	position: absolute;
	background: #ce7f3e;
	background-repeat: no-repeat;
	width: 100%;
	height: 35%;
	border-bottom-right-radius: 40px;
	bottom: -20px;
}

.images-holder .image-container {
	position: relative;
	margin-top: 60px;
	display: inline-block;
	background: #c17238;
	width: 180px;
	height: 180px;
	border-radius: 50%;
}

.images-holder .image-container:not(:first-child) {
	margin-left: 30px;
}



.images-holder .image-container .image {

	-webkit-transition: all 0.1s ease-in-out;
	-moz-transition: all 0.1s ease-in-out;
	-o-transition: all 0.1s ease-in-out;
	transition: all 0.1s ease-in-out;
}

.images-holder .image-container img {
	position: absolute;
	top: 0;
	left: 0;
	width: 180px;
	height: 180px;
	border-radius: 50%;
	-webkit-transition: width 0.1s ease-in-out, height 0.1s ease-in-out;
	-moz-transition: width 0.1s ease-in-out, height 0.1s ease-in-out;
	-o-transition: width 0.1s ease-in-out, height 0.1s ease-in-out;
	transition: width 0.1s ease-in-out, height 0.1s ease-in-out;
	z-index: 3;
}


.image-container img.ui-draggable-dragging {
	border: 4px solid rgba(93,68,88,0.2);
	z-index: 4;
}


.images-holder .image-container img.dropped {
	border: 3px solid rgba(0,0,0,0.1);
}

.continue,
.continue2 {
	display: none;
	position: absolute;
	top: 575px;
	right: 420px;
	z-index: 3;
}

.win-screen,
.loose-screen,
.start-screen {
	display: none;
	z-index: 5;
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	overflow: hidden;
	background: rgba(0,0,0,0.5);
}

.start-screen {
	display: block;
}

.win-screen .container,
.loose-screen .container,
.start-screen .container {
	position: absolute;
	background: white;
	border-radius: 10px;
	width: 550px;
	height: 600px;
	top: 50%;
	margin-top: -320px;
	left: 50%;
	margin-left: -275px;
	padding: 30px 60px;
}

.win-screen .container,
.loose-screen .container {
	padding-top: 60px;
}

.start-screen .container {
	height: 700px;
	margin-top: -325px;
}

.start-screen .container h3 {
	margin-bottom: 30px;
}

.start-screen .container img {
	width: 120px;
	height: 120px;
	border-radius: 50%;
	margin-top: 15px;
	border: 3px solid rgba(0,0,0,0.1);
}

.start-screen .container img.rotate {
	-ms-transform: rotate(90deg); /* IE 9 */
	-webkit-transform: rotate(90deg); /* Chrome, Safari, Opera */
	transform: rotate(90deg);
}

.start-screen .container .start {
	margin-top: 30px;
}


.start-screen .container .divider {
	height: 2px;
	background: #d5dae0;
	margin: 30px 0;
}


.win-screen .container h2,
.loose-screen .container h2 {
	padding: 50px 0 40px 0;
	font-size: 42px;
}

.win-screen .container a,
.loose-screen .container a,
.start-screen .container a {
	width: 100%;
}

.container .win,
.container .loose {
	margin: 0 auto;
	border-radius: 50%;
	width: 180px;
	height: 180px;
	margin-top: 30px;
	margin-bottom: 30px;
}

.container .win {
	background: #79cee8;
}

.win-screen .container .win img {
	margin-top: 50px;
}

.container .big-btn {
	background: #ac88b5;
	border-bottom: solid 3px #816289;
}

.container .big-btn:hover {
	border-bottom: solid 3px #9a75a3;
}