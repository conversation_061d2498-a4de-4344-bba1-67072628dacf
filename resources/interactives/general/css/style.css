* {
    -moz-box-sizing: border-box;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
}

body,html {
    margin: 0px;
    padding: 0px;
    background-color: #dcd3ce;
    width: 100%;
    height: 100%;
    font-family: CambridgeRound-Light,"Helvetica Neue",Helvetica,Arial,sans-serif;
    font-size: 16px;
    line-height: 1.4;
    text-align: center;
}

.content {
    display: inline-block;
    width: 1024px;
    height: 768px;
    position: relative;
    background-color: #f1ece9;
    position: relative;
    margin: 90px 0px;
    padding: 0px 30px;
    background-image: url(../img/bg_cloud.png);
    background-position: top;
    background-repeat: no-repeat;  
}

.content:after {
    content: "";
    background-color: #f1ece9;   
    height: 40px;
    width: 100%;
    display: block;
    -webkit-border-radius: 0px;
    -webkit-border-top-left-radius: 40px;
    -moz-border-radius: 0px;
    -moz-border-radius-topleft: 40px;
    border-radius: 0px;
    border-top-left-radius: 40px;
    position: absolute;
    top: -40px;
    left: 0px;
}

.content:before {
    content: "";
    background-color: #f1ece9;
    height: 40px;
    width: 100%;
    display: block;
    -webkit-border-radius: 0px;
    -webkit-border-bottom-right-radius: 40px;
    -moz-border-radius: 0px;
    -moz-border-radius-bottomright: 40px;
    border-radius: 0px;
    border-bottom-right-radius: 40px;
    position: absolute;
    bottom: -40px;
    left: 0px;
}

.clearfix:after {
  content:"";
  display:table;
  clear:both;
}

h1 {
    color: #686262;
    margin-bottom: 0px;
}

.heading p {
    margin-top: 0px;
    margin-bottom: 27px;
    height: 50px;
    color: #686262;
}

.arrow {
    float: left;
    background-image: url(../img/arrow.png);
    background-position: center;
    background-repeat: no-repeat;
    width:100%;
    height: 134px;
}

.big-btn {
    background-color: #479dc0;
    color: #fff;
    padding: 15px 60px;
    font-size: 18px;
    -moz-border-radius: 4px;
    -webkit-border-radius: 4px;
    border-radius: 4px;
    border-bottom: solid 3px #516382;
    text-decoration: none;
    outline: none !important;
    cursor: pointer;
    display: inline-block;
}
.big-btn:hover {
    border-bottom: solid 3px #65748f;
}

.disabled {
    opacity: 0.5;
    cursor: default;
}

.cancelBtnHolder,
.confirmBtnHolder {
    bottom: 0;
    left: 0px;
    position: absolute;
    width: 100%;
    text-align: center;
}

.confirmBtnHolder {
    color: #816e81;
}

.cancelBtnHolder {
    left: auto;
    right: 0px;
    width: auto;
}

.cancelBtnHolder {
    text-align: right;
    padding-right: 12.5px;
    left: auto;
    right: 34px;
    width: auto;
    top: 0px;
    bottom: auto;
}

.cancelBtnHolder .big-btn {
    background-color: transparent;
    border: 2px solid #dbd0cb;
    color: #956f9e;
    background-image: url('../img/close_dark.png');
    background-position: center;
    background-repeat: no-repeat;
    width: 48px;
    height: 45px;
    padding: 0px;
     -webkit-transition: all 300ms ease;
    -moz-transition: all 300m sease;
    -o-transition: all 300ms ease;
    transition: all 300ms ease;    
}

.cancelBtnHolder .big-btn:hover {
    background-color: rgba(255,255,255,0.3);    
}