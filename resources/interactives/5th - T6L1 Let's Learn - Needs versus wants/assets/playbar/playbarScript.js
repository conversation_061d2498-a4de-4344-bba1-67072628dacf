cp.playbarAssetArr = 
[
	'AudioOff',
	'AudioOn',
	'BackGround',
	'Backward',
	'Color',
	'ColorSmall',
	'CC',
	'Exit',
	'FastForward',
	'FastForward1',
	'FastForward2',
	'Forward',
	'Glow',
	'GlowSmall',
	'Height',
	'InnerStroke',
	'InnerStrokeSmall',
	'Play',
	'Pause',
	'Progress',
	'Re<PERSON>',
	'Shade',
	'ShadeSmall',
	'Stroke',
	'StrokeSmall',
	'Thumb',
	'ThumbBase',
	'TOC'
];
cp.playbarTooltips = 
{
	AudioOff : 'Audio Off',
	AudioOn : 'Audio On',
	BackGround : 'BackGround',
	Backward : 'Backward',
	Color : 'Color',
	CC : 'CC',
	Exit : 'Exit',
	FastForward : '2x Fast Forward Speed',
	FastForward1 : '4x Fast Forward Speed',
	FastForward2 : 'Normal Speed',
	Forward : 'Forward',
	Glow : 'Glow',
	Height : 'Height',
	InnerStroke : 'InnerStroke',
	Play : 'Play',
	Pause : 'Pause',
	Progress : 'Progress',
	Rewind : 'Rewind',
	Shade : 'Shade',
	Stroke : 'Stroke',
	Thumb : 'Thumb',
	ThumbBase : 'ThumbBase',
	TOC : 'TOC'
};
cp.handleSpecialForPlaybar = function(playbarConstruct)
{
}