cp.playbarAssetArr = 
[
	'AudioOff',
	'AudioOn',
	'BackGround',
	'Backward',
	'Color',
	'ColorSmall',
	'CC',
	'Exit',
	'FastForward',
	'FastForward1',
	'FastForward2',
	'Forward',
	'Glow',
	'GlowSmall',
	'Height',
	'Play',
	'Pause',
	'Progress',
	'Rewind',
	'Stroke',
	'StrokeSmall',
	'Thumb',
	'ThumbBase',
	'TOC'
];
cp.playbarTooltips = 
{
	AudioOff : 'Audio Off',
	AudioOn : 'Audio On',
	BackGround : 'BackGround',
	Backward : 'Backward',
	Color : 'Color',
	CC : 'CC',
	Exit : 'Exit',
	FastForward : '2x Fast Forward Speed',
	FastForward1 : '4x Fast Forward Speed',
	FastForward2 : 'Normal Speed',
	Forward : 'Forward',
	Glow : 'Glow',
	Height : 'Height',
	InnerStroke : 'InnerStroke',
	Play : 'Play',
	Pause : 'Pause',
	Progress : 'Progress',
	Rewind : 'Rewind',
	Shade : 'Shade',
	Stroke : 'Stroke',
	Thumb : 'Thumb',
	ThumbBase : 'ThumbBase',
	TOC : 'TOC'
};
cp.responsiveButtons = 
{
	//"ButtonName"	: 	[Primary,Tablet,Mobile],
	"Rewind"		: 	[true,true,true,true,false],
	"Backward"		: 	[true,true,true,true,true],
	"Play"			: 	[true,true,true,true,true],
	"Slider"		: 	[true,true,true,true,false],
	"Forward"		: 	[true,true,true,true,true],
	"CC"			: 	[true,true,true,true,true],
	"AudioOn"		: 	[true,true,false,false,false],
	"Exit"			: 	[true,true,true,true,true],
	"FastForward"	: 	[true,true,true,true,false],
	"TOC"			: 	[true,true,true,true,false]
};
cp.handleSpecialForPlaybar = function(playbarConstruct)
{
}