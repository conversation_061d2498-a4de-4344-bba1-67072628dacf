body, html {
    background-color: #5D4463;
}

.content:before,
.content,
.content:after {
    background-color: #956f9e;
}

.heading p,
h1 {
    color: #fff;
}

.cancelBtnHolder .big-btn {
    background-image: url('../../general/img/close.png');
    border-color: #fff;
}

.window-holders {
  width: 100%;
  overflow-y: scroll;
  height: 82%;
  padding-top: 30px;
  top: -50px;
  position: relative;
}

.window {
    width: 215px;
    height: 150px;
    -webkit-perspective: 800px;
    -moz-perspective: 800px;
    -o-perspective: 800px;
    -ms-perspective: 800px;
    perspective: 800px;
    -webkit-transition: all 1s ease-in-out;
    -moz-transition: all 1s ease-in-out;
    -o-transition: all 1s ease-in-out;
    -ms-transition: all 1s ease-in-out;
    transition: all 1s ease-in-out;
    overflow: visible;
    float: left;
    margin-left: 2%;
    margin-bottom: 3%;
    border-radius: 4px;
}

.window p{
    color:  #ffffff;
    font-size: 20px;
    text-align: center;
}

.window .door{
    background: #fff;
    width: 215px;
    height: 150px;
    cursor: pointer;
    -webkit-transform-origin: 100% 40%;
    -moz-transform-origin: 100% 40%;
    -o-transform-origin: 100% 40%;
    -ms-transform-origin: 100% 40%;
    transform-origin: 100% 40%;
    -webkit-transition: all 1s ease-in-out;
    -moz-transition: all 1s ease-in-out;
    -o-transition: all 1s ease-in-out;
    -ms-transition: all 1s ease-in-out;
    transition: all 1s ease-in-out;
    -webkit-transform-style: preserve-3d;
    -moz-transform-style: preserve-3d;
    -o-transform-style: preserve-3d;
    -ms-transform-style: preserve-3d;
    transform-style: preserve-3d;
    border: 1px dashed #c2c0bf;
    border-radius: 4px;
}

.window .door img {
    width: 90%;
    top: 20%;
    position: relative;
}

.window .door p {
    position: absolute;
    border-radius: 50%;
    width: 40%;
    height: 60%;
    top: 20%;
    left: calc(50% - 50px);
    font-size: 40px;
    line-height: 90px;
    margin: 0;

}

.text-holder {
    line-height: 150px;
    position: absolute;
    width: 80%;
    padding-left: 10px;
}

.text-holder p {
    display: inline-block;
    vertical-align: middle;
    line-height: 24px;
    width: 100%;
}

.window.opened .door {
    -webkit-transform: rotate3d(0, 1, 0, 75deg);
    -moz-transform: rotate3d(0, 1, 0, 75deg);
    -o-transform: rotate3d(0, 1, 0, 75deg);
    -ms-transform: rotate3d(0, 1, 0, 75deg);
    transform: rotate3d(0, 1, 0, 75deg);
}

.window.blue {
    background-color: #8dd5eb;
}

.window.pink {
    background-color: #eb7f94;
    
}

.window.purple {
    background-color: #ac88b5;
}

.window.orange {
    background-color: #fabf84;
}

.window.blue .door p{
    background-color: #8dd5eb;
}

.window.pink .door p{
    background-color: #eb7f94;
}

.window.purple .door p{
    background-color: #ac88b5;
}

.window.orange .door p{
    background-color: #fabf84;
}