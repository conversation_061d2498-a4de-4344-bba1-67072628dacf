body, html {
    background-color: #479dc0;
}

.content,.content:before, .content:after {
    background-color: #79cee8;
}

h1 {
    color: #fff;
}

.cancelBtnHolder .big-btn {
    background-image: url('../../general/img/close.png');
    border-color: #fff;
}


.heading p{
    margin-bottom: 40px;
    color: #fff;
}

.images-holder .image-container {
    width: 200px;
    height: 200px;
    background-image: url(../../general/img/frame-grey.png);
    background-color: rgba(71, 157, 192, 0.8);
    border-radius: 50%;
    background-position: 50% 50%;
    background-repeat: no-repeat;
}

.images-holder .image-container img {
    width: 200px;
    height: 200px;
    border-radius: 50%;
    -webkit-transition: width 0.1s ease-in-out, height 0.1s ease-in-out;
    -moz-transition: width 0.1s ease-in-out, height 0.1s ease-in-out;
    -o-transition: width 0.1s ease-in-out, height 0.1s ease-in-out;
    transition: width 0.1s ease-in-out, height 0.1s ease-in-out;
    z-index: 1;
}

.image-container img.ui-draggable-dragging {
    height: 200px;
    width: 200px;
    border: 4px solid rgba(93,68,88,0.2);
    z-index: 3;
}

.image-container:first-child{
    float: left;
}

.image-container:last-child{
    float: right;
}

.images-holder .top {
    width: 50%;
    margin: 0 auto;
}

.images-holder .middle,
.images-holder .bottom  {
    width: 100%;
    margin: 0 auto;
    position: relative;
    top: -100px;
}

.images-holder .middle {
    margin-bottom: 50px;
    margin-top: 50px;
}


.images-holder .image-container img.dropped {
    border: 3px solid #479dc0;
    height: 150px;
    width: 150px;
    position: absolute;
}

.center-drop {
    width: 300px;
    height: 300px;
    background-color: #479dc0;
    border-radius: 50%;
    position: relative;
    left: calc(50% - 150px);
    top: -420px;
}


.center-drop .heart{
    background-image: url(../img/heart.png);
    position: absolute;
    height: 75px;
    background-repeat: no-repeat;
    width: 80px;
    top: calc(50% - 40px);
    left: calc(50% - 38px);
    -webkit-transition: all 0.1s ease-in-out;
    -moz-transition: all 0.1s ease-in-out;
    -o-transition: all 0.1s ease-in-out;
    transition: all 0.1s ease-in-out;
}

.center-drop.hovered .heart{
    width: 105px;
    height: 100px;
    background-size: 100% 100%;
    top: calc(50% - 50px);
    left: calc(50% - 52px);
}