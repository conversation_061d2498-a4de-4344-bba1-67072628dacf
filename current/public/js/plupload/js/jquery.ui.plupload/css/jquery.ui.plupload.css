/*
   Plupload
------------------------------------------------------------------- */

.plupload_button {cursor: pointer;}

.plupload_wrapper {
	font: normal 11px Verdana,sans-serif;
	width: 100%;
}

.plupload .plupload_container input {width: 98%;}
.plupload .plupload_filelist_footer {border-width: 1px 0 0 0}
.plupload .plupload_filelist_header {border-width: 0 0 1px 0}
div.plupload .plupload_file {border-width: 0 0 1px 0}
div.plupload div.plupload_header {border-width: 0 0 1px 0; position: relative;}

.plupload_file .ui-icon {
	cursor:pointer;	
}

.plupload_header_content {
	background-image: url('../img/plupload.png');
	background-repeat: no-repeat;
	background-position: 8px center;
	min-height: 56px;
	padding-left: 60px;
	position:relative;
}
.plupload_header_content_bw {background-image: url('../img/plupload-bw.png');}
.plupload_header_title {
	font: normal 18px sans-serif;
	padding: 6px 0 3px;
}
.plupload_header_text {font: normal 12px sans-serif;}

.plupload_filelist,
.plupload_filelist_content {
	border-collapse: collapse;
	margin: 0;
	padding: 0;
	width: 100%;
	-moz-user-select:none;
	-webkit-user-select:none;
	user-select:none;
}

.plupload_cell {padding: 8px 6px;}

.plupload_file {
	border-left: none;
	border-right: none;
}

.plupload .ui-sortable-helper,
.plupload .ui-sortable .plupload_file {
	cursor:move;	
}

.plupload_scroll {
	max-height: 180px;
	min-height: 168px;
	_height: 168px;
	overflow-y: auto;
}

.plupload_file_size, .plupload_file_status {text-align: right;}
.plupload_file_size, .plupload_file_status {width: 52px;}
.plupload_file_action {width: 16px;}
.plupload_file_name {
	overflow: hidden;
	padding-left: 10px;
}

.plupload_file_rename {
	width:95%;	
}

.plupload_progress {width: 60px;}
.plupload_progress_container {padding: 1px;}


/* Floats */

.plupload_right {float: right;}
.plupload_left {float: left;}
.plupload_clear,.plupload_clearer {clear: both;}
.plupload_clearer, .plupload_progress_bar {
	display: block;
	font-size: 0;
	line-height: 0;
}
.plupload_clearer {height: 0;}

/* Misc */
.plupload_hidden {display: none;}
.plupload_droptext {
	background: transparent;
	text-align: center;
	vertical-align: middle;
	border: 0;
	line-height: 165px;
}

.plupload_buttons, .plupload_upload_status {float: left}

.plupload_message {
	position: absolute;
	top: 0px;
	left: 0px;	
	height: 100%;
	width: 100%;
}

.plupload_message p {
	padding:0.7em;
	margin:0;
}

.plupload_message strong {
	font-weight: bold;	
}

plupload_message i {
	font-style: italic;	
}

.plupload_message p span.ui-icon {
	float: left;
	margin-right: 0.3em;	
}

.plupload_header_content .ui-state-error,
.plupload_header_content .ui-state-highlight {
	border:none;	
}

.plupload_message_close {
	position:absolute;
	top:5px;
	right:5px;
	cursor:pointer;	
}

.plupload .ui-sortable-placeholder {
	height:35px;
}
