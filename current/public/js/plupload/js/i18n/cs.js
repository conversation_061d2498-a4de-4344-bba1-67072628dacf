// .po file like language pack
plupload.addI18n({
	'Select files' : '<PERSON><PERSON><PERSON><PERSON> soubory',
	'Add files to the upload queue and click the start button.' : '<PERSON>řidejte soubory do fronty a pak spusťte nahrávání.',
	'Filename' : '<PERSON><PERSON><PERSON><PERSON> souboru',
	'Status' : 'Status',
	'Size' : '<PERSON>elikos<PERSON>',
	'Add Files' : '<PERSON><PERSON><PERSON><PERSON> soubory',
	'Stop current upload' : 'Zastavit nahrávání',
	'Start uploading queue' : 'Spustit frontu nahrávání',
	'Drag files here.' : 'Sem přetáhněte soubory.',
	'Start Upload': 'Spustit nahrávání',
	'Uploaded %d/%d files': 'Nahráno %d/%d souborů'
});