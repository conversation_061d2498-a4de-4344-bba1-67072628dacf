Array.prototype.clone = function() {
    return this.slice(0);
};

$(function() {
    var topPictures = [
        "img/stories/ir_wheredoibelong_matching-1a.png",
        "img/stories/ir_wheredoibelong_matching-2a.png",
        "img/stories/ir_wheredoibelong_matching-3a.png",
        "img/stories/ir_wheredoibelong_matching-4a.png",
        "img/stories/ir_wheredoibelong_matching-5a.png",
    ];

    var bottomPictures = [
        "img/stories/ir_wheredoibelong_matching-1b.png",
        "img/stories/ir_wheredoibelong_matching-2b.png",
        "img/stories/ir_wheredoibelong_matching-3b.png",
        "img/stories/ir_wheredoibelong_matching-4b.png",
        "img/stories/ir_wheredoibelong_matching-5b.png",
    ];

    var audiotypes={
        "mp3": "audio/mpeg",
        "mp4": "audio/mp4",
        "ogg": "audio/ogg",
        "wav": "audio/wav"
    };


    function ss_soundbits(sound, loop){
        sound = '/interactives/whoami/sounds/' + sound;
        
        var audio_element = document.createElement('audio');
        if (audio_element.canPlayType){
            for (var i=0; i === 0 && i<arguments.length; i++){
                for(var key in audiotypes) {
                    var source_element = document.createElement('source');
                    source_element.setAttribute('src', arguments[i] + "." + key);
                    source_element.setAttribute('type', audiotypes[key]);
                    audio_element.appendChild(source_element);
                }
            }
            
            //audio_element.play()
            audio_element.playclip=function(){
                this.pause();
                //this.currentTime=0
                this.play();
            };
            
            if(loop) {
                audio_element.addEventListener('ended', function() {
                    ss_soundbits(sound.replace('/interactives/whoami/sounds/', ''), loop).playclip();
                }, false);
            }
            
            return audio_element;
        }
    }

    function shuffleTopImages() {
        var o = topPictures.clone();
            
        for(var j, x, i = o.length; i; j = Math.floor(Math.random() * i), x = o[--i], o[i] = o[j], o[j] = x);

        $(".top-stories .story").each(function (i, item) {
            $(this).on('click', function() {
                var topImageId = '';
                var bottomImageId = '';
                //is matched
                if($(this).hasClass('matched')) {
                    topImageId = $(this).attr('image');
                    $.each(matches, function(a){
                        if(matches[a].topImage === topImageId) {
                            bottomImageId = matches[a].bottomImage;
                            matches.splice(a,1);
                            return false;
                        }
                    });


                    $(this).removeClass('matched');
                    $(".bottom-stories .story[image='" + bottomImageId + "']").removeClass('matched');
                    $(".story").removeClass('active');
                    $(this).addClass('active');
                //is active
                } else if($(this).hasClass('active')){
                   $(this).removeClass('active');
                //is not active or matched
                } else {
                    //set as active
                    $(".top-stories .story").removeClass('active');
                    $(this).addClass('active');

                    //if top is also selected
                    if($(".bottom-stories .active")[0]){
                        var bottomStory = $(".bottom-stories .active");
                        var topStory = $(this);
                        
                        //create a match
                        topImageId = topStory.attr('image');
                        bottomImageId =  bottomStory.attr('image');

                        if (topImageId == bottomImageId) {

                            //animate move
                            moveElement(topStory);
                            moveElement(bottomStory);

                            if(topStory.hasClass('purple')) {
                                bottomStory.addClass('purple');
                            } else if(topStory.hasClass('pink')) {
                                bottomStory.addClass('pink');
                            } else if(topStory.hasClass('blue')) {
                                bottomStory.addClass('blue');
                            } else if(topStory.hasClass('darkblue')) {
                                bottomStory.addClass('darkblue');
                            } else if(topStory.hasClass('orange')) {
                                bottomStory.addClass('orange');
                            }

                            bottomStory.addClass('matched');
                            bottomStory.removeClass('active');

                            setTimeout(function () {
                                topStory.addClass('matched');
                                topStory.removeClass('active');
                            }, 500);
                            
                            ss_soundbits('win').playclip();

                        } else {
                            ss_soundbits('negative').playclip();
                        }
                    }
                }
                
            });
            $(this).find("img").first().attr("src", o[i]).attr('original-image', o[i]);
            var index = topPictures.indexOf(o[i]);
            $(this).attr('image', index);
        });
    }

    function shuffleBottomImages() {
        var o = bottomPictures.clone();
        for(var j, x, i = o.length; i; j = Math.floor(Math.random() * i), x = o[--i], o[i] = o[j], o[j] = x);
    
        $(".bottom-stories .story").each(function (i, item) {
            $(this).on('click', function() {
                var topImageId = '';
                var bottomImageId = '';
                //is matched
                if($(this).hasClass('matched')) {
                    bottomImageId = $(this).attr('image');
                    $.each(matches, function(a){
                        if(matches[a].bottomImage === bottomImageId) {
                            topImageId = matches[a].topImage;
                            matches.splice(a,1);
                            return false;
                        }
                    });

                    $(this).removeClass('matched');
                    $(".top-stories .story[image='" + topImageId + "']").removeClass('matched');
                    $(".story").removeClass('active');
                    $(this).addClass('active');

                    if($(this).hasClass('purple')) {
                        $(this).removeClass('purple');
                    } else if($(this).hasClass('pink')) {
                        $(this).removeClass('pink');
                    } else if($(this).hasClass('blue')) {
                        $(this).removeClass('blue');
                    } else if($(this).hasClass('darkblue')) {
                        $(this).removeClass('darkblue');
                    } else if($(this).hasClass('orange')) {
                        $(this).removeClass('orange');
                    }
                //is active
                } else if($(this).hasClass('active')){
                   $(this).removeClass('active');


                //is not active or matched
                } else {
                    //set as active
                    $(".bottom-stories .story").removeClass('active');
                    $(this).addClass('active');

                    //if top is also selected
                    if($(".top-stories .active")[0]){
                        var topStory = $(".top-stories .active");
                        var bottomStory = $(this);
                        
                        //create a match
                        topImageId = bottomStory.attr('image');
                        bottomImageId =  topStory.attr('image');

                        if (topImageId == bottomImageId) {

                            //animate move
                            moveElement(bottomStory);
                            moveElement(topStory);


                            if(topStory.hasClass('purple')) {
                                bottomStory.addClass('purple');
                            } else if(topStory.hasClass('pink')) {
                                bottomStory.addClass('pink');
                            } else if(topStory.hasClass('blue')) {
                                bottomStory.addClass('blue');
                            } else if(topStory.hasClass('darkblue')) {
                                bottomStory.addClass('darkblue');
                            } else if(topStory.hasClass('orange')) {
                                bottomStory.addClass('orange');
                            }

                            bottomStory.removeClass('active');
                            bottomStory.addClass('matched');
                            
                            setTimeout(function() {
                                topStory.addClass('matched');
                                topStory.removeClass('active');
                            }, 500);      
                            
                            ss_soundbits('win').playclip();

                        } else {
                            ss_soundbits('negative').playclip();
                        }                                          
                    }
                }
                
            });
            $(this).find("img").first().attr("src", o[i]).attr('original-image', o[i]);
            var index = bottomPictures.indexOf(o[i]);
            $(this).attr('image', index);
        });
    }

    function moveElement(el) {
        // all the LIs above the el
        var previousAll = el.prevAll();

        // only proceed if it's not already on top (no previous siblings)
        if(previousAll.length > 0) {
            // top LI
            var left = $(previousAll[previousAll.length - 1]);

            // immediately previous LI
            var previous = $(previousAll[0]);

            // how far up do we need to move the el
            var moveUp = el.attr('offsetLeft') - left.attr('offsetLeft');

            // how far down do we need to move the previous siblings?
            var moveDown = (el.offset().left + el.outerWidth()) - (previous.offset().left + previous.outerWidth());

            // let's move stuff
            el.css('position', 'relative');
            previousAll.css('position', 'relative');
            el.animate({'left': -moveUp});
            previousAll.animate({'left': moveDown}, {complete: function() {
              // rearrange the DOM and restore positioning when we're done moving
              el.parent().prepend(el);
              el.css({'position': 'relative', 'left': 0});
              previousAll.css({'position': 'relative', 'left': 0}); 
            }});
        }
    }


    $(document).ready(function () {
        shuffleTopImages();
        shuffleBottomImages();
    });
});