
@media( min-width: 980px ) { .container { width: 980px; margin: 0 auto; } }
@media( min-width: 1200px ) { .container { width: 1170px; margin: 0 auto; } }

@media( min-width: 980px ) { .half-container { width: 100%; height: 100%; margin: 0 auto; &.left { } &.right { } } }
@media( min-width: 1200px ) { .half-container { width: 100%; height: 100%; margin: 0 auto; } }

@media( min-width: 1px ) { .page-container { margin: 0 15px; } }
@media( min-width: 768px ) { .page-container { margin: 0 30px; } }
@media( min-width: 980px ) { .page-container { width: 820px; margin: 0 auto; &.left { } &.right { } } }

@media( min-width: 1px ) and (max-width: 970px ){
     #mobile-menu {
          position: relative;
          bottom: 79px;
          width: 20%;
          left: 72%;
          .select-link {
               -webkit-appearance: none;
               appearance: none;
               width: 100%;
               padding: 10px;
               border: none;
               color: #fff;
               background-color: #fff;
               background-image: url('../image/menu-icon.png');
               background-repeat: no-repeat;
               background-position: 100% center;
               background-size: 24%;
          }
          option{
               color: $grey !important;
               padding: 5px;
          }
     }
     .form-wrap {
          margin: 0 15px;
     }
     .field-wrap-item {
          margin-bottom: 20px;
          position: relative;
          overflow: hidden;
          label {
               left: 10px;
          }
     }
     nav {
          li {
               width: 100%;
          }
          .logo-wrap {
               padding: 15px 0;
          }
          .menu-item {
               display: none;
          }
          .menu-wrap{
               display: none;
          }
     }
     .text{
          text-align: center;
     }
     .half-container{
          width: 100%;
     }
     #mobile-menu {
          display: block;
     }
     .login-wrap {
          background-position: 25% center;

          .login {
               width: calc(100% - 97px);
               padding: 0!important;
          }
          #register-btn, #login-btn{
               height: 79px;
               width: 50% !important;
               margin: 0 !important;
               border-radius: 0;
          }
          #register-btn{
               background-color: $blue;
               border-color: $blue;
               color: $white;
          }
          .member-notice {
               right: 75%;
          }

          .follow-us{
               display: none;
          }
          .qtranxs_widget, .language-switcher{
               margin: 0;
          }

     }
     #intro-panel{
          height: auto !important;
          position: relative;
          padding-bottom: 20px;
          top: 10px;
          .graphic img{
               margin: 0 auto;
          }
          .btn{
               position: relative;
               top: 350px;
          }
          .slick-dots{
               position: relative;
               top: 200px;
          }
          .text, .text-body{
               height: auto !important;
          }
     }
     #first-panel {
          .text {
               padding-right: 0px;
          }
     }
     #second-panel{
          position: relative;
          top: 337px;
          .text{
               position: relative;
               bottom: 300px;
          }
          .graphic{
               position: relative;
               top: 300px;
               img{
                    top: 55px !important;
               }
          }
     }

     #first-panel, #second-panel{
          height: 300px;
          overflow-y: visible !important;
     }

     .home-panel{
          .graphic {
               display: block;
               img{
                    top: 46px !important;
                    width: 310px !important;
               }
          }
          .text{
               padding-top: 40px !important;
          }
          .text-body{
               padding: 0 25px;
               margin-bottom: 30px;
               font-size: 17px;
               line-height: 30px;
               height: auto !important;
          }
          .text-spacer{
               margin: 0 auto 22px !important;
               height: 3px;
          }
     }

     #third-panel{
          height: auto !important;
          position: relative;
          top: 637px;
          padding-bottom: 50px;
          .text-body{
               margin-bottom: 20px;
               height: auto !important;
          }
          .btn{
               margin-top: 20px;
          }
          .text{
               padding-top: 70px;
          }
     }

     .btn-wide{
          width: 90% !important;
     }
     h1{
          font-size: 40px !important;
          width: 85% !important;
          margin: 0 auto 20px auto !important;
     }
     h2{
          font-size: 32px !important;
          margin: 0 auto 20px auto !important;
     }
     .btn{
          font-size: 20px !important;
     }
     .slick-list{
          overflow: visible;
     }
}

@media( min-width: 970px ) {
     #mobile-menu {
          display: none;
     }
     nav {
          a {
               padding-right: 5px;
               padding-left: 5px;
          }
     }

     nav {
          .menu-item {
               display: block;
          }
     }

     .home-panel {
          .graphic, .text {
               width: 50%;
               float: left;
          }
          .graphic img{
               max-width: 500px;
          }
          .graphic {
               display: block;
          }
          .text {
               text-align: left;
          }
          .text-body{
               width: 60%;
               margin: 0 auto;
          }
     }
     .home-panel-wide {
          .text {
               text-align: center;
               width: 100%;
               display: block;
          }
          .text-body{
               max-width: 700px;
               margin: 0 auto;
          }
     }
     #intro-panel{
          .text-body{
               width: 100%;
          }
          .half-container{
               width: 500px;
          }
          height: 630px;
          .graphic img{
               width: 60%;
          }
          .carousel-pane-one{
               .graphic{
                    img{
                         top: 140px;
                         left: 20px;
                         width: 100%;
                    }
               }
          }
          .carousel-pane-two{
               .graphic{
                    img{
                         top: 100px;
                         left: 20px;
                         width: 100%;
                    }
               }
          }
          .btn{
               position: static;
          }
     }
     .login-wrap {
          .member-notice {
               line-height: 1.5em;
               top: 25px;
               font-size: 1em;
               padding-right: 5px;
               padding-left: 10px;
          }

          .language-switcher {
               ul {
                    li {
                         display:block;
                    }
               }
          }
     }
     .login-wrap {
          background-position: center center;
          .login {
               width: 50%;
          }
          .member-notice {
               right: 50%;
          }
     }
     #footer, #second-panel, #third-panel{
          position: static;
          .text, .graphic{
               position: static;
          }
     }
     .btn-wide{
          width: 200px !important;
     }
     .text{
          padding: 0;
     }
     .text-body{
          padding: 0;
     }
}

@media( min-width: 1250px ) {
     #intro-panel{
          .half-container.right{
               position: relative;
               right: 120px;
               img{
                    width: 110%;
               }
          }
     }
}

@media( min-width: 1400px ) {
     #intro-panel{
          .half-container.right{
               position: relative;
               img{
                    width: 120%;
               }
          }
     }
}


@media( max-width: 1400px ) {
     .slick-next, .slick-prev{
          display: none !important;
     }
}

