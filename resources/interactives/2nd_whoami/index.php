<!DOCTYPE html>
<html>
<head>
    <link rel="stylesheet" type="text/css" href="/interactives/general/css/style.css"/>
    <link rel="stylesheet" type="text/css" href="css/jquery-ui.min.css" />
    <link rel="stylesheet" type="text/css" href="css/style.css" />
</head>
<body>
<script>
    (function(i,s,o,g,r,a,m){i['GoogleAnalyticsObject']=r;i[r]=i[r]||function(){
            (i[r].q=i[r].q||[]).push(arguments)},i[r].l=1*new Date();a=s.createElement(o),
        m=s.getElementsByTagName(o)[0];a.async=1;a.src=g;m.parentNode.insertBefore(a,m)
    })(window,document,'script','https://www.google-analytics.com/analytics.js','ga');

    ga('create', 'UA-2014879-79', 'auto');
    ga('send', 'pageview');

</script>

<?php
$configString = file_get_contents("config.json");
$config = json_decode($configString);
$title = $config->title;
$description = $config->description;
?>

<div class='content'>
    <div class='heading'>
        <h1><?php echo $title; ?></h1>
        <p><?php echo $description; ?></p>
    </div>

    <div class='top-stories'>
        <div class='story first purple'>
            <img class="audio_icon" src='img/audio_purple.png'/>
            <div class="color_block"></div>
            <div class="audio_btn">
                <div class="button_bg"></div>
            </div>
        </div>
        <div class='story blue'>
            <img class="audio_icon" src='img/audio_blue.png'/>
            <div class="color_block"></div>
            <div class="audio_btn">
                <div class="button_bg"></div>
            </div>
        </div>
        <div class='story orange'>
            <img class="audio_icon" src='img/audio_orange.png'/>
            <div class="color_block"></div>
            <div class="audio_btn">
                <div class="button_bg"></div>
            </div>
        </div>
        <div class='story pink'>
            <img class="audio_icon" src='img/audio_pink.png'/>
            <div class="color_block"></div>
            <div class="audio_btn">
                <div class="button_bg"></div>
            </div>
        </div>
        <div class='story last darkblue'>
            <img class="audio_icon" src='img/audio_darkblue.png'/>
            <div class="color_block"></div>
            <div class="audio_btn">
                <div class="button_bg"></div>
            </div>
        </div>
    </div>

    <div class='spacer'>
        <canvas id="lines" style="width: 100%;height: 150px;" />
    </div>

    <div class='bottom-stories'>
        <div class='story first story1'>
            <img src='' />
        </div>
        <div class='story story2'>
            <img src='' />
        </div>
        <div class='story story3'>
            <img src='' />
        </div>
        <div class='story story4'>
            <img src='' />
        </div>
        <div class='story last story5'>
            <img src='' />
        </div>
    </div>

    <?php
    $referer = isset($_SERVER['HTTP_REFERER']) ? $_SERVER['HTTP_REFERER'] : '';

    if($referer != '') {?>
        <div class='cancelBtnHolder'>
            <a class='big-btn' onclick="window.close(); return false;" href="<?= $referer ?>"></a>
        </div>
    <?php } ?>
</div>

<script type="text/javascript" src="js/jquery-1.11.2.min.js"></script>
<script type="text/javascript" src="js/jquery-ui.min.js"></script>
<script type="text/javascript" src="js/script.js"></script>
<script type="text/javascript" src="/interactives/general/js/script.js"></script>
</body>
</html>