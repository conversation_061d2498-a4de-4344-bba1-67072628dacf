$(document).ready(function(){function e(e){$(e||".resources .resource-wrap").each(function(){""==n||$(this).find(".title a").first().text().toLowerCase().indexOf(n.toLowerCase())>=0?$(this).show():$(this).hide()})}function i(e){if(!$(e).data("animating")){$(e).data("animating",!0);var t=function(i,n){++i>=a&&(i=0,n++),$(e).css("background-position",0+200*i+"px 0px"),setTimeout(function(){t(i,n)},50)};setTimeout(function(){t(0,1)},1e3*Math.random())}}var n="",s=function(i){var n=$.getJSON("/user/book-lesson/"+i),s=new t($("#bookItem").html()),o=new t($("#vidItem").html()),r=new t($("#linkItem").html()),a=new t($("#interactiveItem").html());html="",n.then(function(t){t.slideshows&&$.each(t.slideshows,function(e,t){t.type="slideshow",t.imagePath="slideshow",console.log(t);var i=s.render(t);html+=i}),t.stories&&$.each(t.stories,function(e,t){t.type="story",t.imagePath="storie",console.log(t);var i=s.render(t);html+=i}),t.illutrations&&$.each(t.illutrations,function(e,t){t.type="illustration",t.imagePath="illustration",console.log(t);var i=s.render(t);html+=i}),t.videos&&$.each(t.videos,function(e,t){t.type="video",t.imagePath="video";var i=o.render(t);html+=i}),t.links&&$.each(t.links,function(e,t){t.type="link",t.imagePath="interactive";var i=r.render(t);html+=i}),t.interactives&&$.each(t.interactives,function(e,t){t.type="interactive",t.imagePath="interactive";var i=a.render(t);html+=i}),$(".resources .wrap").empty().html(html),e()})},o=function(t){t.preventDefault();var i=$(t.currentTarget).data("type"),n=$(".resources .wrap").children();if("all"===i)return n.show(),void e();n.hide(),$('.resource-wrap[data-resource="'+i+'"]').show(),e('.resource-wrap[data-resource="'+i+'"]')};$(".filters").find("a").on("click",function(e){o(e)}),$(".search-field").keyup(function(){n=$(this).val(),e()}),$(".select-theme").click(function(e){e.stopPropagation(),e.preventDefault(),$(this).data("selected")||($(".select-theme").removeClass("choice-selected").removeClass("selected"),$(this).addClass("choice-selected").addClass("selected"),$(".choice-select-lessons").hide(),$(".choice-select-lessons[data-theme='"+$(this).attr("data-theme")+"']").show(),$(".choice-select-lessons[data-theme='"+$(this).attr("data-theme")+"']").find(".choice-select-lesson").first().click(),$(".select-theme").each(function(){$(this).data("selected",!1)}),$(this).data("selected",!0))}),$(".choice-select-lesson").click(function(e){e.stopPropagation(),e.preventDefault(),$(this).data("selected")||($(".choice-select-lesson").closest(".choice-select-lessons").find(".choice-select-lesson").removeClass("selected"),$(this).addClass("selected"),$(".choice-select-lesson").closest(".choice-select-lessons").find(".choice-select-lesson").each(function(){$(this).data("selected",!1)}),$(this).data("selected",!0),s($(this).attr("data-lesson")))}),$(".select-theme").first().click(),$(".resources").on("mouseenter",".resource",function(){$(this).find(".open-story, .open-video","open-link").fadeIn(100)}).on("mouseleave",".resource",function(){$(this).find(".open-story, .open-video","open-link").fadeOut(100)}),$(".resources").on("mouseenter",".resource",function(){$(this).find(".open-slideshow, .open-video","open-link").fadeIn(100)}).on("mouseleave",".resource",function(){$(this).find(".open-slideshow, .open-video","open-link").fadeOut(100)});$(".resources").on("click",".open-story",function(e){e.preventDefault();var i=$(e.currentTarget),n=i.data("sliderid"),s=i.data("bookid"),o=$.getJSON("/user/book-stories-slides/"+n),r=new t($("#sliderStoryTemp").html());container=4==s?$("<div />").addClass("slider-container-purple").hide():$("<div />").addClass("slider-container").hide(),o.then(function(e){var t=r.render({images:e});container.append(t).appendTo("body").fadeIn(300);var i=function(){var e=$(window).width()-100,t=$(window).height()-100;container.find(".slider-modal, .slider-modal .slider, .slider-modal .slide").width(e).height(t)};4==s&&$(".caption").css("background-color","#755080"),$(window).on("resize",i),i(),container.find(".slider").slick({autoplay:!1,autoplaySpeed:1e3,infinite:!1,prevArrow:".slider-previous",nextArrow:".slider-next",fade:!0}),container.on("click",".close",function(){container.fadeOut(300,function(){container.remove()})})})});$(".resources").on("click",".open-slideshow",function(e){e.preventDefault();var i=$(e.currentTarget),n=i.data("sliderid"),s=i.data("bookid"),o=$.getJSON("/user/book-newslideshow-slides/"+n),r=new t($("#sliderSlideshowTemp").html());container=4==s?$("<div />").addClass("slider-container-purple").hide():$("<div />").addClass("slider-container").hide(),o.then(function(e){var t=r.render({images:e});container.append(t).appendTo("body").fadeIn(300);var i=function(){var e=$(window).width()-100,t=$(window).height()-100;container.find(".slider-modal, .slider-modal .slider, .slider-modal .slide").width(e).height(t)};4==s&&$(".caption").css("background-color","#755080"),$(window).on("resize",i),i(),container.find(".slider").slick({autoplay:!1,autoplaySpeed:1e3,infinite:!1,prevArrow:".slider-previous",nextArrow:".slider-next",fade:!0}),container.on("click",".close",function(){container.fadeOut(300,function(){container.remove()})})})});var r=function(e){return this.form=e,this.temp=['<div class="title"><h2>{{=title}}</h2></div>','<div class="prayer"><p>{{=prayer}}</p></div>'].join(""),this.template=new t(this.temp),this.candleTemplate=new t($("#candleTemp").html()),this.resp="",this};r.prototype={stepOne:function(){var e=this;$.post("/user/candle",this.form.serialize()).then(function(t){if(!t.success)return e.error();e.resp=t,e.stepTwo()})},stepTwo:function(){var e=this;$(".states").find(".step-two").addClass("active"),$(".prayer-form").fadeOut(300,function(){$(".animated-glow").fadeIn();var t=e.template.render(e.resp);$(".review-window .review-wrap").append(t).parent().fadeIn()}),$(".btn-see-prayers").on("click",$.proxy(this.stepThree,this))},stepThree:function(){var e=this;$(".states").find(".step-three").addClass("active"),$.getJSON("/user/candles-mix/"+this.resp.id).then(function(t){var i=[],n="",s=t.user[0];s.hide_delete_link="",$.each(t.random,function(e,t){t.hide_delete_link="hidden",2===e?(i.push(t),i.push(s)):i.push(t)}),i.length<2&&i.push(s),$.each(i,function(t,i){var s=moment().diff(moment(i.created_at),"days");i.days=s,n+=e.candleTemplate.render(i)}),$(".candles-page").append(n),$(".setup").fadeOut(function(){$(".candles-page").fadeIn(function(){$(this).children().each(function(e){$(this).delay(500*e).fadeIn(300)})})}),$(".candles-page").on("mouseenter",".hover-element",function(){$(this).parent().find(".prayer").fadeIn()}),$(".candles-page").on("mouseleave",".candle-holder",function(){$(this).find(".prayer").fadeOut()})})},error:function(){$("<div />").addClass("error").text(Lang.get("messages.js.err")).appendTo(this.form.find(".form-wrap-error").empty())}},$("#candleForm").unbind("submit"),$("#candleForm").on("submit",function(e){e.preventDefault(),$(this).parsley().validate()&&new r($(this)).stepOne()});$(".open-prayer").on("click",function(e){var i=$(e.currentTarget).data("id"),n=$.getJSON("/user/prayers/"+i),s=new t($("#prayerTemp").html());n.then(function(e){var t=s.render(e[0]);$("body").append(t).hide().fadeIn()})}),$("body").on("click",".close-prayer",function(){$(this).parent().fadeOut()}),$(".prayers").on("mouseenter",".prayer",function(){$(this).find(".open-prayer").fadeIn(100)}).on("mouseleave",".prayer",function(){$(this).find(".open-prayer").fadeOut(100)});var a=20;setInterval(function(){$(".animated-glow").each(function(){i(this)})},80),$("body").on("click",".delete-candle",function(e){e.preventDefault();var t=$(this).attr("candle-id");window.confirm(Lang.get("messages.js.del_confirm"))&&$.post("/user/candle/delete",{candle:t}).then(function(e){e.success&&(location.href="/user/candle")})})}),function(e,t,i,n){function s(t,i){this.element=t,this.options=e.extend({},o,i),this.init()}var o={selected:function(){}};s.prototype.init=function(){var t=e(this.element),n=t.children(),s=t.wrap('<div class="Choice" />').parent(),o=e("<input />").attr({name:t.attr("name"),type:"hidden"}).appendTo(s),r=e(s).append('<ul class="ListChoice" />').find("ul").hide(),a=s.prepend('<span class="ChoiceCurrent" />').find("span"),l=n.eq(0).val()||n.eq(0).text(),d=n.eq(0).text(),u=this;t.remove(),a.text(d),e(o).val(l),n.each(function(t){e(this).attr("selected")?(e(r).append('<li data-item="'+t+' ">'+e(this).text()+"</li>"),a.text(e(this).text()),e(o).val(e(this).val()||e(this).text())):e(r).append('<li data-item="'+t+' ">'+e(this).text()+"</li>")}),a.click(function(){r.toggle()}),r.find("li").click(function(){var t=e(this).data("item"),i=n.eq(t).val()||n.eq(t).text(),s=n.eq(t).text();e(o).val(i),a.text(s),r.hide(),u.options.selected(t,i)}),e(i).mouseup(function(e){0===s.has(e.target).length&&s.find(r).hide()})},e.fn.choice=function(t){return this.each(function(){e.data(this,"plugin_choice")||e.data(this,"plugin_choice",new s(this,t))})}}(jQuery,window,document),$.fn.setAllToMaxHeight=function(){return this.height(Math.max.apply(this,$.map(this,function(e){return $(e).height()})))},function(){function e(e){this.t=e}function t(e,t){for(var i=t.split(".");i.length;){if(!(i[0]in e))return!1;e=e[i.shift()]}return e}function i(e,o){return e.replace(n,function(e,n,s,r,a,l,d,u){var c,h="";if(!(r=t(o,r)))return"!"==s?i(a,o):d?i(u,o):"";if(!s)return i(l,o);if("@"==s){e=o._key,n=o._val;for(c in r)r.hasOwnProperty(c)&&(o._key=c,o._val=r[c],h+=i(a,o));return o._key=e,o._val=n,h}}).replace(s,function(e,i,n){return(e=t(o,n))||0===e?"%"==i?new Option(e).innerHTML.replace(/"/g,"&quot;"):e:""})}var n=/\{\{(([@!]?)(.+?))\}\}(([\s\S]+?)(\{\{:\1\}\}([\s\S]+?))?)\{\{\/\1\}\}/g,s=/\{\{([=%])(.+?)\}\}/g;e.prototype.render=function(e){return i(this.t,e)},window.t=e}(),function(e){"use strict";"function"==typeof define&&define.amd?define(["jquery"],e):e(jQuery)}(function(e){"use strict";var t=window.Slick||{};(t=function(){var t=0;return function(i,n){var s,o,r=this;if(r.defaults={accessibility:!0,appendArrows:e(i),arrows:!0,asNavFor:null,prevArrow:'<button type="button" data-role="none" class="slick-prev">Previous</button>',nextArrow:'<button type="button" data-role="none" class="slick-next">Next</button>',autoplay:!1,autoplaySpeed:3e3,centerMode:!1,centerPadding:"50px",cssEase:"ease",customPaging:function(e,t){return'<button type="button" data-role="none">'+(t+1)+"</button>"},dots:!1,dotsClass:"slick-dots",draggable:!0,easing:"linear",fade:!1,focusOnSelect:!1,infinite:!0,lazyLoad:"ondemand",onBeforeChange:null,onAfterChange:null,onInit:null,onReInit:null,pauseOnHover:!0,pauseOnDotsHover:!1,responsive:null,rtl:!1,slide:"div",slidesToShow:1,slidesToScroll:1,speed:300,swipe:!0,touchMove:!0,touchThreshold:5,useCSS:!0,vertical:!1},r.initials={animating:!1,dragging:!1,autoPlayTimer:null,currentSlide:0,currentLeft:null,direction:1,$dots:null,listWidth:null,listHeight:null,loadIndex:0,$nextArrow:null,$prevArrow:null,slideCount:null,slideWidth:null,$slideTrack:null,$slides:null,sliding:!1,slideOffset:0,swipeLeft:null,$list:null,touchObject:{},transformsEnabled:!1},e.extend(r,r.initials),r.activeBreakpoint=null,r.animType=null,r.animProp=null,r.breakpoints=[],r.breakpointSettings=[],r.cssTransitions=!1,r.paused=!1,r.positionProp=null,r.$slider=e(i),r.$slidesCache=null,r.transformType=null,r.transitionType=null,r.windowWidth=0,r.windowTimer=null,r.options=e.extend({},r.defaults,n),r.originalSettings=r.options,(s=r.options.responsive||null)&&s.length>-1){for(o in s)s.hasOwnProperty(o)&&(r.breakpoints.push(s[o].breakpoint),r.breakpointSettings[s[o].breakpoint]=s[o].settings);r.breakpoints.sort(function(e,t){return t-e})}r.autoPlay=e.proxy(r.autoPlay,r),r.autoPlayClear=e.proxy(r.autoPlayClear,r),r.changeSlide=e.proxy(r.changeSlide,r),r.selectHandler=e.proxy(r.selectHandler,r),r.setPosition=e.proxy(r.setPosition,r),r.swipeHandler=e.proxy(r.swipeHandler,r),r.dragHandler=e.proxy(r.dragHandler,r),r.keyHandler=e.proxy(r.keyHandler,r),r.autoPlayIterator=e.proxy(r.autoPlayIterator,r),r.instanceUid=t++,r.htmlExpr=/^(?:\s*(<[\w\W]+>)[^>]*)$/,r.init()}}()).prototype.addSlide=function(t,i,n){var s=this;if("boolean"==typeof i)n=i,i=null;else if(0>i||i>=s.slideCount)return!1;s.unload(),"number"==typeof i?0===i&&0===s.$slides.length?e(t).appendTo(s.$slideTrack):n?e(t).insertBefore(s.$slides.eq(i)):e(t).insertAfter(s.$slides.eq(i)):!0===n?e(t).prependTo(s.$slideTrack):e(t).appendTo(s.$slideTrack),s.$slides=s.$slideTrack.children(this.options.slide),s.$slideTrack.children(this.options.slide).detach(),s.$slideTrack.append(s.$slides),s.$slides.each(function(t,i){e(i).attr("index",t)}),s.$slidesCache=s.$slides,s.reinit()},t.prototype.animateSlide=function(t,i){var n={},s=this;!0===s.options.rtl&&!1===s.options.vertical&&(t=-t),!1===s.transformsEnabled?!1===s.options.vertical?s.$slideTrack.animate({left:t},s.options.speed,s.options.easing,i):s.$slideTrack.animate({top:t},s.options.speed,s.options.easing,i):!1===s.cssTransitions?e({animStart:s.currentLeft}).animate({animStart:t},{duration:s.options.speed,easing:s.options.easing,step:function(e){!1===s.options.vertical?(n[s.animType]="translate("+e+"px, 0px)",s.$slideTrack.css(n)):(n[s.animType]="translate(0px,"+e+"px)",s.$slideTrack.css(n))},complete:function(){i&&i.call()}}):(s.applyTransition(),n[s.animType]=!1===s.options.vertical?"translate3d("+t+"px, 0px, 0px)":"translate3d(0px,"+t+"px, 0px)",s.$slideTrack.css(n),i&&setTimeout(function(){s.disableTransition(),i.call()},s.options.speed))},t.prototype.applyTransition=function(e){var t=this,i={};i[t.transitionType]=!1===t.options.fade?t.transformType+" "+t.options.speed+"ms "+t.options.cssEase:"opacity "+t.options.speed+"ms "+t.options.cssEase,!1===t.options.fade?t.$slideTrack.css(i):t.$slides.eq(e).css(i)},t.prototype.autoPlay=function(){var e=this;e.autoPlayTimer&&clearInterval(e.autoPlayTimer),e.slideCount>e.options.slidesToShow&&!0!==e.paused&&(e.autoPlayTimer=setInterval(e.autoPlayIterator,e.options.autoplaySpeed))},t.prototype.autoPlayClear=function(){var e=this;e.autoPlayTimer&&clearInterval(e.autoPlayTimer)},t.prototype.autoPlayIterator=function(){var t=this,i=null!=t.options.asNavFor?e(t.options.asNavFor).getSlick():null;!1===t.options.infinite?1===t.direction?(t.currentSlide+1===t.slideCount-1&&(t.direction=0),t.slideHandler(t.currentSlide+t.options.slidesToScroll),null!=i&&i.slideHandler(i.currentSlide+i.options.slidesToScroll)):(0==t.currentSlide-1&&(t.direction=1),t.slideHandler(t.currentSlide-t.options.slidesToScroll),null!=i&&i.slideHandler(i.currentSlide-i.options.slidesToScroll)):(t.slideHandler(t.currentSlide+t.options.slidesToScroll),null!=i&&i.slideHandler(i.currentSlide+i.options.slidesToScroll))},t.prototype.buildArrows=function(){var t=this;!0===t.options.arrows&&t.slideCount>t.options.slidesToShow&&(t.$prevArrow=e(t.options.prevArrow),t.$nextArrow=e(t.options.nextArrow),t.htmlExpr.test(t.options.prevArrow)&&t.$prevArrow.appendTo(t.options.appendArrows),t.htmlExpr.test(t.options.nextArrow)&&t.$nextArrow.appendTo(t.options.appendArrows),!0!==t.options.infinite&&t.$prevArrow.addClass("slick-disabled"))},t.prototype.buildDots=function(){var t,i,n=this;if(!0===n.options.dots&&n.slideCount>n.options.slidesToShow){for(i='<ul class="'+n.options.dotsClass+'">',t=0;t<=n.getDotCount();t+=1)i+="<li>"+n.options.customPaging.call(this,n,t)+"</li>";i+="</ul>",n.$dots=e(i).appendTo(n.$slider),n.$dots.find("li").first().addClass("slick-active")}},t.prototype.buildOut=function(){var t=this;t.$slides=t.$slider.children(t.options.slide+":not(.slick-cloned)").addClass("slick-slide"),t.slideCount=t.$slides.length,t.$slides.each(function(t,i){e(i).attr("index",t)}),t.$slidesCache=t.$slides,t.$slider.addClass("slick-slider"),t.$slideTrack=0===t.slideCount?e('<div class="slick-track"/>').appendTo(t.$slider):t.$slides.wrapAll('<div class="slick-track"/>').parent(),t.$list=t.$slideTrack.wrap('<div class="slick-list"/>').parent(),t.$slideTrack.css("opacity",0),!0===t.options.centerMode&&(t.options.slidesToScroll=1,0==t.options.slidesToShow%2&&(t.options.slidesToShow=3)),e("img[data-lazy]",t.$slider).not("[src]").addClass("slick-loading"),t.setupInfinite(),t.buildArrows(),t.buildDots(),t.updateDots(),!0===t.options.accessibility&&t.$list.prop("tabIndex",0),t.setSlideClasses("number"==typeof this.currentSlide?this.currentSlide:0),!0===t.options.draggable&&t.$list.addClass("draggable")},t.prototype.checkResponsive=function(){var t,i,n=this;if(n.originalSettings.responsive&&n.originalSettings.responsive.length>-1&&null!==n.originalSettings.responsive){i=null;for(t in n.breakpoints)n.breakpoints.hasOwnProperty(t)&&e(window).width()<n.breakpoints[t]&&(i=n.breakpoints[t]);null!==i?null!==n.activeBreakpoint?i!==n.activeBreakpoint&&(n.activeBreakpoint=i,n.options=e.extend({},n.options,n.breakpointSettings[i]),n.refresh()):(n.activeBreakpoint=i,n.options=e.extend({},n.options,n.breakpointSettings[i]),n.refresh()):null!==n.activeBreakpoint&&(n.activeBreakpoint=null,n.options=e.extend({},n.options,n.originalSettings),n.refresh())}},t.prototype.changeSlide=function(t){var i=this,n=e(t.target),s=null!=i.options.asNavFor?e(i.options.asNavFor).getSlick():null;switch(n.is("a")&&t.preventDefault(),t.data.message){case"previous":i.slideCount>i.options.slidesToShow&&(i.slideHandler(i.currentSlide-i.options.slidesToScroll),null!=s&&s.slideHandler(s.currentSlide-s.options.slidesToScroll));break;case"next":i.slideCount>i.options.slidesToShow&&(i.slideHandler(i.currentSlide+i.options.slidesToScroll),null!=s&&s.slideHandler(s.currentSlide+s.options.slidesToScroll));break;case"index":var o=e(t.target).parent().index()*i.options.slidesToScroll;i.slideHandler(o),null!=s&&s.slideHandler(o);break;default:return!1}},t.prototype.destroy=function(){var t=this;t.autoPlayClear(),t.touchObject={},e(".slick-cloned",t.$slider).remove(),t.$dots&&t.$dots.remove(),t.$prevArrow&&(t.$prevArrow.remove(),t.$nextArrow.remove()),t.$slides.parent().hasClass("slick-track")&&t.$slides.unwrap().unwrap(),t.$slides.removeClass("slick-slide slick-active slick-visible").removeAttr("style"),t.$slider.removeClass("slick-slider"),t.$slider.removeClass("slick-initialized"),t.$list.off(".slick"),e(window).off(".slick-"+t.instanceUid),e(document).off(".slick-"+t.instanceUid)},t.prototype.disableTransition=function(e){var t=this,i={};i[t.transitionType]="",!1===t.options.fade?t.$slideTrack.css(i):t.$slides.eq(e).css(i)},t.prototype.fadeSlide=function(e,t){var i=this;!1===i.cssTransitions?(i.$slides.eq(e).css({zIndex:1e3}),i.$slides.eq(e).animate({opacity:1},i.options.speed,i.options.easing,t)):(i.applyTransition(e),i.$slides.eq(e).css({opacity:1,zIndex:1e3}),t&&setTimeout(function(){i.disableTransition(e),t.call()},i.options.speed))},t.prototype.filterSlides=function(e){var t=this;null!==e&&(t.unload(),t.$slideTrack.children(this.options.slide).detach(),t.$slidesCache.filter(e).appendTo(t.$slideTrack),t.reinit())},t.prototype.getCurrent=function(){return this.currentSlide},t.prototype.getDotCount=function(){var e,t=this,i=0,n=0,s=0;for(e=!0===t.options.infinite?t.slideCount+t.options.slidesToShow-t.options.slidesToScroll:t.slideCount;e>i;)s++,n+=t.options.slidesToScroll,i=n+t.options.slidesToShow;return s},t.prototype.getLeft=function(e){var t,i=this,n=0;return i.slideOffset=0,t=i.$slides.first().outerHeight(),!0===i.options.infinite?(i.slideCount>i.options.slidesToShow&&(i.slideOffset=-1*i.slideWidth*i.options.slidesToShow,n=-1*t*i.options.slidesToShow),0!=i.slideCount%i.options.slidesToScroll&&e+i.options.slidesToScroll>i.slideCount&&i.slideCount>i.options.slidesToShow&&(i.slideOffset=-1*i.slideCount%i.options.slidesToShow*i.slideWidth,n=-1*i.slideCount%i.options.slidesToShow*t)):0!=i.slideCount%i.options.slidesToShow&&e+i.options.slidesToScroll>i.slideCount&&i.slideCount>i.options.slidesToShow&&(i.slideOffset=i.options.slidesToShow*i.slideWidth-i.slideCount%i.options.slidesToShow*i.slideWidth,n=i.slideCount%i.options.slidesToShow*t),!0===i.options.centerMode&&!0===i.options.infinite?i.slideOffset+=i.slideWidth*Math.floor(i.options.slidesToShow/2)-i.slideWidth:!0===i.options.centerMode&&(i.slideOffset+=i.slideWidth*Math.floor(i.options.slidesToShow/2)),!1===i.options.vertical?-1*e*i.slideWidth+i.slideOffset:-1*e*t+n},t.prototype.init=function(){var t=this;e(t.$slider).hasClass("slick-initialized")||(e(t.$slider).addClass("slick-initialized"),t.buildOut(),t.setProps(),t.startLoad(),t.loadSlider(),t.initializeEvents(),t.checkResponsive()),null!==t.options.onInit&&t.options.onInit.call(this,t)},t.prototype.initArrowEvents=function(){var e=this;!0===e.options.arrows&&e.slideCount>e.options.slidesToShow&&(e.$prevArrow.on("click.slick",{message:"previous"},e.changeSlide),e.$nextArrow.on("click.slick",{message:"next"},e.changeSlide))},t.prototype.initDotEvents=function(){var t=this;!0===t.options.dots&&t.slideCount>t.options.slidesToShow&&e("li",t.$dots).on("click.slick",{message:"index"},t.changeSlide),!0===t.options.dots&&!0===t.options.pauseOnDotsHover&&!0===t.options.autoplay&&e("li",t.$dots).on("mouseenter.slick",t.autoPlayClear).on("mouseleave.slick",t.autoPlay)},t.prototype.initializeEvents=function(){var t=this;t.initArrowEvents(),t.initDotEvents(),t.$list.on("touchstart.slick mousedown.slick",{action:"start"},t.swipeHandler),t.$list.on("touchmove.slick mousemove.slick",{action:"move"},t.swipeHandler),t.$list.on("touchend.slick mouseup.slick",{action:"end"},t.swipeHandler),t.$list.on("touchcancel.slick mouseleave.slick",{action:"end"},t.swipeHandler),!0===t.options.pauseOnHover&&!0===t.options.autoplay&&(t.$list.on("mouseenter.slick",t.autoPlayClear),t.$list.on("mouseleave.slick",t.autoPlay)),!0===t.options.accessibility&&t.$list.on("keydown.slick",t.keyHandler),!0===t.options.focusOnSelect&&e(t.options.slide,t.$slideTrack).on("click.slick",t.selectHandler),e(window).on("orientationchange.slick.slick-"+t.instanceUid,function(){t.checkResponsive(),t.setPosition()}),e(window).on("resize.slick.slick-"+t.instanceUid,function(){e(window).width()!==t.windowWidth&&(clearTimeout(t.windowDelay),t.windowDelay=window.setTimeout(function(){t.windowWidth=e(window).width(),t.checkResponsive(),t.setPosition()},50))}),e(window).on("load.slick.slick-"+t.instanceUid,t.setPosition),e(document).on("ready.slick.slick-"+t.instanceUid,t.setPosition)},t.prototype.initUI=function(){var e=this;!0===e.options.arrows&&e.slideCount>e.options.slidesToShow&&(e.$prevArrow.show(),e.$nextArrow.show()),!0===e.options.dots&&e.slideCount>e.options.slidesToShow&&e.$dots.show(),!0===e.options.autoplay&&e.autoPlay()},t.prototype.keyHandler=function(e){var t=this;37===e.keyCode?t.changeSlide({data:{message:"previous"}}):39===e.keyCode&&t.changeSlide({data:{message:"next"}})},t.prototype.lazyLoad=function(){function t(t){e("img[data-lazy]",t).each(function(){var t=e(this),i=e(this).attr("data-lazy")+"?"+(new Date).getTime();t.load(function(){t.animate({opacity:1},200)}).css({opacity:0}).attr("src",i).removeAttr("data-lazy").removeClass("slick-loading")})}var i,n,s,o=this;!0===o.options.centerMode||!0===o.options.fade?(n=o.options.slidesToShow+o.currentSlide-1,s=n+o.options.slidesToShow+2):(n=o.options.infinite?o.options.slidesToShow+o.currentSlide:o.currentSlide,s=n+o.options.slidesToShow),t(o.$slider.find(".slick-slide").slice(n,s)),1==o.slideCount?(i=o.$slider.find(".slick-slide"),t(i)):o.currentSlide>=o.slideCount-o.options.slidesToShow?(i=o.$slider.find(".slick-cloned").slice(0,o.options.slidesToShow),t(i)):0===o.currentSlide&&(i=o.$slider.find(".slick-cloned").slice(-1*o.options.slidesToShow),t(i))},t.prototype.loadSlider=function(){var e=this;e.setPosition(),e.$slideTrack.css({opacity:1}),e.$slider.removeClass("slick-loading"),e.initUI(),"progressive"===e.options.lazyLoad&&e.progressiveLazyLoad()},t.prototype.postSlide=function(e){var t=this;null!==t.options.onAfterChange&&t.options.onAfterChange.call(this,t,e),t.animating=!1,t.setPosition(),t.swipeLeft=null,!0===t.options.autoplay&&!1===t.paused&&t.autoPlay()},t.prototype.progressiveLazyLoad=function(){var t,i=this;e("img[data-lazy]").length>0&&(t=e("img[data-lazy]",i.$slider).first()).attr("src",t.attr("data-lazy")).removeClass("slick-loading").load(function(){t.removeAttr("data-lazy"),i.progressiveLazyLoad()})},t.prototype.refresh=function(){var t=this,i=t.currentSlide;t.destroy(),e.extend(t,t.initials),t.currentSlide=i,t.init()},t.prototype.reinit=function(){var t=this;t.$slides=t.$slideTrack.children(t.options.slide).addClass("slick-slide"),t.slideCount=t.$slides.length,t.currentSlide>=t.slideCount&&0!==t.currentSlide&&(t.currentSlide=t.currentSlide-t.options.slidesToScroll),t.setProps(),t.setupInfinite(),t.buildArrows(),t.updateArrows(),t.initArrowEvents(),t.buildDots(),t.updateDots(),t.initDotEvents(),!0===t.options.focusOnSelect&&e(t.options.slide,t.$slideTrack).on("click.slick",t.selectHandler),t.setSlideClasses(0),t.setPosition(),null!==t.options.onReInit&&t.options.onReInit.call(this,t)},t.prototype.removeSlide=function(e,t){var i=this;return"boolean"==typeof e?(t=e,e=!0===t?0:i.slideCount-1):e=!0===t?--e:e,!(i.slideCount<1||0>e||e>i.slideCount-1)&&(i.unload(),i.$slideTrack.children(this.options.slide).eq(e).remove(),i.$slides=i.$slideTrack.children(this.options.slide),i.$slideTrack.children(this.options.slide).detach(),i.$slideTrack.append(i.$slides),i.$slidesCache=i.$slides,void i.reinit())},t.prototype.setCSS=function(e){var t,i,n=this,s={};!0===n.options.rtl&&(e=-e),t="left"==n.positionProp?e+"px":"0px",i="top"==n.positionProp?e+"px":"0px",s[n.positionProp]=e,!1===n.transformsEnabled?n.$slideTrack.css(s):(s={},!1===n.cssTransitions?(s[n.animType]="translate("+t+", "+i+")",n.$slideTrack.css(s)):(s[n.animType]="translate3d("+t+", "+i+", 0px)",n.$slideTrack.css(s)))},t.prototype.setDimensions=function(){var e=this;!1===e.options.vertical?!0===e.options.centerMode&&e.$list.css({padding:"0px "+e.options.centerPadding}):(e.$list.height(e.$slides.first().outerHeight(!0)*e.options.slidesToShow),!0===e.options.centerMode&&e.$list.css({padding:e.options.centerPadding+" 0px"})),e.listWidth=e.$list.width(),e.listHeight=e.$list.height(),!1===e.options.vertical?(e.slideWidth=Math.ceil(e.listWidth/e.options.slidesToShow),e.$slideTrack.width(Math.ceil(e.slideWidth*e.$slideTrack.children(".slick-slide").length))):(e.slideWidth=Math.ceil(e.listWidth),e.$slideTrack.height(Math.ceil(e.$slides.first().outerHeight(!0)*e.$slideTrack.children(".slick-slide").length)));var t=e.$slides.first().outerWidth(!0)-e.$slides.first().width();e.$slideTrack.children(".slick-slide").width(e.slideWidth-t)},t.prototype.setFade=function(){var t,i=this;i.$slides.each(function(n,s){t=-1*i.slideWidth*n,e(s).css({position:"relative",left:t,top:0,zIndex:800,opacity:0})}),i.$slides.eq(i.currentSlide).css({zIndex:900,opacity:1})},t.prototype.setPosition=function(){var e=this;e.setDimensions(),!1===e.options.fade?e.setCSS(e.getLeft(e.currentSlide)):e.setFade()},t.prototype.setProps=function(){var e=this;e.positionProp=!0===e.options.vertical?"top":"left","top"===e.positionProp?e.$slider.addClass("slick-vertical"):e.$slider.removeClass("slick-vertical"),(void 0!==document.body.style.WebkitTransition||void 0!==document.body.style.MozTransition||void 0!==document.body.style.msTransition)&&!0===e.options.useCSS&&(e.cssTransitions=!0),void 0!==document.body.style.MozTransform&&(e.animType="MozTransform",e.transformType="-moz-transform",e.transitionType="MozTransition"),void 0!==document.body.style.webkitTransform&&(e.animType="webkitTransform",e.transformType="-webkit-transform",e.transitionType="webkitTransition"),void 0!==document.body.style.msTransform&&(e.animType="msTransform",e.transformType="-ms-transform",e.transitionType="msTransition"),void 0!==document.body.style.transform&&(e.animType="transform",e.transformType="transform",e.transitionType="transition"),e.transformsEnabled=null!==e.animType},t.prototype.setSlideClasses=function(e){var t,i,n,s,o=this;o.$slider.find(".slick-slide").removeClass("slick-active").removeClass("slick-center"),i=o.$slider.find(".slick-slide"),!0===o.options.centerMode?(t=Math.floor(o.options.slidesToShow/2),!0===o.options.infinite&&(e>=t&&e<=o.slideCount-1-t?o.$slides.slice(e-t,e+t+1).addClass("slick-active"):(n=o.options.slidesToShow+e,i.slice(n-t+1,n+t+2).addClass("slick-active")),0===e?i.eq(i.length-1-o.options.slidesToShow).addClass("slick-center"):e===o.slideCount-1&&i.eq(o.options.slidesToShow).addClass("slick-center")),o.$slides.eq(e).addClass("slick-center")):e>=0&&e<=o.slideCount-o.options.slidesToShow?o.$slides.slice(e,e+o.options.slidesToShow).addClass("slick-active"):i.length<=o.options.slidesToShow?i.addClass("slick-active"):(s=o.slideCount%o.options.slidesToShow,n=!0===o.options.infinite?o.options.slidesToShow+e:e,o.options.slidesToShow==o.options.slidesToScroll&&o.slideCount-e<o.options.slidesToShow?i.slice(n-(o.options.slidesToShow-s),n+s).addClass("slick-active"):i.slice(n,n+o.options.slidesToShow).addClass("slick-active")),"ondemand"===o.options.lazyLoad&&o.lazyLoad()},t.prototype.setupInfinite=function(){var t,i,n,s=this;if((!0===s.options.fade||!0===s.options.vertical)&&(s.options.centerMode=!1),!0===s.options.infinite&&!1===s.options.fade&&(i=null,s.slideCount>s.options.slidesToShow)){for(n=!0===s.options.centerMode?s.options.slidesToShow+1:s.options.slidesToShow,t=s.slideCount;t>s.slideCount-n;t-=1)i=t-1,e(s.$slides[i]).clone(!0).attr("id","").prependTo(s.$slideTrack).addClass("slick-cloned");for(t=0;n>t;t+=1)i=t,e(s.$slides[i]).clone(!0).attr("id","").appendTo(s.$slideTrack).addClass("slick-cloned");s.$slideTrack.find(".slick-cloned").find("[id]").each(function(){e(this).attr("id","")})}},t.prototype.selectHandler=function(t){var i=this,n=null!=i.options.asNavFor?e(i.options.asNavFor).getSlick():null,s=parseInt(e(t.target).parent().attr("index"));if(s||(s=0),!(i.slideCount<=i.options.slidesToShow)&&(i.slideHandler(s),null!=n)){if(n.slideCount<=n.options.slidesToShow)return;n.slideHandler(s)}},t.prototype.slideHandler=function(e){var t,i,n,s,o=null,r=this;return!0!==r.animating&&(t=e,o=r.getLeft(t),n=r.getLeft(r.currentSlide),s=0!=r.slideCount%r.options.slidesToScroll?r.options.slidesToScroll:0,r.currentLeft=null===r.swipeLeft?n:r.swipeLeft,!1===r.options.infinite&&!1===r.options.centerMode&&(0>e||e>r.slideCount-r.options.slidesToShow+s)?(!1===r.options.fade&&(t=r.currentSlide,r.animateSlide(n,function(){r.postSlide(t)})),!1):!1===r.options.infinite&&!0===r.options.centerMode&&(0>e||e>r.slideCount-r.options.slidesToScroll)?(!1===r.options.fade&&(t=r.currentSlide,r.animateSlide(n,function(){r.postSlide(t)})),!1):(!0===r.options.autoplay&&clearInterval(r.autoPlayTimer),i=0>t?0!=r.slideCount%r.options.slidesToScroll?r.slideCount-r.slideCount%r.options.slidesToScroll:r.slideCount-r.options.slidesToScroll:t>r.slideCount-1?0:t,r.animating=!0,null!==r.options.onBeforeChange&&e!==r.currentSlide&&r.options.onBeforeChange.call(this,r,r.currentSlide,i),r.currentSlide=i,r.setSlideClasses(r.currentSlide),r.updateDots(),r.updateArrows(),!0===r.options.fade?(r.fadeSlide(i,function(){r.postSlide(i)}),!1):void r.animateSlide(o,function(){r.postSlide(i)})))},t.prototype.startLoad=function(){var e=this;!0===e.options.arrows&&e.slideCount>e.options.slidesToShow&&(e.$prevArrow.hide(),e.$nextArrow.hide()),!0===e.options.dots&&e.slideCount>e.options.slidesToShow&&e.$dots.hide(),e.$slider.addClass("slick-loading")},t.prototype.swipeDirection=function(){var e,t,i,n,s=this;return e=s.touchObject.startX-s.touchObject.curX,t=s.touchObject.startY-s.touchObject.curY,i=Math.atan2(t,e),0>(n=Math.round(180*i/Math.PI))&&(n=360-Math.abs(n)),45>=n&&n>=0?"left":360>=n&&n>=315?"left":n>=135&&225>=n?"right":"vertical"},t.prototype.swipeEnd=function(t){var i=this,n=null!=i.options.asNavFor?e(i.options.asNavFor).getSlick():null;if(i.dragging=!1,void 0===i.touchObject.curX)return!1;if(i.touchObject.swipeLength>=i.touchObject.minSwipe)switch(e(t.target).on("click.slick",function(t){t.stopImmediatePropagation(),t.stopPropagation(),t.preventDefault(),e(t.target).off("click.slick")}),i.swipeDirection()){case"left":i.slideHandler(i.currentSlide+i.options.slidesToScroll),null!=n&&n.slideHandler(n.currentSlide+n.options.slidesToScroll),i.touchObject={};break;case"right":i.slideHandler(i.currentSlide-i.options.slidesToScroll),null!=n&&n.slideHandler(n.currentSlide-n.options.slidesToScroll),i.touchObject={}}else i.touchObject.startX!==i.touchObject.curX&&(i.slideHandler(i.currentSlide),null!=n&&n.slideHandler(n.currentSlide),i.touchObject={})},t.prototype.swipeHandler=function(e){var t=this;if(!(!1===t.options.swipe||"ontouchend"in document&&!1===t.options.swipe||!1===t.options.draggable||!1===t.options.draggable&&!e.originalEvent.touches))switch(t.touchObject.fingerCount=e.originalEvent&&void 0!==e.originalEvent.touches?e.originalEvent.touches.length:1,t.touchObject.minSwipe=t.listWidth/t.options.touchThreshold,e.data.action){case"start":t.swipeStart(e);break;case"move":t.swipeMove(e);break;case"end":t.swipeEnd(e)}},t.prototype.swipeMove=function(e){var t,i,n,s,o=this;return s=void 0!==e.originalEvent?e.originalEvent.touches:null,t=o.getLeft(o.currentSlide),!(!o.dragging||s&&1!==s.length)&&(o.touchObject.curX=void 0!==s?s[0].pageX:e.clientX,o.touchObject.curY=void 0!==s?s[0].pageY:e.clientY,o.touchObject.swipeLength=Math.round(Math.sqrt(Math.pow(o.touchObject.curX-o.touchObject.startX,2))),i=o.swipeDirection(),"vertical"!==i?(void 0!==e.originalEvent&&o.touchObject.swipeLength>4&&e.preventDefault(),n=o.touchObject.curX>o.touchObject.startX?1:-1,o.swipeLeft=!1===o.options.vertical?t+o.touchObject.swipeLength*n:t+o.touchObject.swipeLength*(o.$list.height()/o.listWidth)*n,!0!==o.options.fade&&!1!==o.options.touchMove&&(!0===o.animating?(o.swipeLeft=null,!1):void o.setCSS(o.swipeLeft))):void 0)},t.prototype.swipeStart=function(e){var t,i=this;return 1!==i.touchObject.fingerCount||i.slideCount<=i.options.slidesToShow?(i.touchObject={},!1):(void 0!==e.originalEvent&&void 0!==e.originalEvent.touches&&(t=e.originalEvent.touches[0]),i.touchObject.startX=i.touchObject.curX=void 0!==t?t.pageX:e.clientX,i.touchObject.startY=i.touchObject.curY=void 0!==t?t.pageY:e.clientY,void(i.dragging=!0))},t.prototype.unfilterSlides=function(){var e=this;null!==e.$slidesCache&&(e.unload(),e.$slideTrack.children(this.options.slide).detach(),e.$slidesCache.appendTo(e.$slideTrack),e.reinit())},t.prototype.unload=function(){var t=this;e(".slick-cloned",t.$slider).remove(),t.$dots&&t.$dots.remove(),t.$prevArrow&&(t.$prevArrow.remove(),t.$nextArrow.remove()),t.$slides.removeClass("slick-slide slick-active slick-visible").removeAttr("style")},t.prototype.updateArrows=function(){var e=this;!0===e.options.arrows&&!0!==e.options.infinite&&e.slideCount>e.options.slidesToShow&&(e.$prevArrow.removeClass("slick-disabled"),e.$nextArrow.removeClass("slick-disabled"),0===e.currentSlide?(e.$prevArrow.addClass("slick-disabled"),e.$nextArrow.removeClass("slick-disabled")):e.currentSlide>=e.slideCount-e.options.slidesToShow&&(e.$nextArrow.addClass("slick-disabled"),e.$prevArrow.removeClass("slick-disabled")))},t.prototype.updateDots=function(){var e=this;null!==e.$dots&&(e.$dots.find("li").removeClass("slick-active"),e.$dots.find("li").eq(Math.floor(e.currentSlide/e.options.slidesToScroll)).addClass("slick-active"))},e.fn.slick=function(e){return this.each(function(i,n){n.slick=new t(n,e)})},e.fn.slickAdd=function(e,t,i){return this.each(function(n,s){s.slick.addSlide(e,t,i)})},e.fn.slickCurrentSlide=function(){return this.get(0).slick.getCurrent()},e.fn.slickFilter=function(e){return this.each(function(t,i){i.slick.filterSlides(e)})},e.fn.slickGoTo=function(t){return this.each(function(i,n){var s=null!=n.slick.options.asNavFor?e(n.slick.options.asNavFor):null;null!=s&&s.slickGoTo(t),n.slick.slideHandler(t)})},e.fn.slickNext=function(){return this.each(function(e,t){t.slick.changeSlide({data:{message:"next"}})})},e.fn.slickPause=function(){return this.each(function(e,t){t.slick.autoPlayClear(),t.slick.paused=!0})},e.fn.slickPlay=function(){return this.each(function(e,t){t.slick.paused=!1,t.slick.autoPlay()})},e.fn.slickPrev=function(){return this.each(function(e,t){t.slick.changeSlide({data:{message:"previous"}})})},e.fn.slickRemove=function(e,t){return this.each(function(i,n){n.slick.removeSlide(e,t)})},e.fn.slickGetOption=function(e){return this.get(0).slick.options[e]},e.fn.slickSetOption=function(e,t,i){return this.each(function(n,s){s.slick.options[e]=t,!0===i&&(s.slick.unload(),s.slick.reinit())})},e.fn.slickUnfilter=function(){return this.each(function(e,t){t.slick.unfilterSlides()})},e.fn.unslick=function(){return this.each(function(e,t){t.slick&&t.slick.destroy()})},e.fn.getSlick=function(){var e=null;return this.each(function(t,i){e=i.slick}),e}}),function(e){"function"==typeof define&&define.amd?define(["jquery"],e):e(jQuery)}(function(e){void 0===e&&void 0!==window.jQuery&&(e=window.jQuery);var t={attr:function(e,t,i){var n,s={},o=this.msieversion(),r=new RegExp("^"+t,"i");if(void 0===e||void 0===e[0])return{};for(var a in e[0].attributes)if(void 0!==(n=e[0].attributes[a])&&null!==n&&(!o||o>=8||n.specified)&&r.test(n.name)){if(void 0!==i&&new RegExp(i+"$","i").test(n.name))return!0;s[this.camelize(n.name.replace(t,""))]=this.deserializeValue(n.value)}return void 0===i&&s},setAttr:function(e,t,i,n){e[0].setAttribute(this.dasherize(t+i),String(n))},get:function(e,t){for(var i=0,n=(t||"").split(".");this.isObject(e)||this.isArray(e);)if(e=e[n[i++]],i===n.length)return e},hash:function(e){return String(Math.random()).substring(2,e?e+2:9)},isArray:function(e){return"[object Array]"===Object.prototype.toString.call(e)},isObject:function(e){return e===Object(e)},deserializeValue:function(t){var i;try{return t?"true"==t||"false"!=t&&("null"==t?null:isNaN(i=Number(t))?/^[\[\{]/.test(t)?e.parseJSON(t):t:i):t}catch(e){return t}},camelize:function(e){return e.replace(/-+(.)?/g,function(e,t){return t?t.toUpperCase():""})},dasherize:function(e){return e.replace(/::/g,"/").replace(/([A-Z]+)([A-Z][a-z])/g,"$1_$2").replace(/([a-z\d])([A-Z])/g,"$1_$2").replace(/_/g,"-").toLowerCase()},msieversion:function(){var e=window.navigator.userAgent,t=e.indexOf("MSIE ");return t>0||navigator.userAgent.match(/Trident.*rv\:11\./)?parseInt(e.substring(t+5,e.indexOf(".",t)),10):0}},i={namespace:"data-parsley-",inputs:"input, textarea, select",excluded:"input[type=button], input[type=submit], input[type=reset], input[type=hidden]",priorityEnabled:!0,uiEnabled:!0,validationThreshold:3,focus:"first",trigger:!1,errorClass:"parsley-error",successClass:"parsley-success",classHandler:function(){},errorsContainer:function(){},errorsWrapper:'<ul class="parsley-errors-list"></ul>',errorTemplate:"<li></li>"},n=function(){};n.prototype={asyncSupport:!1,actualizeOptions:function(){return this.options=this.OptionsFactory.get(this),this},validateThroughValidator:function(e,t,i){return window.ParsleyValidator.validate.apply(window.ParsleyValidator,[e,t,i])},subscribe:function(t,i){return e.listenTo(this,t.toLowerCase(),i),this},unsubscribe:function(t){return e.unsubscribeTo(this,t.toLowerCase()),this},reset:function(){if("ParsleyForm"!==this.__class__)return e.emit("parsley:field:reset",this);for(var t=0;t<this.fields.length;t++)e.emit("parsley:field:reset",this.fields[t]);e.emit("parsley:form:reset",this)},destroy:function(){if("ParsleyForm"!==this.__class__)return this.$element.removeData("Parsley"),this.$element.removeData("ParsleyFieldMultiple"),void e.emit("parsley:field:destroy",this);for(var t=0;t<this.fields.length;t++)this.fields[t].destroy();this.$element.removeData("Parsley"),e.emit("parsley:form:destroy",this)}};var s=function(){var e={},t=function(e){this.__class__="Validator",this.__version__="1.0.0",this.options=e||{},this.bindingKey=this.options.bindingKey||"_validatorjsConstraint"};t.prototype={constructor:t,validate:function(e,t,i){if("string"!=typeof e&&"object"!=typeof e)throw new Error("You must validate an object or a string");return"string"==typeof e||r(e)?this._validateString(e,t,i):this.isBinded(e)?this._validateBindedObject(e,t):this._validateObject(e,t,i)},bind:function(e,t){if("object"!=typeof e)throw new Error("Must bind a Constraint to an object");return e[this.bindingKey]=new i(t),this},unbind:function(e){return void 0===e._validatorjsConstraint?this:(delete e[this.bindingKey],this)},isBinded:function(e){return void 0!==e[this.bindingKey]},getBinded:function(e){return this.isBinded(e)?e[this.bindingKey]:null},_validateString:function(e,t,i){var o,a=[];r(t)||(t=[t]);for(var l=0;l<t.length;l++){if(!(t[l]instanceof s))throw new Error("You must give an Assert or an Asserts array to validate a string");(o=t[l].check(e,i))instanceof n&&a.push(o)}return!a.length||a},_validateObject:function(e,t,n){if("object"!=typeof t)throw new Error("You must give a constraint to validate an object");return t instanceof i?t.check(e,n):new i(t).check(e,n)},_validateBindedObject:function(e,t){return e[this.bindingKey].check(e,t)}},t.errorCode={must_be_a_string:"must_be_a_string",must_be_an_array:"must_be_an_array",must_be_a_number:"must_be_a_number",must_be_a_string_or_array:"must_be_a_string_or_array"};var i=function(e,t){if(this.__class__="Constraint",this.options=t||{},this.nodes={},e)try{this._bootstrap(e)}catch(t){throw new Error("Should give a valid mapping object to Constraint",t,e)}};i.prototype={constructor:i,check:function(e,t){var i,n={};for(var a in this.nodes){for(var l=!1,d=this.get(a),u=r(d)?d:[d],c=u.length-1;c>=0;c--)"Required"!==u[c].__class__||(l=u[c].requiresValidation(t));if(this.has(a,e)||this.options.strict||l)try{this.has(a,this.options.strict||l?e:void 0)||(new s).HaveProperty(a).validate(e),i=this._check(a,e[a],t),(r(i)&&i.length>0||!r(i)&&!o(i))&&(n[a]=i)}catch(e){n[a]=e}}return!!o(n)||n},add:function(e,t){if(t instanceof s||r(t)&&t[0]instanceof s)return this.nodes[e]=t,this;if("object"==typeof t&&!r(t))return this.nodes[e]=t instanceof i?t:new i(t),this;throw new Error("Should give an Assert, an Asserts array, a Constraint",t)},has:function(e,t){return void 0!==(t=void 0!==t?t:this.nodes)[e]},get:function(e,t){return this.has(e)?this.nodes[e]:t||null},remove:function(e){var t=[];for(var i in this.nodes)i!==e&&(t[i]=this.nodes[i]);return this.nodes=t,this},_bootstrap:function(e){if(e instanceof i)return this.nodes=e.nodes;for(var t in e)this.add(t,e[t])},_check:function(e,t,n){if(this.nodes[e]instanceof s)return this._checkAsserts(t,[this.nodes[e]],n);if(r(this.nodes[e]))return this._checkAsserts(t,this.nodes[e],n);if(this.nodes[e]instanceof i)return this.nodes[e].check(t,n);throw new Error("Invalid node",this.nodes[e])},_checkAsserts:function(e,t,i){for(var n,s=[],o=0;o<t.length;o++)void 0!==(n=t[o].check(e,i))&&!0!==n&&s.push(n);return s}};var n=function(e,t,i){if(this.__class__="Violation",!(e instanceof s))throw new Error("Should give an assertion implementing the Assert interface");this.assert=e,this.value=t,void 0!==i&&(this.violation=i)};n.prototype={show:function(){var e={assert:this.assert.__class__,value:this.value};return this.violation&&(e.violation=this.violation),e},__toString:function(){return void 0!==this.violation&&(this.violation='", '+this.getViolation().constraint+" expected was "+this.getViolation().expected),this.assert.__class__+' assert failed for "'+this.value+this.violation||""},getViolation:function(){var e,t;for(e in this.violation)t=this.violation[e];return{constraint:e,expected:t}}};var s=function(e){this.__class__="Assert",this.__parentClass__=this.__class__,this.groups=[],void 0!==e&&this.addGroup(e)};s.prototype={construct:s,requiresValidation:function(e){return!(e&&!this.hasGroup(e))&&!(!e&&this.hasGroups())},check:function(e,t){if(this.requiresValidation(t))try{return this.validate(e,t)}catch(e){return e}},hasGroup:function(e){return r(e)?this.hasOneOf(e):"Any"===e||(this.hasGroups()?-1!==this.groups.indexOf(e):"Default"===e)},hasOneOf:function(e){for(var t=0;t<e.length;t++)if(this.hasGroup(e[t]))return!0;return!1},hasGroups:function(){return this.groups.length>0},addGroup:function(e){return r(e)?this.addGroups(e):(this.hasGroup(e)||this.groups.push(e),this)},removeGroup:function(e){for(var t=[],i=0;i<this.groups.length;i++)e!==this.groups[i]&&t.push(this.groups[i]);return this.groups=t,this},addGroups:function(e){for(var t=0;t<e.length;t++)this.addGroup(e[t]);return this},HaveProperty:function(e){return this.__class__="HaveProperty",this.node=e,this.validate=function(e){if(void 0===e[this.node])throw new n(this,e,{value:this.node});return!0},this},Blank:function(){return this.__class__="Blank",this.validate=function(e){if("string"!=typeof e)throw new n(this,e,{value:t.errorCode.must_be_a_string});if(""!==e.replace(/^\s+/g,"").replace(/\s+$/g,""))throw new n(this,e);return!0},this},Callback:function(e){if(this.__class__="Callback",this.arguments=Array.prototype.slice.call(arguments),1===this.arguments.length?this.arguments=[]:this.arguments.splice(0,1),"function"!=typeof e)throw new Error("Callback must be instanciated with a function");return this.fn=e,this.validate=function(e){var t=this.fn.apply(this,[e].concat(this.arguments));if(!0!==t)throw new n(this,e,{result:t});return!0},this},Choice:function(e){if(this.__class__="Choice",!r(e)&&"function"!=typeof e)throw new Error("Choice must be instanciated with an array or a function");return this.list=e,this.validate=function(e){for(var t="function"==typeof this.list?this.list():this.list,i=0;i<t.length;i++)if(e===t[i])return!0;throw new n(this,e,{choices:t})},this},Collection:function(e){return this.__class__="Collection",this.constraint=void 0!==e&&(e instanceof s?e:new i(e)),this.validate=function(e,i){var s,a=new t,l=0,d={},u=this.groups.length?this.groups:i;if(!r(e))throw new n(this,array,{value:t.errorCode.must_be_an_array});for(var c=0;c<e.length;c++)s=this.constraint?a.validate(e[c],this.constraint,u):a.validate(e[c],u),o(s)||(d[l]=s),l++;return!!o(d)||d},this},Count:function(e){return this.__class__="Count",this.count=e,this.validate=function(e){if(!r(e))throw new n(this,e,{value:t.errorCode.must_be_an_array});var i="function"==typeof this.count?this.count(e):this.count;if(isNaN(Number(i)))throw new Error("Count must be a valid interger",i);if(i!==e.length)throw new n(this,e,{count:i});return!0},this},Email:function(){return this.__class__="Email",this.validate=function(e){var i=/^((([a-z]|\d|[!#\$%&'\*\+\-\/=\?\^_`{\|}~]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])+(\.([a-z]|\d|[!#\$%&'\*\+\-\/=\?\^_`{\|}~]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])+)*)|((\x22)((((\x20|\x09)*(\x0d\x0a))?(\x20|\x09)+)?(([\x01-\x08\x0b\x0c\x0e-\x1f\x7f]|\x21|[\x23-\x5b]|[\x5d-\x7e]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(\\([\x01-\x09\x0b\x0c\x0d-\x7f]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]))))*(((\x20|\x09)*(\x0d\x0a))?(\x20|\x09)+)?(\x22)))@((([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])*([a-z]|\d|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])))\.)+(([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])|(([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])([a-z]|\d|-|\.|_|~|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])*([a-z]|[\u00A0-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF])))$/i;if("string"!=typeof e)throw new n(this,e,{value:t.errorCode.must_be_a_string});if(!i.test(e))throw new n(this,e);return!0},this},EqualTo:function(e){if(this.__class__="EqualTo",void 0===e)throw new Error("EqualTo must be instanciated with a value or a function");return this.reference=e,this.validate=function(e){var t="function"==typeof this.reference?this.reference(e):this.reference;if(t!==e)throw new n(this,e,{value:t});return!0},this},GreaterThan:function(e){if(this.__class__="GreaterThan",void 0===e)throw new Error("Should give a threshold value");return this.threshold=e,this.validate=function(e){if(""===e||isNaN(Number(e)))throw new n(this,e,{value:t.errorCode.must_be_a_number});if(this.threshold>=e)throw new n(this,e,{threshold:this.threshold});return!0},this},GreaterThanOrEqual:function(e){if(this.__class__="GreaterThanOrEqual",void 0===e)throw new Error("Should give a threshold value");return this.threshold=e,this.validate=function(e){if(""===e||isNaN(Number(e)))throw new n(this,e,{value:t.errorCode.must_be_a_number});if(this.threshold>e)throw new n(this,e,{threshold:this.threshold});return!0},this},InstanceOf:function(e){if(this.__class__="InstanceOf",void 0===e)throw new Error("InstanceOf must be instanciated with a value");return this.classRef=e,this.validate=function(e){if(1!=e instanceof this.classRef)throw new n(this,e,{classRef:this.classRef});return!0},this},Length:function(e){if(this.__class__="Length",!e.min&&!e.max)throw new Error("Lenth assert must be instanciated with a { min: x, max: y } object");return this.min=e.min,this.max=e.max,this.validate=function(e){if("string"!=typeof e&&!r(e))throw new n(this,e,{value:t.errorCode.must_be_a_string_or_array});if(void 0!==this.min&&this.min===this.max&&e.length!==this.min)throw new n(this,e,{min:this.min,max:this.max});if(void 0!==this.max&&e.length>this.max)throw new n(this,e,{max:this.max});if(void 0!==this.min&&e.length<this.min)throw new n(this,e,{min:this.min});return!0},this},LessThan:function(e){if(this.__class__="LessThan",void 0===e)throw new Error("Should give a threshold value");return this.threshold=e,this.validate=function(e){if(""===e||isNaN(Number(e)))throw new n(this,e,{value:t.errorCode.must_be_a_number});if(this.threshold<=e)throw new n(this,e,{threshold:this.threshold});return!0},this},LessThanOrEqual:function(e){if(this.__class__="LessThanOrEqual",void 0===e)throw new Error("Should give a threshold value");return this.threshold=e,this.validate=function(e){if(""===e||isNaN(Number(e)))throw new n(this,e,{value:t.errorCode.must_be_a_number});if(this.threshold<e)throw new n(this,e,{threshold:this.threshold});return!0},this},NotNull:function(){return this.__class__="NotNull",this.validate=function(e){if(null===e||void 0===e)throw new n(this,e);return!0},this},NotBlank:function(){return this.__class__="NotBlank",this.validate=function(e){if("string"!=typeof e)throw new n(this,e,{value:t.errorCode.must_be_a_string});if(""===e.replace(/^\s+/g,"").replace(/\s+$/g,""))throw new n(this,e);return!0},this},Null:function(){return this.__class__="Null",this.validate=function(e){if(null!==e)throw new n(this,e);return!0},this},Range:function(e,t){if(this.__class__="Range",void 0===e||void 0===t)throw new Error("Range assert expects min and max values");return this.min=e,this.max=t,this.validate=function(e){try{return"string"==typeof e&&isNaN(Number(e))||r(e)?(new s).Length({min:this.min,max:this.max}).validate(e):(new s).GreaterThanOrEqual(this.min).validate(e)&&(new s).LessThanOrEqual(this.max).validate(e),!0}catch(t){throw new n(this,e,t.violation)}return!0},this},Regexp:function(e,i){if(this.__class__="Regexp",void 0===e)throw new Error("You must give a regexp");return this.regexp=e,this.flag=i||"",this.validate=function(e){if("string"!=typeof e)throw new n(this,e,{value:t.errorCode.must_be_a_string});if(!new RegExp(this.regexp,this.flag).test(e))throw new n(this,e,{regexp:this.regexp,flag:this.flag});return!0},this},Required:function(){return this.__class__="Required",this.validate=function(e){if(void 0===e)throw new n(this,e);try{"string"==typeof e?(new s).NotNull().validate(e)&&(new s).NotBlank().validate(e):!0===r(e)&&(new s).Length({min:1}).validate(e)}catch(t){throw new n(this,e)}return!0},this},Unique:function(e){return this.__class__="Unique","object"==typeof e&&(this.key=e.key),this.validate=function(e){var i,s=[];if(!r(e))throw new n(this,e,{value:t.errorCode.must_be_an_array});for(var o=0;o<e.length;o++)if(void 0!==(i="object"==typeof e[o]?e[o][this.key]:e[o])){if(-1!==s.indexOf(i))throw new n(this,e,{value:i});s.push(i)}return!0},this}},e.Assert=s,e.Validator=t,e.Violation=n,e.Constraint=i,Array.prototype.indexOf||(Array.prototype.indexOf=function(e){if(null===this)throw new TypeError;var t=Object(this),i=t.length>>>0;if(0===i)return-1;var n=0;if(arguments.length>1&&(n=Number(arguments[1]),n!=n?n=0:0!==n&&1/0!=n&&n!=-1/0&&(n=(n>0||-1)*Math.floor(Math.abs(n)))),n>=i)return-1;for(var s=n>=0?n:Math.max(i-Math.abs(n),0);i>s;s++)if(s in t&&t[s]===e)return s;return-1});var o=function(e){for(var t in e)return!1;return!0},r=function(e){return"[object Array]"===Object.prototype.toString.call(e)};return"function"==typeof define&&define.amd?define("vendors/validator.js/dist/validator",[],function(){return e}):"undefined"!=typeof module&&module.exports?module.exports=e:window["undefined"!=typeof validatorjs_ns?validatorjs_ns:"Validator"]=e,e}();s=void 0!==s?s:"undefined"!=typeof module?module.exports:null;var o=function(e,t){this.__class__="ParsleyValidator",this.Validator=s,this.locale="en",this.init(e||{},t||{})};o.prototype={init:function(t,i){this.catalog=i;for(var n in t)this.addValidator(n,t[n].fn,t[n].priority,t[n].requirementsTransformer);e.emit("parsley:validator:init")},setLocale:function(e){if(void 0===this.catalog[e])throw new Error(e+" is not available in the catalog");return this.locale=e,this},addCatalog:function(e,t,i){return"object"==typeof t&&(this.catalog[e]=t),!0===i?this.setLocale(e):this},addMessage:function(e,t,i){return void 0===this.catalog[e]&&(this.catalog[e]={}),this.catalog[e][t.toLowerCase()]=i,this},validate:function(){return(new this.Validator.Validator).validate.apply(new s.Validator,arguments)},addValidator:function(t,i,n,o){return this.validators[t.toLowerCase()]=function(t){return e.extend((new s.Assert).Callback(i,t),{priority:n,requirementsTransformer:o})},this},updateValidator:function(e,t,i,n){return this.addValidator(e,t,i,n)},removeValidator:function(e){return delete this.validators[e],this},getErrorMessage:function(e){var t;return t="type"===e.name?this.catalog[this.locale][e.name][e.requirements]:this.formatMessage(this.catalog[this.locale][e.name],e.requirements),""!==t?t:this.catalog[this.locale].defaultMessage},formatMessage:function(e,t){if("object"==typeof t){for(var i in t)e=this.formatMessage(e,t[i]);return e}return"string"==typeof e?e.replace(new RegExp("%s","i"),t):""},validators:{notblank:function(){return e.extend((new s.Assert).NotBlank(),{priority:2})},required:function(){return e.extend((new s.Assert).Required(),{priority:512})},type:function(t){var i;switch(t){case"email":i=(new s.Assert).Email();break;case"range":case"number":i=(new s.Assert).Regexp("^-?(?:\\d+|\\d{1,3}(?:,\\d{3})+)?(?:\\.\\d+)?$");break;case"integer":i=(new s.Assert).Regexp("^-?\\d+$");break;case"digits":i=(new s.Assert).Regexp("^\\d+$");break;case"alphanum":i=(new s.Assert).Regexp("^\\w+$","i");break;case"url":i=(new s.Assert).Regexp("(https?:\\/\\/)?(www\\.)?[-a-zA-Z0-9@:%._\\+~#=]{2,256}\\.[a-z]{2,4}\\b([-a-zA-Z0-9@:%_\\+.~#?&//=]*)","i");break;default:throw new Error("validator type `"+t+"` is not supported")}return e.extend(i,{priority:256})},pattern:function(t){var i="";return/^\/.*\/(?:[gimy]*)$/.test(t)&&(i=t.replace(/.*\/([gimy]*)$/,"$1"),t=t.replace(new RegExp("^/(.*?)/"+i+"$"),"$1")),e.extend((new s.Assert).Regexp(t,i),{priority:64})},minlength:function(t){return e.extend((new s.Assert).Length({min:t}),{priority:30,requirementsTransformer:function(){return"string"!=typeof t||isNaN(t)?t:parseInt(t,10)}})},maxlength:function(t){return e.extend((new s.Assert).Length({max:t}),{priority:30,requirementsTransformer:function(){return"string"!=typeof t||isNaN(t)?t:parseInt(t,10)}})},length:function(t){return e.extend((new s.Assert).Length({min:t[0],max:t[1]}),{priority:32})},mincheck:function(e){return this.minlength(e)},maxcheck:function(e){return this.maxlength(e)},check:function(e){return this.length(e)},min:function(t){return e.extend((new s.Assert).GreaterThanOrEqual(t),{priority:30,requirementsTransformer:function(){return"string"!=typeof t||isNaN(t)?t:parseInt(t,10)}})},max:function(t){return e.extend((new s.Assert).LessThanOrEqual(t),{priority:30,requirementsTransformer:function(){return"string"!=typeof t||isNaN(t)?t:parseInt(t,10)}})},range:function(t){return e.extend((new s.Assert).Range(t[0],t[1]),{priority:32,requirementsTransformer:function(){for(var e=0;e<t.length;e++)t[e]="string"!=typeof t[e]||isNaN(t[e])?t[e]:parseInt(t[e],10);return t}})},equalto:function(t){return e.extend((new s.Assert).EqualTo(t),{priority:256,requirementsTransformer:function(){return e(t).length?e(t).val():t}})}}};var r=function(){this.__class__="ParsleyUI"};r.prototype={listen:function(){return e.listen("parsley:form:init",this,this.setupForm),e.listen("parsley:field:init",this,this.setupField),e.listen("parsley:field:validated",this,this.reflow),e.listen("parsley:form:validated",this,this.focus),e.listen("parsley:field:reset",this,this.reset),e.listen("parsley:form:destroy",this,this.destroy),e.listen("parsley:field:destroy",this,this.destroy),this},reflow:function(e){if(void 0!==e._ui&&!1!==e._ui.active){var t=this._diff(e.validationResult,e._ui.lastValidationResult);e._ui.lastValidationResult=e.validationResult,e._ui.validatedOnce=!0,this.manageStatusClass(e),this.manageErrorsMessages(e,t),this.actualizeTriggers(e),(t.kept.length||t.added.length)&&void 0===e._ui.failedOnce&&this.manageFailingFieldTrigger(e)}},getErrorsMessages:function(e){if(!0===e.validationResult)return[];for(var t=[],i=0;i<e.validationResult.length;i++)t.push(this._getErrorMessage(e,e.validationResult[i].assert));return t},manageStatusClass:function(e){!0===e.validationResult?this._successClass(e):e.validationResult.length>0?this._errorClass(e):this._resetClass(e)},manageErrorsMessages:function(t,i){if(void 0===t.options.errorsMessagesDisabled){if(void 0!==t.options.errorMessage)return i.added.length||i.kept.length?(0===t._ui.$errorsWrapper.find(".parsley-custom-error-message").length&&t._ui.$errorsWrapper.append(e(t.options.errorTemplate).addClass("parsley-custom-error-message")),t._ui.$errorsWrapper.addClass("filled").find(".parsley-custom-error-message").html(t.options.errorMessage)):t._ui.$errorsWrapper.removeClass("filled").find(".parsley-custom-error-message").remove();for(var n=0;n<i.removed.length;n++)this.removeError(t,i.removed[n].assert.name,!0);for(n=0;n<i.added.length;n++)this.addError(t,i.added[n].assert.name,void 0,i.added[n].assert,!0);for(n=0;n<i.kept.length;n++)this.updateError(t,i.kept[n].assert.name,void 0,i.kept[n].assert,!0)}},addError:function(t,i,n,s,o){t._ui.$errorsWrapper.addClass("filled").append(e(t.options.errorTemplate).addClass("parsley-"+i).html(n||this._getErrorMessage(t,s))),!0!==o&&this._errorClass(t)},updateError:function(e,t,i,n,s){e._ui.$errorsWrapper.addClass("filled").find(".parsley-"+t).html(i||this._getErrorMessage(e,n)),!0!==s&&this._errorClass(e)},removeError:function(e,t,i){e._ui.$errorsWrapper.removeClass("filled").find(".parsley-"+t).remove(),!0!==i&&this.manageStatusClass(e)},focus:function(e){if(!0===e.validationResult||"none"===e.options.focus)return e._focusedField=null;e._focusedField=null;for(var t=0;t<e.fields.length;t++)if(!0!==e.fields[t].validationResult&&e.fields[t].validationResult.length>0&&void 0===e.fields[t].options.noFocus){if("first"===e.options.focus)return e._focusedField=e.fields[t].$element,e._focusedField.focus();e._focusedField=e.fields[t].$element}return null===e._focusedField?null:e._focusedField.focus()},_getErrorMessage:function(e,t){var i=t.name+"Message";return void 0!==e.options[i]?window.ParsleyValidator.formatMessage(e.options[i],t.requirements):window.ParsleyValidator.getErrorMessage(t)},_diff:function(e,t,i){for(var n=[],s=[],o=0;o<e.length;o++){for(var r=!1,a=0;a<t.length;a++)if(e[o].assert.name===t[a].assert.name){r=!0;break}r?s.push(e[o]):n.push(e[o])}return{kept:s,added:n,removed:i?[]:this._diff(t,e,!0).added}},setupForm:function(t){t.$element.on("submit.Parsley",!1,e.proxy(t.onSubmitValidate,t)),!1!==t.options.uiEnabled&&t.$element.attr("novalidate","")},setupField:function(t){var i={active:!1};!1!==t.options.uiEnabled&&(i.active=!0,t.$element.attr(t.options.namespace+"id",t.__id__),i.$errorClassHandler=this._manageClassHandler(t),i.errorsWrapperId="parsley-id-"+(void 0!==t.options.multiple?"multiple-"+t.options.multiple:t.__id__),i.$errorsWrapper=e(t.options.errorsWrapper).attr("id",i.errorsWrapperId),i.lastValidationResult=[],i.validatedOnce=!1,i.validationInformationVisible=!1,t._ui=i,t.$element.is(t.options.excluded)||this._insertErrorWrapper(t),this.actualizeTriggers(t))},_manageClassHandler:function(t){if("string"==typeof t.options.classHandler&&e(t.options.classHandler).length)return e(t.options.classHandler);var i=t.options.classHandler(t);return void 0!==i&&i.length?i:void 0===t.options.multiple||t.$element.is("select")?t.$element:t.$element.parent()},_insertErrorWrapper:function(t){var i;if("string"==typeof t.options.errorsContainer){if(e(t.options.errorsContainer).length)return e(t.options.errorsContainer).append(t._ui.$errorsWrapper);window.console&&window.console.warn&&window.console.warn("The errors container `"+t.options.errorsContainer+"` does not exist in DOM")}else"function"==typeof t.options.errorsContainer&&(i=t.options.errorsContainer(t));return void 0!==i&&i.length?i.append(t._ui.$errorsWrapper):void 0===t.options.multiple?t.$element.after(t._ui.$errorsWrapper):t.$element.parent().after(t._ui.$errorsWrapper)},actualizeTriggers:function(t){var i=this;if(t.options.multiple?e("["+t.options.namespace+'multiple="'+t.options.multiple+'"]').each(function(){e(this).off(".Parsley")}):t.$element.off(".Parsley"),!1!==t.options.trigger){var n=t.options.trigger.replace(/^\s+/g,"").replace(/\s+$/g,"");""!==n&&(t.options.multiple?e("["+t.options.namespace+'multiple="'+t.options.multiple+'"]').each(function(){e(this).on(n.split(" ").join(".Parsley ")+".Parsley",!1,e.proxy("function"==typeof t.eventValidate?t.eventValidate:i.eventValidate,t))}):t.$element.on(n.split(" ").join(".Parsley ")+".Parsley",!1,e.proxy("function"==typeof t.eventValidate?t.eventValidate:this.eventValidate,t)))}},eventValidate:function(e){new RegExp("key").test(e.type)&&!this._ui.validationInformationVisible&&this.getValue().length<=this.options.validationThreshold||(this._ui.validatedOnce=!0,this.validate())},manageFailingFieldTrigger:function(t){return t._ui.failedOnce=!0,t.options.multiple&&e("["+t.options.namespace+'multiple="'+t.options.multiple+'"]').each(function(){return new RegExp("change","i").test(e(this).parsley().options.trigger||"")?void 0:e(this).on("change.ParsleyFailedOnce",!1,e.proxy(t.validate,t))}),t.$element.is("select")&&!new RegExp("change","i").test(t.options.trigger||"")?t.$element.on("change.ParsleyFailedOnce",!1,e.proxy(t.validate,t)):new RegExp("keyup","i").test(t.options.trigger||"")?void 0:t.$element.on("keyup.ParsleyFailedOnce",!1,e.proxy(t.validate,t))},reset:function(t){t.$element.off(".Parsley"),t.$element.off(".ParsleyFailedOnce"),void 0!==t._ui&&"ParsleyForm"!==t.__class__&&(t._ui.$errorsWrapper.children().each(function(){e(this).remove()}),this._resetClass(t),t._ui.validatedOnce=!1,t._ui.lastValidationResult=[],t._ui.validationInformationVisible=!1)},destroy:function(e){this.reset(e),"ParsleyForm"!==e.__class__&&(void 0!==e._ui&&e._ui.$errorsWrapper.remove(),delete e._ui)},_successClass:function(e){e._ui.validationInformationVisible=!0,e._ui.$errorClassHandler.removeClass(e.options.errorClass).addClass(e.options.successClass)},_errorClass:function(e){e._ui.validationInformationVisible=!0,e._ui.$errorClassHandler.removeClass(e.options.successClass).addClass(e.options.errorClass)},_resetClass:function(e){e._ui.$errorClassHandler.removeClass(e.options.successClass).removeClass(e.options.errorClass)}};var a=function(i,n,s,o){this.__class__="OptionsFactory",this.__id__=t.hash(4),this.formOptions=null,this.fieldOptions=null,this.staticOptions=e.extend(!0,{},i,n,s,{namespace:o})};a.prototype={get:function(e){if(void 0===e.__class__)throw new Error("Parsley Instance expected");switch(e.__class__){case"Parsley":return this.staticOptions;case"ParsleyForm":return this.getFormOptions(e);case"ParsleyField":case"ParsleyFieldMultiple":return this.getFieldOptions(e);default:throw new Error("Instance "+e.__class__+" is not supported")}},getFormOptions:function(i){return this.formOptions=t.attr(i.$element,this.staticOptions.namespace),e.extend({},this.staticOptions,this.formOptions)},getFieldOptions:function(i){return this.fieldOptions=t.attr(i.$element,this.staticOptions.namespace),null===this.formOptions&&void 0!==i.parent&&(this.formOptions=this.getFormOptions(i.parent)),e.extend({},this.staticOptions,this.formOptions,this.fieldOptions)}};var l=function(i,n){if(this.__class__="ParsleyForm",this.__id__=t.hash(4),"OptionsFactory"!==t.get(n,"__class__"))throw new Error("You must give an OptionsFactory instance");this.OptionsFactory=n,this.$element=e(i),this.validationResult=null,this.options=this.OptionsFactory.get(this)};l.prototype={onSubmitValidate:function(t){return this.validate(void 0,void 0,t),!1===this.validationResult&&t instanceof e.Event&&(t.stopImmediatePropagation(),t.preventDefault()),this},validate:function(t,i,n){this.submitEvent=n,this.validationResult=!0;var s=[];this._refreshFields(),e.emit("parsley:form:validate",this);for(var o=0;o<this.fields.length;o++)(!t||this._isFieldInGroup(this.fields[o],t))&&!0!==(s=this.fields[o].validate(i))&&s.length>0&&this.validationResult&&(this.validationResult=!1);return e.emit("parsley:form:validated",this),this.validationResult},isValid:function(e,t){this._refreshFields();for(var i=0;i<this.fields.length;i++)if((!e||this._isFieldInGroup(this.fields[i],e))&&!1===this.fields[i].isValid(t))return!1;return!0},_isFieldInGroup:function(i,n){return t.isArray(i.options.group)?-1!==e.inArray(i.options.group,n):i.options.group===n},_refreshFields:function(){return this.actualizeOptions()._bindFields()},_bindFields:function(){var e=this;return this.fields=[],this.fieldsMappedById={},this.$element.find(this.options.inputs).each(function(){var t=new window.Parsley(this,{},e);"ParsleyField"!==t.__class__&&"ParsleyFieldMultiple"!==t.__class__||t.$element.is(t.options.excluded)||void 0===e.fieldsMappedById[t.__class__+"-"+t.__id__]&&(e.fieldsMappedById[t.__class__+"-"+t.__id__]=t,e.fields.push(t))}),this}};var d=function(i,n,s,o,r){if(!new RegExp("ParsleyField").test(t.get(i,"__class__")))throw new Error("ParsleyField or ParsleyFieldMultiple instance expected");if("function"!=typeof window.ParsleyValidator.validators[n]&&"Assert"!==window.ParsleyValidator.validators[n](s).__parentClass__)throw new Error("Valid validator expected");return o=o||function(e,i){return void 0!==e.options[i+"Priority"]?e.options[i+"Priority"]:t.get(window.ParsleyValidator.validators[i](s),"priority")||2}(i,n),"function"==typeof window.ParsleyValidator.validators[n](s).requirementsTransformer&&(s=window.ParsleyValidator.validators[n](s).requirementsTransformer()),e.extend(window.ParsleyValidator.validators[n](s),{name:n,requirements:s,priority:o,groups:[o],isDomConstraint:r||t.attr(i.$element,i.options.namespace,n)})},u=function(i,n,s){this.__class__="ParsleyField",this.__id__=t.hash(4),this.$element=e(i),void 0!==s?(this.parent=s,this.OptionsFactory=this.parent.OptionsFactory,this.options=this.OptionsFactory.get(this)):(this.OptionsFactory=n,this.options=this.OptionsFactory.get(this)),this.constraints=[],this.constraintsByName={},this.validationResult=[],this._bindConstraints()};u.prototype={validate:function(t){return this.value=this.getValue(),e.emit("parsley:field:validate",this),e.emit("parsley:field:"+(this.isValid(t,this.value)?"success":"error"),this),e.emit("parsley:field:validated",this),this.validationResult},isValid:function(e,t){this.refreshConstraints();var i=this._getConstraintsSortedPriorities();if(0===(t=t||this.getValue()).length&&!this._isRequired()&&void 0===this.options.validateIfEmpty&&!0!==e)return this.validationResult=[];if(!1===this.options.priorityEnabled)return!0===(this.validationResult=this.validateThroughValidator(t,this.constraints,"Any"));for(var n=0;n<i.length;n++)if(!0!==(this.validationResult=this.validateThroughValidator(t,this.constraints,i[n])))return!1;return!0},getValue:function(){var e;return e=void 0!==this.options.value?this.options.value:this.$element.val(),void 0===e||null===e?"":!0===this.options.trimValue?e.replace(/^\s+|\s+$/g,""):e},refreshConstraints:function(){return this.actualizeOptions()._bindConstraints()},addConstraint:function(e,t,i,n){if(e=e.toLowerCase(),"function"==typeof window.ParsleyValidator.validators[e]){var s=new d(this,e,t,i,n);"undefined"!==this.constraintsByName[s.name]&&this.removeConstraint(s.name),this.constraints.push(s),this.constraintsByName[s.name]=s}return this},removeConstraint:function(e){for(var t=0;t<this.constraints.length;t++)if(e===this.constraints[t].name){this.constraints.splice(t,1);break}return this},updateConstraint:function(e,t,i){return this.removeConstraint(e).addConstraint(e,t,i)},_bindConstraints:function(){for(var e=[],t=0;t<this.constraints.length;t++)!1===this.constraints[t].isDomConstraint&&e.push(this.constraints[t]);this.constraints=e;for(var i in this.options)this.addConstraint(i,this.options[i]);return this._bindHtml5Constraints()},_bindHtml5Constraints:function(){(this.$element.hasClass("required")||this.$element.attr("required"))&&this.addConstraint("required",!0,void 0,!0),"string"==typeof this.$element.attr("pattern")&&this.addConstraint("pattern",this.$element.attr("pattern"),void 0,!0),void 0!==this.$element.attr("min")&&void 0!==this.$element.attr("max")?this.addConstraint("range",[this.$element.attr("min"),this.$element.attr("max")],void 0,!0):void 0!==this.$element.attr("min")?this.addConstraint("min",this.$element.attr("min"),void 0,!0):void 0!==this.$element.attr("max")&&this.addConstraint("max",this.$element.attr("max"),void 0,!0);var e=this.$element.attr("type");return void 0===e?this:"number"===e?this.addConstraint("type","integer",void 0,!0):new RegExp(e,"i").test("email url range")?this.addConstraint("type",e,void 0,!0):this},_isRequired:function(){return void 0!==this.constraintsByName.required&&!1!==this.constraintsByName.required.requirements},_getConstraintsSortedPriorities:function(){for(var e=[],t=0;t<this.constraints.length;t++)-1===e.indexOf(this.constraints[t].priority)&&e.push(this.constraints[t].priority);return e.sort(function(e,t){return t-e}),e}};var c=function(){this.__class__="ParsleyFieldMultiple"};c.prototype={addElement:function(e){return this.$elements.push(e),this},refreshConstraints:function(){var t;if(this.constraints=[],this.$element.is("select"))return this.actualizeOptions()._bindConstraints(),this;for(var i=0;i<this.$elements.length;i++)if(e("html").has(this.$elements[i]).length){t=this.$elements[i].data("ParsleyFieldMultiple").refreshConstraints().constraints;for(var n=0;n<t.length;n++)this.addConstraint(t[n].name,t[n].requirements,t[n].priority,t[n].isDomConstraint)}else this.$elements.splice(i,1);return this},getValue:function(){if(void 0!==this.options.value)return this.options.value;if(this.$element.is("input[type=radio]"))return e("["+this.options.namespace+'multiple="'+this.options.multiple+'"]:checked').val()||"";if(this.$element.is("input[type=checkbox]")){var t=[];return e("["+this.options.namespace+'multiple="'+this.options.multiple+'"]:checked').each(function(){t.push(e(this).val())}),t.length?t:[]}return this.$element.is("select")&&null===this.$element.val()?[]:this.$element.val()},_init:function(e){return this.$elements=[this.$element],this.options.multiple=e,this}};var h=e({}),f={};e.listen=function(e){if(void 0===f[e]&&(f[e]=[]),"function"==typeof arguments[1])return f[e].push({fn:arguments[1]});if("object"==typeof arguments[1]&&"function"==typeof arguments[2])return f[e].push({fn:arguments[2],ctxt:arguments[1]});throw new Error("Wrong parameters")},e.listenTo=function(e,t,i){if(void 0===f[t]&&(f[t]=[]),!(e instanceof u||e instanceof l))throw new Error("Must give Parsley instance");if("string"!=typeof t||"function"!=typeof i)throw new Error("Wrong parameters");f[t].push({instance:e,fn:i})},e.unsubscribe=function(e,t){if(void 0!==f[e]){if("string"!=typeof e||"function"!=typeof t)throw new Error("Wrong arguments");for(var i=0;i<f[e].length;i++)if(f[e][i].fn===t)return f[e].splice(i,1)}},e.unsubscribeTo=function(e,t){if(void 0!==f[t]){if(!(e instanceof u||e instanceof l))throw new Error("Must give Parsley instance");for(var i=0;i<f[t].length;i++)if(void 0!==f[t][i].instance&&f[t][i].instance.__id__===e.__id__)return f[t].splice(i,1)}},e.unsubscribeAll=function(e){void 0!==f[e]&&delete f[e]},e.emit=function(e,t){if(void 0!==f[e])for(var i=0;i<f[e].length;i++)if(void 0!==f[e][i].instance){if(t instanceof u||t instanceof l)if(f[e][i].instance.__id__!==t.__id__){if(f[e][i].instance instanceof l&&t instanceof u)for(var n=0;n<f[e][i].instance.fields.length;n++)if(f[e][i].instance.fields[n].__id__===t.__id__){f[e][i].fn.apply(h,Array.prototype.slice.call(arguments,1));continue}}else f[e][i].fn.apply(h,Array.prototype.slice.call(arguments,1))}else f[e][i].fn.apply(void 0!==f[e][i].ctxt?f[e][i].ctxt:h,Array.prototype.slice.call(arguments,1))},e.subscribed=function(){return f},window.ParsleyConfig=window.ParsleyConfig||{},window.ParsleyConfig.i18n=window.ParsleyConfig.i18n||{},window.ParsleyConfig.i18n.en=e.extend(window.ParsleyConfig.i18n.en||{},{defaultMessage:"This value seems to be invalid.",type:{email:"This value should be a valid email.",url:"This value should be a valid url.",number:"This value should be a valid number.",integer:"This value should be a valid integer.",digits:"This value should be digits.",alphanum:"This value should be alphanumeric."},notblank:"This value should not be blank.",required:"This value is required.",pattern:"This value seems to be invalid.",min:"This value should be greater than or equal to %s.",max:"This value should be lower than or equal to %s.",range:"This value should be between %s and %s.",minlength:"This value is too short. It should have %s characters or more.",maxlength:"This value is too long. It should have %s characters or fewer.",length:"This value length is invalid. It should be between %s and %s characters long.",mincheck:"You must select at least %s choices.",maxcheck:"You must select %s choices or fewer.",check:"You must select between %s and %s choices.",equalto:"This value should be the same."}),void 0!==window.ParsleyValidator&&window.ParsleyValidator.addCatalog("en",window.ParsleyConfig.i18n.en,!0);var p=function(i,n,s){if(this.__class__="Parsley",this.__version__="2.0.5",this.__id__=t.hash(4),void 0===i)throw new Error("You must give an element");if(void 0!==s&&"ParsleyForm"!==s.__class__)throw new Error("Parent instance must be a ParsleyForm instance");return this.init(e(i),n,s)};p.prototype={init:function(e,n,s){if(!e.length)throw new Error("You must bind Parsley on an existing element.");if(this.$element=e,this.$element.data("Parsley")){var o=this.$element.data("Parsley");return void 0!==s&&(o.parent=s),o}return this.OptionsFactory=new a(i,t.get(window,"ParsleyConfig")||{},n,this.getNamespace(n)),this.options=this.OptionsFactory.get(this),this.$element.is("form")||t.attr(this.$element,this.options.namespace,"validate")&&!this.$element.is(this.options.inputs)?this.bind("parsleyForm"):this.$element.is(this.options.inputs)&&!this.$element.is(this.options.excluded)?this.isMultiple()?this.handleMultiple(s):this.bind("parsleyField",s):this},isMultiple:function(){return this.$element.is("input[type=radio], input[type=checkbox]")&&void 0===this.options.multiple||this.$element.is("select")&&void 0!==this.$element.attr("multiple")},handleMultiple:function(i){var n,s,o,r=this;if(this.options=e.extend(this.options,i?i.OptionsFactory.get(i):{},t.attr(this.$element,this.options.namespace)),this.options.multiple?s=this.options.multiple:void 0!==this.$element.attr("name")&&this.$element.attr("name").length?s=n=this.$element.attr("name"):void 0!==this.$element.attr("id")&&this.$element.attr("id").length&&(s=this.$element.attr("id")),this.$element.is("select")&&void 0!==this.$element.attr("multiple"))return this.bind("parsleyFieldMultiple",i,s||this.__id__);if(void 0===s)return window.console&&window.console.warn&&window.console.warn("To be binded by Parsley, a radio, a checkbox and a multiple select input must have either a name or a multiple option.",this.$element),this;if(s=s.replace(/(:|\.|\[|\]|\$)/g,""),void 0!==n&&e('input[name="'+n+'"]').each(function(){e(this).is("input[type=radio], input[type=checkbox]")&&e(this).attr(r.options.namespace+"multiple",s)}),e("["+this.options.namespace+"multiple="+s+"]").length)for(var a=0;a<e("["+this.options.namespace+"multiple="+s+"]").length;a++)if(void 0!==e(e("["+this.options.namespace+"multiple="+s+"]").get(a)).data("Parsley")){o=e(e("["+this.options.namespace+"multiple="+s+"]").get(a)).data("Parsley"),this.$element.data("ParsleyFieldMultiple")||(o.addElement(this.$element),this.$element.attr(this.options.namespace+"id",o.__id__));break}return this.bind("parsleyField",i,s,!0),o||this.bind("parsleyFieldMultiple",i,s)},getNamespace:function(e){return void 0!==this.$element.data("parsleyNamespace")?this.$element.data("parsleyNamespace"):void 0!==t.get(e,"namespace")?e.namespace:void 0!==t.get(window,"ParsleyConfig.namespace")?window.ParsleyConfig.namespace:i.namespace},bind:function(i,s,o,r){var a;switch(i){case"parsleyForm":a=e.extend(new l(this.$element,this.OptionsFactory),new n,window.ParsleyExtend)._bindFields();break;case"parsleyField":a=e.extend(new u(this.$element,this.OptionsFactory,s),new n,window.ParsleyExtend);break;case"parsleyFieldMultiple":a=e.extend(new u(this.$element,this.OptionsFactory,s),new n,new c,window.ParsleyExtend)._init(o);break;default:throw new Error(i+"is not a supported Parsley type")}return void 0!==o&&t.setAttr(this.$element,this.options.namespace,"multiple",o),void 0!==r?(this.$element.data("ParsleyFieldMultiple",a),a):(new RegExp("ParsleyF","i").test(a.__class__)&&(this.$element.data("Parsley",a),e.emit("parsley:"+("parsleyForm"===i?"form":"field")+":init",a)),a)}},e.fn.parsley=e.fn.psly=function(t){if(this.length>1){var i=[];return this.each(function(){i.push(e(this).parsley(t))}),i}return e(this).length?new p(this,t):void(window.console&&window.console.warn&&window.console.warn("You must bind Parsley on an existing element."))},window.ParsleyUI="function"==typeof t.get(window,"ParsleyConfig.ParsleyUI")?(new window.ParsleyConfig.ParsleyUI).listen():(new r).listen(),void 0===window.ParsleyExtend&&(window.ParsleyExtend={}),void 0===window.ParsleyConfig&&(window.ParsleyConfig={}),window.Parsley=window.psly=p,window.ParsleyUtils=t,window.ParsleyValidator=new o(window.ParsleyConfig.validators,window.ParsleyConfig.i18n),!1!==t.get(window,"ParsleyConfig.autoBind")&&e(document).ready(function(){e("[data-parsley-validate]").length&&e("[data-parsley-validate]").parsley()})}),function(e){function t(e,t,i){switch(arguments.length){case 2:return null!=e?e:t;case 3:return null!=e?e:null!=t?t:i;default:throw new Error("Implement me")}}function i(e,t){return ge.call(e,t)}function n(){return{empty:!1,unusedTokens:[],unusedInput:[],overflow:-2,charsLeftOver:0,nullInput:!1,invalidMonth:null,invalidFormat:!1,userInvalidated:!1,iso:!1}}function s(e){!1===me.suppressDeprecationWarnings&&"undefined"!=typeof console&&console.warn&&console.warn("Deprecation warning: "+e)}function o(e,t){var i=!0;return c(function(){return i&&(s(e),i=!1),t.apply(this,arguments)},t)}function r(e,t){ut[e]||(s(t),ut[e]=!0)}function a(e,t){return function(i){return p(e.call(this,i),t)}}function l(){}function d(e,t){!1!==t&&D(e),h(this,e),this._d=new Date(+e._d)}function u(e){var t=b(e),i=t.year||0,n=t.quarter||0,s=t.month||0,o=t.week||0,r=t.day||0,a=t.hour||0,l=t.minute||0,d=t.second||0,u=t.millisecond||0;this._milliseconds=+u+1e3*d+6e4*l+36e5*a,this._days=+r+7*o,this._months=+s+3*n+12*i,this._data={},this._locale=me.localeData(),this._bubble()}function c(e,t){for(var n in t)i(t,n)&&(e[n]=t[n]);return i(t,"toString")&&(e.toString=t.toString),i(t,"valueOf")&&(e.valueOf=t.valueOf),e}function h(e,t){var i,n,s;if(void 0!==t._isAMomentObject&&(e._isAMomentObject=t._isAMomentObject),void 0!==t._i&&(e._i=t._i),void 0!==t._f&&(e._f=t._f),void 0!==t._l&&(e._l=t._l),void 0!==t._strict&&(e._strict=t._strict),void 0!==t._tzm&&(e._tzm=t._tzm),void 0!==t._isUTC&&(e._isUTC=t._isUTC),void 0!==t._offset&&(e._offset=t._offset),void 0!==t._pf&&(e._pf=t._pf),void 0!==t._locale&&(e._locale=t._locale),De.length>0)for(i in De)void 0!==(s=t[n=De[i]])&&(e[n]=s);return e}function f(e){return e<0?Math.ceil(e):Math.floor(e)}function p(e,t,i){for(var n=""+Math.abs(e),s=e>=0;n.length<t;)n="0"+n;return(s?i?"+":"":"-")+n}function m(e,t){var i={milliseconds:0,months:0};return i.months=t.month()-e.month()+12*(t.year()-e.year()),e.clone().add(i.months,"M").isAfter(t)&&--i.months,i.milliseconds=+t-+e.clone().add(i.months,"M"),i}function v(e,t){var i;return t=E(t,e),e.isBefore(t)?i=m(e,t):((i=m(t,e)).milliseconds=-i.milliseconds,i.months=-i.months),i}function _(e,t){return function(i,n){var s,o;return null===n||isNaN(+n)||(r(t,"moment()."+t+"(period, number) is deprecated. Please use moment()."+t+"(number, period)."),o=i,i=n,n=o),i="string"==typeof i?+i:i,s=me.duration(i,n),y(this,s,e),this}}function y(e,t,i,n){var s=t._milliseconds,o=t._days,r=t._months;n=null==n||n,s&&e._d.setTime(+e._d+s*i),o&&ue(e,"Date",de(e,"Date")+o*i),r&&le(e,de(e,"Month")+r*i),n&&me.updateOffset(e,o||r)}function w(e){return"[object Array]"===Object.prototype.toString.call(e)}function g(e){return"[object Date]"===Object.prototype.toString.call(e)||e instanceof Date}function k(e,t,i){var n,s=Math.min(e.length,t.length),o=Math.abs(e.length-t.length),r=0;for(n=0;n<s;n++)(i&&e[n]!==t[n]||!i&&T(e[n])!==T(t[n]))&&r++;return r+o}function $(e){if(e){var t=e.toLowerCase().replace(/(.)s$/,"$1");e=nt[e]||st[t]||t}return e}function b(e){var t,n,s={};for(n in e)i(e,n)&&(t=$(n))&&(s[t]=e[n]);return s}function T(e){var t=+e,i=0;return 0!==t&&isFinite(t)&&(i=t>=0?Math.floor(t):Math.ceil(t)),i}function C(e,t){return new Date(Date.UTC(e,t+1,0)).getUTCDate()}function S(e,t,i){return se(me([e,11,31+t-i]),t,i).week}function x(e){return F(e)?366:365}function F(e){return e%4==0&&e%100!=0||e%400==0}function D(e){var t;e._a&&-2===e._pf.overflow&&(t=e._a[$e]<0||e._a[$e]>11?$e:e._a[be]<1||e._a[be]>C(e._a[ke],e._a[$e])?be:e._a[Te]<0||e._a[Te]>23?Te:e._a[Ce]<0||e._a[Ce]>59?Ce:e._a[Se]<0||e._a[Se]>59?Se:e._a[xe]<0||e._a[xe]>999?xe:-1,e._pf._overflowDayOfYear&&(t<ke||t>be)&&(t=be),e._pf.overflow=t)}function M(e){return null==e._isValid&&(e._isValid=!isNaN(e._d.getTime())&&e._pf.overflow<0&&!e._pf.empty&&!e._pf.invalidMonth&&!e._pf.nullInput&&!e._pf.invalidFormat&&!e._pf.userInvalidated,e._strict&&(e._isValid=e._isValid&&0===e._pf.charsLeftOver&&0===e._pf.unusedTokens.length)),e._isValid}function O(e){return e?e.toLowerCase().replace("_","-"):e}function P(e){for(var t,i,n,s,o=0;o<e.length;){for(t=(s=O(e[o]).split("-")).length,i=(i=O(e[o+1]))?i.split("-"):null;t>0;){if(n=Y(s.slice(0,t).join("-")))return n;if(i&&i.length>=t&&k(s,i,!0)>=t-1)break;t--}o++}return null}function Y(e){var t=null;if(!Fe[e]&&Me)try{t=me.locale(),require("./locale/"+e),me.locale(t)}catch(e){}return Fe[e]}function E(e,t){return t._isUTC?me(e).zone(t._offset||0):me(e).local()}function A(e){return e.match(/\[[\s\S]/)?e.replace(/^\[|\]$/g,""):e.replace(/\\/g,"")}function I(e){var t,i,n=e.match(Ee);for(t=0,i=n.length;t<i;t++)dt[n[t]]?n[t]=dt[n[t]]:n[t]=A(n[t]);return function(s){var o="";for(t=0;t<i;t++)o+=n[t]instanceof Function?n[t].call(s,e):n[t];return o}}function z(e,t){return e.isValid()?(t=W(t,e.localeData()),ot[t]||(ot[t]=I(t)),ot[t](e)):e.localeData().invalidDate()}function W(e,t){var i=5;for(Ae.lastIndex=0;i>=0&&Ae.test(e);)e=e.replace(Ae,function(e){return t.longDateFormat(e)||e}),Ae.lastIndex=0,i-=1;return e}function H(e,t){var i=t._strict;switch(e){case"Q":return Re;case"DDDD":return Be;case"YYYY":case"GGGG":case"gggg":return i?Ze:We;case"Y":case"G":case"g":return Je;case"YYYYYY":case"YYYYY":case"GGGGG":case"ggggg":return i?Xe:He;case"S":if(i)return Re;case"SS":if(i)return Ue;case"SSS":if(i)return Be;case"DDD":return ze;case"MMM":case"MMMM":case"dd":case"ddd":case"dddd":return Ne;case"a":case"A":return t._locale._meridiemParse;case"X":return Ge;case"Z":case"ZZ":return je;case"T":return qe;case"SSSS":return Le;case"MM":case"DD":case"YY":case"GG":case"gg":case"HH":case"hh":case"mm":case"ss":case"ww":case"WW":return i?Ue:Ie;case"M":case"D":case"d":case"H":case"h":case"m":case"s":case"w":case"W":case"e":case"E":return Ie;case"Do":return Ve;default:return new RegExp(B(U(e.replace("\\",""))))}}function L(e){var t=(e=e||"").match(je)||[],i=((t[t.length-1]||[])+"").match(tt)||["-",0,0],n=60*i[1]+T(i[2]);return"+"===i[0]?-n:n}function N(e,t,i){var n,s=i._a;switch(e){case"Q":null!=t&&(s[$e]=3*(T(t)-1));break;case"M":case"MM":null!=t&&(s[$e]=T(t)-1);break;case"MMM":case"MMMM":null!=(n=i._locale.monthsParse(t))?s[$e]=n:i._pf.invalidMonth=t;break;case"D":case"DD":null!=t&&(s[be]=T(t));break;case"Do":null!=t&&(s[be]=T(parseInt(t,10)));break;case"DDD":case"DDDD":null!=t&&(i._dayOfYear=T(t));break;case"YY":s[ke]=me.parseTwoDigitYear(t);break;case"YYYY":case"YYYYY":case"YYYYYY":s[ke]=T(t);break;case"a":case"A":i._isPm=i._locale.isPM(t);break;case"H":case"HH":case"h":case"hh":s[Te]=T(t);break;case"m":case"mm":s[Ce]=T(t);break;case"s":case"ss":s[Se]=T(t);break;case"S":case"SS":case"SSS":case"SSSS":s[xe]=T(1e3*("0."+t));break;case"X":i._d=new Date(1e3*parseFloat(t));break;case"Z":case"ZZ":i._useUTC=!0,i._tzm=L(t);break;case"dd":case"ddd":case"dddd":null!=(n=i._locale.weekdaysParse(t))?(i._w=i._w||{},i._w.d=n):i._pf.invalidWeekday=t;break;case"w":case"ww":case"W":case"WW":case"d":case"e":case"E":e=e.substr(0,1);case"gggg":case"GGGG":case"GGGGG":e=e.substr(0,2),t&&(i._w=i._w||{},i._w[e]=T(t));break;case"gg":case"GG":i._w=i._w||{},i._w[e]=me.parseTwoDigitYear(t)}}function j(e){var i,n,s,o,r,a,l;null!=(i=e._w).GG||null!=i.W||null!=i.E?(r=1,a=4,n=t(i.GG,e._a[ke],se(me(),1,4).year),s=t(i.W,1),o=t(i.E,1)):(r=e._locale._week.dow,a=e._locale._week.doy,n=t(i.gg,e._a[ke],se(me(),r,a).year),s=t(i.w,1),null!=i.d?(o=i.d)<r&&++s:o=null!=i.e?i.e+r:r),l=oe(n,s,o,a,r),e._a[ke]=l.year,e._dayOfYear=l.dayOfYear}function q(e){var i,n,s,o,r=[];if(!e._d){for(s=V(e),e._w&&null==e._a[be]&&null==e._a[$e]&&j(e),e._dayOfYear&&(o=t(e._a[ke],s[ke]),e._dayOfYear>x(o)&&(e._pf._overflowDayOfYear=!0),n=ee(o,0,e._dayOfYear),e._a[$e]=n.getUTCMonth(),e._a[be]=n.getUTCDate()),i=0;i<3&&null==e._a[i];++i)e._a[i]=r[i]=s[i];for(;i<7;i++)e._a[i]=r[i]=null==e._a[i]?2===i?1:0:e._a[i];e._d=(e._useUTC?ee:K).apply(null,r),null!=e._tzm&&e._d.setUTCMinutes(e._d.getUTCMinutes()+e._tzm)}}function G(e){var t;e._d||(t=b(e._i),e._a=[t.year,t.month,t.day,t.hour,t.minute,t.second,t.millisecond],q(e))}function V(e){var t=new Date;return e._useUTC?[t.getUTCFullYear(),t.getUTCMonth(),t.getUTCDate()]:[t.getFullYear(),t.getMonth(),t.getDate()]}function R(e){if(e._f!==me.ISO_8601){e._a=[],e._pf.empty=!0;var t,i,n,s,o,r=""+e._i,a=r.length,l=0;for(n=W(e._f,e._locale).match(Ee)||[],t=0;t<n.length;t++)s=n[t],(i=(r.match(H(s,e))||[])[0])&&((o=r.substr(0,r.indexOf(i))).length>0&&e._pf.unusedInput.push(o),r=r.slice(r.indexOf(i)+i.length),l+=i.length),dt[s]?(i?e._pf.empty=!1:e._pf.unusedTokens.push(s),N(s,i,e)):e._strict&&!i&&e._pf.unusedTokens.push(s);e._pf.charsLeftOver=a-l,r.length>0&&e._pf.unusedInput.push(r),e._isPm&&e._a[Te]<12&&(e._a[Te]+=12),!1===e._isPm&&12===e._a[Te]&&(e._a[Te]=0),q(e),D(e)}else X(e)}function U(e){return e.replace(/\\(\[)|\\(\])|\[([^\]\[]*)\]|\\(.)/g,function(e,t,i,n,s){return t||i||n||s})}function B(e){return e.replace(/[-\/\\^$*+?.()|[\]{}]/g,"\\$&")}function Z(e){var t,i,s,o,r;if(0===e._f.length)return e._pf.invalidFormat=!0,void(e._d=new Date(NaN));for(o=0;o<e._f.length;o++)r=0,(t=h({},e))._pf=n(),t._f=e._f[o],R(t),M(t)&&(r+=t._pf.charsLeftOver,r+=10*t._pf.unusedTokens.length,t._pf.score=r,(null==s||r<s)&&(s=r,i=t));c(e,i||t)}function X(e){var t,i,n=e._i,s=Qe.exec(n);if(s){for(e._pf.iso=!0,t=0,i=Ke.length;t<i;t++)if(Ke[t][1].exec(n)){e._f=Ke[t][0]+(s[6]||" ");break}for(t=0,i=et.length;t<i;t++)if(et[t][1].exec(n)){e._f+=et[t][0];break}n.match(je)&&(e._f+="Z"),R(e)}else e._isValid=!1}function J(e){X(e),!1===e._isValid&&(delete e._isValid,me.createFromInputFallback(e))}function Q(t){var i,n=t._i;n===e?t._d=new Date:g(n)?t._d=new Date(+n):null!==(i=Oe.exec(n))?t._d=new Date(+i[1]):"string"==typeof n?J(t):w(n)?(t._a=n.slice(0),q(t)):"object"==typeof n?G(t):"number"==typeof n?t._d=new Date(n):me.createFromInputFallback(t)}function K(e,t,i,n,s,o,r){var a=new Date(e,t,i,n,s,o,r);return e<1970&&a.setFullYear(e),a}function ee(e){var t=new Date(Date.UTC.apply(null,arguments));return e<1970&&t.setUTCFullYear(e),t}function te(e,t){if("string"==typeof e)if(isNaN(e)){if("number"!=typeof(e=t.weekdaysParse(e)))return null}else e=parseInt(e,10);return e}function ie(e,t,i,n,s){return s.relativeTime(t||1,!!i,e,n)}function ne(e,t,i){var n=me.duration(e).abs(),s=we(n.as("s")),o=we(n.as("m")),r=we(n.as("h")),a=we(n.as("d")),l=we(n.as("M")),d=we(n.as("y")),u=s<rt.s&&["s",s]||1===o&&["m"]||o<rt.m&&["mm",o]||1===r&&["h"]||r<rt.h&&["hh",r]||1===a&&["d"]||a<rt.d&&["dd",a]||1===l&&["M"]||l<rt.M&&["MM",l]||1===d&&["y"]||["yy",d];return u[2]=t,u[3]=+e>0,u[4]=i,ie.apply({},u)}function se(e,t,i){var n,s=i-t,o=i-e.day();return o>s&&(o-=7),o<s-7&&(o+=7),n=me(e).add(o,"d"),{week:Math.ceil(n.dayOfYear()/7),year:n.year()}}function oe(e,t,i,n,s){var o,r,a=ee(e,0,1).getUTCDay();return a=0===a?7:a,i=null!=i?i:s,o=s-a+(a>n?7:0)-(a<s?7:0),r=7*(t-1)+(i-s)+o+1,{year:r>0?e:e-1,dayOfYear:r>0?r:x(e-1)+r}}function re(t){var i=t._i,n=t._f;return t._locale=t._locale||me.localeData(t._l),null===i||n===e&&""===i?me.invalid({nullInput:!0}):("string"==typeof i&&(t._i=i=t._locale.preparse(i)),me.isMoment(i)?new d(i,!0):(n?w(n)?Z(t):R(t):Q(t),new d(t)))}function ae(e,t){var i,n;if(1===t.length&&w(t[0])&&(t=t[0]),!t.length)return me();for(i=t[0],n=1;n<t.length;++n)t[n][e](i)&&(i=t[n]);return i}function le(e,t){var i;return"string"==typeof t&&"number"!=typeof(t=e.localeData().monthsParse(t))?e:(i=Math.min(e.date(),C(e.year(),t)),e._d["set"+(e._isUTC?"UTC":"")+"Month"](t,i),e)}function de(e,t){return e._d["get"+(e._isUTC?"UTC":"")+t]()}function ue(e,t,i){return"Month"===t?le(e,i):e._d["set"+(e._isUTC?"UTC":"")+t](i)}function ce(e,t){return function(i){return null!=i?(ue(this,e,i),me.updateOffset(this,t),this):de(this,e)}}function he(e){return 400*e/146097}function fe(e){return 146097*e/400}function pe(e){"undefined"==typeof ender&&(ve=ye.moment,ye.moment=e?o("Accessing Moment through the global scope is deprecated, and will be removed in an upcoming release.",me):me)}for(var me,ve,_e,ye="undefined"!=typeof global?global:this,we=Math.round,ge=Object.prototype.hasOwnProperty,ke=0,$e=1,be=2,Te=3,Ce=4,Se=5,xe=6,Fe={},De=[],Me="undefined"!=typeof module&&module.exports,Oe=/^\/?Date\((\-?\d+)/i,Pe=/(\-)?(?:(\d*)\.)?(\d+)\:(\d+)(?:\:(\d+)\.?(\d{3})?)?/,Ye=/^(-)?P(?:(?:([0-9,.]*)Y)?(?:([0-9,.]*)M)?(?:([0-9,.]*)D)?(?:T(?:([0-9,.]*)H)?(?:([0-9,.]*)M)?(?:([0-9,.]*)S)?)?|([0-9,.]*)W)$/,Ee=/(\[[^\[]*\])|(\\)?(Mo|MM?M?M?|Do|DDDo|DD?D?D?|ddd?d?|do?|w[o|w]?|W[o|W]?|Q|YYYYYY|YYYYY|YYYY|YY|gg(ggg?)?|GG(GGG?)?|e|E|a|A|hh?|HH?|mm?|ss?|S{1,4}|X|zz?|ZZ?|.)/g,Ae=/(\[[^\[]*\])|(\\)?(LT|LL?L?L?|l{1,4})/g,Ie=/\d\d?/,ze=/\d{1,3}/,We=/\d{1,4}/,He=/[+\-]?\d{1,6}/,Le=/\d+/,Ne=/[0-9]*['a-z\u00A0-\u05FF\u0700-\uD7FF\uF900-\uFDCF\uFDF0-\uFFEF]+|[\u0600-\u06FF\/]+(\s*?[\u0600-\u06FF]+){1,2}/i,je=/Z|[\+\-]\d\d:?\d\d/gi,qe=/T/i,Ge=/[\+\-]?\d+(\.\d{1,3})?/,Ve=/\d{1,2}/,Re=/\d/,Ue=/\d\d/,Be=/\d{3}/,Ze=/\d{4}/,Xe=/[+-]?\d{6}/,Je=/[+-]?\d+/,Qe=/^\s*(?:[+-]\d{6}|\d{4})-(?:(\d\d-\d\d)|(W\d\d$)|(W\d\d-\d)|(\d\d\d))((T| )(\d\d(:\d\d(:\d\d(\.\d+)?)?)?)?([\+\-]\d\d(?::?\d\d)?|\s*Z)?)?$/,Ke=[["YYYYYY-MM-DD",/[+-]\d{6}-\d{2}-\d{2}/],["YYYY-MM-DD",/\d{4}-\d{2}-\d{2}/],["GGGG-[W]WW-E",/\d{4}-W\d{2}-\d/],["GGGG-[W]WW",/\d{4}-W\d{2}/],["YYYY-DDD",/\d{4}-\d{3}/]],et=[["HH:mm:ss.SSSS",/(T| )\d\d:\d\d:\d\d\.\d+/],["HH:mm:ss",/(T| )\d\d:\d\d:\d\d/],["HH:mm",/(T| )\d\d:\d\d/],["HH",/(T| )\d\d/]],tt=/([\+\-]|\d\d)/gi,it=("Date|Hours|Minutes|Seconds|Milliseconds".split("|"),{Milliseconds:1,Seconds:1e3,Minutes:6e4,Hours:36e5,Days:864e5,Months:2592e6,Years:31536e6}),nt={ms:"millisecond",s:"second",m:"minute",h:"hour",d:"day",D:"date",w:"week",W:"isoWeek",M:"month",Q:"quarter",y:"year",DDD:"dayOfYear",e:"weekday",E:"isoWeekday",gg:"weekYear",GG:"isoWeekYear"},st={dayofyear:"dayOfYear",isoweekday:"isoWeekday",isoweek:"isoWeek",weekyear:"weekYear",isoweekyear:"isoWeekYear"},ot={},rt={s:45,m:45,h:22,d:26,M:11},at="DDD w W M D d".split(" "),lt="M D H h m s w W".split(" "),dt={M:function(){return this.month()+1},MMM:function(e){return this.localeData().monthsShort(this,e)},MMMM:function(e){return this.localeData().months(this,e)},D:function(){return this.date()},DDD:function(){return this.dayOfYear()},d:function(){return this.day()},dd:function(e){return this.localeData().weekdaysMin(this,e)},ddd:function(e){return this.localeData().weekdaysShort(this,e)},dddd:function(e){return this.localeData().weekdays(this,e)},w:function(){return this.week()},W:function(){return this.isoWeek()},YY:function(){return p(this.year()%100,2)},YYYY:function(){return p(this.year(),4)},YYYYY:function(){return p(this.year(),5)},YYYYYY:function(){var e=this.year();return(e>=0?"+":"-")+p(Math.abs(e),6)},gg:function(){return p(this.weekYear()%100,2)},gggg:function(){return p(this.weekYear(),4)},ggggg:function(){return p(this.weekYear(),5)},GG:function(){return p(this.isoWeekYear()%100,2)},GGGG:function(){return p(this.isoWeekYear(),4)},GGGGG:function(){return p(this.isoWeekYear(),5)},e:function(){return this.weekday()},E:function(){return this.isoWeekday()},a:function(){return this.localeData().meridiem(this.hours(),this.minutes(),!0)},A:function(){return this.localeData().meridiem(this.hours(),this.minutes(),!1)},H:function(){return this.hours()},h:function(){return this.hours()%12||12},m:function(){return this.minutes()},s:function(){return this.seconds()},S:function(){return T(this.milliseconds()/100)},SS:function(){return p(T(this.milliseconds()/10),2)},SSS:function(){return p(this.milliseconds(),3)},SSSS:function(){return p(this.milliseconds(),3)},Z:function(){var e=-this.zone(),t="+";return e<0&&(e=-e,t="-"),t+p(T(e/60),2)+":"+p(T(e)%60,2)},ZZ:function(){var e=-this.zone(),t="+";return e<0&&(e=-e,t="-"),t+p(T(e/60),2)+p(T(e)%60,2)},z:function(){return this.zoneAbbr()},zz:function(){return this.zoneName()},X:function(){return this.unix()},Q:function(){return this.quarter()}},ut={},ct=["months","monthsShort","weekdays","weekdaysShort","weekdaysMin"];at.length;)_e=at.pop(),dt[_e+"o"]=function(e,t){return function(i){return this.localeData().ordinal(e.call(this,i),t)}}(dt[_e],_e);for(;lt.length;)_e=lt.pop(),dt[_e+_e]=a(dt[_e],2);dt.DDDD=a(dt.DDD,3),c(l.prototype,{set:function(e){var t,i;for(i in e)"function"==typeof(t=e[i])?this[i]=t:this["_"+i]=t},_months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),months:function(e){return this._months[e.month()]},_monthsShort:"Jan_Feb_Mar_Apr_May_Jun_Jul_Aug_Sep_Oct_Nov_Dec".split("_"),monthsShort:function(e){return this._monthsShort[e.month()]},monthsParse:function(e){var t,i,n;for(this._monthsParse||(this._monthsParse=[]),t=0;t<12;t++)if(this._monthsParse[t]||(i=me.utc([2e3,t]),n="^"+this.months(i,"")+"|^"+this.monthsShort(i,""),this._monthsParse[t]=new RegExp(n.replace(".",""),"i")),this._monthsParse[t].test(e))return t},_weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),weekdays:function(e){return this._weekdays[e.day()]},_weekdaysShort:"Sun_Mon_Tue_Wed_Thu_Fri_Sat".split("_"),weekdaysShort:function(e){return this._weekdaysShort[e.day()]},_weekdaysMin:"Su_Mo_Tu_We_Th_Fr_Sa".split("_"),weekdaysMin:function(e){return this._weekdaysMin[e.day()]},weekdaysParse:function(e){var t,i,n;for(this._weekdaysParse||(this._weekdaysParse=[]),t=0;t<7;t++)if(this._weekdaysParse[t]||(i=me([2e3,1]).day(t),n="^"+this.weekdays(i,"")+"|^"+this.weekdaysShort(i,"")+"|^"+this.weekdaysMin(i,""),this._weekdaysParse[t]=new RegExp(n.replace(".",""),"i")),this._weekdaysParse[t].test(e))return t},_longDateFormat:{LT:"h:mm A",L:"MM/DD/YYYY",LL:"MMMM D, YYYY",LLL:"MMMM D, YYYY LT",LLLL:"dddd, MMMM D, YYYY LT"},longDateFormat:function(e){var t=this._longDateFormat[e];return!t&&this._longDateFormat[e.toUpperCase()]&&(t=this._longDateFormat[e.toUpperCase()].replace(/MMMM|MM|DD|dddd/g,function(e){return e.slice(1)}),this._longDateFormat[e]=t),t},isPM:function(e){return"p"===(e+"").toLowerCase().charAt(0)},_meridiemParse:/[ap]\.?m?\.?/i,meridiem:function(e,t,i){return e>11?i?"pm":"PM":i?"am":"AM"},_calendar:{sameDay:"[Today at] LT",nextDay:"[Tomorrow at] LT",nextWeek:"dddd [at] LT",lastDay:"[Yesterday at] LT",lastWeek:"[Last] dddd [at] LT",sameElse:"L"},calendar:function(e,t){var i=this._calendar[e];return"function"==typeof i?i.apply(t):i},_relativeTime:{future:"in %s",past:"%s ago",s:"a few seconds",m:"a minute",mm:"%d minutes",h:"an hour",hh:"%d hours",d:"a day",dd:"%d days",M:"a month",MM:"%d months",y:"a year",yy:"%d years"},relativeTime:function(e,t,i,n){var s=this._relativeTime[i];return"function"==typeof s?s(e,t,i,n):s.replace(/%d/i,e)},pastFuture:function(e,t){var i=this._relativeTime[e>0?"future":"past"];return"function"==typeof i?i(t):i.replace(/%s/i,t)},ordinal:function(e){return this._ordinal.replace("%d",e)},_ordinal:"%d",preparse:function(e){return e},postformat:function(e){return e},week:function(e){return se(e,this._week.dow,this._week.doy).week},_week:{dow:0,doy:6},_invalidDate:"Invalid date",invalidDate:function(){return this._invalidDate}}),(me=function(t,i,s,o){var r;return"boolean"==typeof s&&(o=s,s=e),r={},r._isAMomentObject=!0,r._i=t,r._f=i,r._l=s,r._strict=o,r._isUTC=!1,r._pf=n(),re(r)}).suppressDeprecationWarnings=!1,me.createFromInputFallback=o("moment construction falls back to js Date. This is discouraged and will be removed in upcoming major release. Please refer to https://github.com/moment/moment/issues/1407 for more info.",function(e){e._d=new Date(e._i)}),me.min=function(){return ae("isBefore",[].slice.call(arguments,0))},me.max=function(){return ae("isAfter",[].slice.call(arguments,0))},me.utc=function(t,i,s,o){var r;return"boolean"==typeof s&&(o=s,s=e),r={},r._isAMomentObject=!0,r._useUTC=!0,r._isUTC=!0,r._l=s,r._i=t,r._f=i,r._strict=o,r._pf=n(),re(r).utc()},me.unix=function(e){return me(1e3*e)},me.duration=function(e,t){var n,s,o,r,a=e,l=null;return me.isDuration(e)?a={ms:e._milliseconds,d:e._days,M:e._months}:"number"==typeof e?(a={},t?a[t]=e:a.milliseconds=e):(l=Pe.exec(e))?(n="-"===l[1]?-1:1,a={y:0,d:T(l[be])*n,h:T(l[Te])*n,m:T(l[Ce])*n,s:T(l[Se])*n,ms:T(l[xe])*n}):(l=Ye.exec(e))?(n="-"===l[1]?-1:1,a={y:(o=function(e){var t=e&&parseFloat(e.replace(",","."));return(isNaN(t)?0:t)*n})(l[2]),M:o(l[3]),d:o(l[4]),h:o(l[5]),m:o(l[6]),s:o(l[7]),w:o(l[8])}):"object"==typeof a&&("from"in a||"to"in a)&&(r=v(me(a.from),me(a.to)),(a={}).ms=r.milliseconds,a.M=r.months),s=new u(a),me.isDuration(e)&&i(e,"_locale")&&(s._locale=e._locale),s},me.version="2.8.2",me.defaultFormat="YYYY-MM-DDTHH:mm:ssZ",me.ISO_8601=function(){},me.momentProperties=De,me.updateOffset=function(){},me.relativeTimeThreshold=function(t,i){return rt[t]!==e&&(i===e?rt[t]:(rt[t]=i,!0))},me.lang=o("moment.lang is deprecated. Use moment.locale instead.",function(e,t){return me.locale(e,t)}),me.locale=function(e,t){var i;return e&&(i=void 0!==t?me.defineLocale(e,t):me.localeData(e))&&(me.duration._locale=me._locale=i),me._locale._abbr},me.defineLocale=function(e,t){return null!==t?(t.abbr=e,Fe[e]||(Fe[e]=new l),Fe[e].set(t),me.locale(e),Fe[e]):(delete Fe[e],null)},me.langData=o("moment.langData is deprecated. Use moment.localeData instead.",function(e){return me.localeData(e)}),me.localeData=function(e){var t;if(e&&e._locale&&e._locale._abbr&&(e=e._locale._abbr),!e)return me._locale;if(!w(e)){if(t=Y(e))return t;e=[e]}return P(e)},me.isMoment=function(e){return e instanceof d||null!=e&&i(e,"_isAMomentObject")},me.isDuration=function(e){return e instanceof u};for(_e=ct.length-1;_e>=0;--_e)!function(t){var i,n;if(0===t.indexOf("week"))i=7,n="day";else{if(0!==t.indexOf("month"))return;i=12,n="month"}me[t]=function(s,o){var r,a,l=me._locale[t],d=[];if("number"==typeof s&&(o=s,s=e),a=function(e){var t=me().utc().set(n,e);return l.call(me._locale,t,s||"")},null!=o)return a(o);for(r=0;r<i;r++)d.push(a(r));return d}}(ct[_e]);me.normalizeUnits=function(e){return $(e)},me.invalid=function(e){var t=me.utc(NaN);return null!=e?c(t._pf,e):t._pf.userInvalidated=!0,t},me.parseZone=function(){return me.apply(null,arguments).parseZone()},me.parseTwoDigitYear=function(e){return T(e)+(T(e)>68?1900:2e3)},c(me.fn=d.prototype,{clone:function(){return me(this)},valueOf:function(){return+this._d+6e4*(this._offset||0)},unix:function(){return Math.floor(+this/1e3)},toString:function(){return this.clone().locale("en").format("ddd MMM DD YYYY HH:mm:ss [GMT]ZZ")},toDate:function(){return this._offset?new Date(+this):this._d},toISOString:function(){var e=me(this).utc();return 0<e.year()&&e.year()<=9999?z(e,"YYYY-MM-DD[T]HH:mm:ss.SSS[Z]"):z(e,"YYYYYY-MM-DD[T]HH:mm:ss.SSS[Z]")},toArray:function(){var e=this;return[e.year(),e.month(),e.date(),e.hours(),e.minutes(),e.seconds(),e.milliseconds()]},isValid:function(){return M(this)},isDSTShifted:function(){return!!this._a&&(this.isValid()&&k(this._a,(this._isUTC?me.utc(this._a):me(this._a)).toArray())>0)},parsingFlags:function(){return c({},this._pf)},invalidAt:function(){return this._pf.overflow},utc:function(e){return this.zone(0,e)},local:function(e){return this._isUTC&&(this.zone(0,e),this._isUTC=!1,e&&this.add(this._d.getTimezoneOffset(),"m")),this},format:function(e){var t=z(this,e||me.defaultFormat);return this.localeData().postformat(t)},add:_(1,"add"),subtract:_(-1,"subtract"),diff:function(e,t,i){var n,s,o=E(e,this),r=6e4*(this.zone()-o.zone());return t=$(t),"year"===t||"month"===t?(n=432e5*(this.daysInMonth()+o.daysInMonth()),s=12*(this.year()-o.year())+(this.month()-o.month()),s+=(this-me(this).startOf("month")-(o-me(o).startOf("month")))/n,s-=6e4*(this.zone()-me(this).startOf("month").zone()-(o.zone()-me(o).startOf("month").zone()))/n,"year"===t&&(s/=12)):(n=this-o,s="second"===t?n/1e3:"minute"===t?n/6e4:"hour"===t?n/36e5:"day"===t?(n-r)/864e5:"week"===t?(n-r)/6048e5:n),i?s:f(s)},from:function(e,t){return me.duration({to:this,from:e}).locale(this.locale()).humanize(!t)},fromNow:function(e){return this.from(me(),e)},calendar:function(e){var t=E(e||me(),this).startOf("day"),i=this.diff(t,"days",!0),n=i<-6?"sameElse":i<-1?"lastWeek":i<0?"lastDay":i<1?"sameDay":i<2?"nextDay":i<7?"nextWeek":"sameElse";return this.format(this.localeData().calendar(n,this))},isLeapYear:function(){return F(this.year())},isDST:function(){return this.zone()<this.clone().month(0).zone()||this.zone()<this.clone().month(5).zone()},day:function(e){var t=this._isUTC?this._d.getUTCDay():this._d.getDay();return null!=e?(e=te(e,this.localeData()),this.add(e-t,"d")):t},month:ce("Month",!0),startOf:function(e){switch(e=$(e)){case"year":this.month(0);case"quarter":case"month":this.date(1);case"week":case"isoWeek":case"day":this.hours(0);case"hour":this.minutes(0);case"minute":this.seconds(0);case"second":this.milliseconds(0)}return"week"===e?this.weekday(0):"isoWeek"===e&&this.isoWeekday(1),"quarter"===e&&this.month(3*Math.floor(this.month()/3)),this},endOf:function(e){return e=$(e),this.startOf(e).add(1,"isoWeek"===e?"week":e).subtract(1,"ms")},isAfter:function(e,t){return t=void 0!==t?t:"millisecond",+this.clone().startOf(t)>+me(e).startOf(t)},isBefore:function(e,t){return t=void 0!==t?t:"millisecond",+this.clone().startOf(t)<+me(e).startOf(t)},isSame:function(e,t){return t=t||"ms",+this.clone().startOf(t)==+E(e,this).startOf(t)},min:o("moment().min is deprecated, use moment.min instead. https://github.com/moment/moment/issues/1548",function(e){return e=me.apply(null,arguments),e<this?this:e}),max:o("moment().max is deprecated, use moment.max instead. https://github.com/moment/moment/issues/1548",function(e){return e=me.apply(null,arguments),e>this?this:e}),zone:function(e,t){var i,n=this._offset||0;return null==e?this._isUTC?n:this._d.getTimezoneOffset():("string"==typeof e&&(e=L(e)),Math.abs(e)<16&&(e*=60),!this._isUTC&&t&&(i=this._d.getTimezoneOffset()),this._offset=e,this._isUTC=!0,null!=i&&this.subtract(i,"m"),n!==e&&(!t||this._changeInProgress?y(this,me.duration(n-e,"m"),1,!1):this._changeInProgress||(this._changeInProgress=!0,me.updateOffset(this,!0),this._changeInProgress=null)),this)},zoneAbbr:function(){return this._isUTC?"UTC":""},zoneName:function(){return this._isUTC?"Coordinated Universal Time":""},parseZone:function(){return this._tzm?this.zone(this._tzm):"string"==typeof this._i&&this.zone(this._i),this},hasAlignedHourOffset:function(e){return e=e?me(e).zone():0,(this.zone()-e)%60==0},daysInMonth:function(){return C(this.year(),this.month())},dayOfYear:function(e){var t=we((me(this).startOf("day")-me(this).startOf("year"))/864e5)+1;return null==e?t:this.add(e-t,"d")},quarter:function(e){return null==e?Math.ceil((this.month()+1)/3):this.month(3*(e-1)+this.month()%3)},weekYear:function(e){var t=se(this,this.localeData()._week.dow,this.localeData()._week.doy).year;return null==e?t:this.add(e-t,"y")},isoWeekYear:function(e){var t=se(this,1,4).year;return null==e?t:this.add(e-t,"y")},week:function(e){var t=this.localeData().week(this);return null==e?t:this.add(7*(e-t),"d")},isoWeek:function(e){var t=se(this,1,4).week;return null==e?t:this.add(7*(e-t),"d")},weekday:function(e){var t=(this.day()+7-this.localeData()._week.dow)%7;return null==e?t:this.add(e-t,"d")},isoWeekday:function(e){return null==e?this.day()||7:this.day(this.day()%7?e:e-7)},isoWeeksInYear:function(){return S(this.year(),1,4)},weeksInYear:function(){var e=this.localeData()._week;return S(this.year(),e.dow,e.doy)},get:function(e){return e=$(e),this[e]()},set:function(e,t){return e=$(e),"function"==typeof this[e]&&this[e](t),this},locale:function(t){return t===e?this._locale._abbr:(this._locale=me.localeData(t),this)},lang:o("moment().lang() is deprecated. Use moment().localeData() instead.",function(t){return t===e?this.localeData():(this._locale=me.localeData(t),this)}),localeData:function(){return this._locale}}),me.fn.millisecond=me.fn.milliseconds=ce("Milliseconds",!1),me.fn.second=me.fn.seconds=ce("Seconds",!1),me.fn.minute=me.fn.minutes=ce("Minutes",!1),me.fn.hour=me.fn.hours=ce("Hours",!0),me.fn.date=ce("Date",!0),me.fn.dates=o("dates accessor is deprecated. Use date instead.",ce("Date",!0)),me.fn.year=ce("FullYear",!0),me.fn.years=o("years accessor is deprecated. Use year instead.",ce("FullYear",!0)),me.fn.days=me.fn.day,me.fn.months=me.fn.month,me.fn.weeks=me.fn.week,me.fn.isoWeeks=me.fn.isoWeek,me.fn.quarters=me.fn.quarter,me.fn.toJSON=me.fn.toISOString,c(me.duration.fn=u.prototype,{_bubble:function(){var e,t,i,n=this._milliseconds,s=this._days,o=this._months,r=this._data,a=0;r.milliseconds=n%1e3,e=f(n/1e3),r.seconds=e%60,t=f(e/60),r.minutes=t%60,i=f(t/60),r.hours=i%24,s+=f(i/24),o+=f((s-=f(fe(a=f(he(s)))))/30),s%=30,a+=f(o/12),o%=12,r.days=s,r.months=o,r.years=a},abs:function(){return this._milliseconds=Math.abs(this._milliseconds),this._days=Math.abs(this._days),this._months=Math.abs(this._months),this._data.milliseconds=Math.abs(this._data.milliseconds),this._data.seconds=Math.abs(this._data.seconds),this._data.minutes=Math.abs(this._data.minutes),this._data.hours=Math.abs(this._data.hours),this._data.months=Math.abs(this._data.months),this._data.years=Math.abs(this._data.years),this},weeks:function(){return f(this.days()/7)},valueOf:function(){return this._milliseconds+864e5*this._days+this._months%12*2592e6+31536e6*T(this._months/12)},humanize:function(e){var t=ne(this,!e,this.localeData());return e&&(t=this.localeData().pastFuture(+this,t)),this.localeData().postformat(t)},add:function(e,t){var i=me.duration(e,t);return this._milliseconds+=i._milliseconds,this._days+=i._days,this._months+=i._months,this._bubble(),this},subtract:function(e,t){var i=me.duration(e,t);return this._milliseconds-=i._milliseconds,this._days-=i._days,this._months-=i._months,this._bubble(),this},get:function(e){return e=$(e),this[e.toLowerCase()+"s"]()},as:function(e){var t,i;if(e=$(e),t=this._days+this._milliseconds/864e5,"month"===e||"year"===e)return i=this._months+12*he(t),"month"===e?i:i/12;switch(t+=fe(this._months/12),e){case"week":return t/7;case"day":return t;case"hour":return 24*t;case"minute":return 24*t*60;case"second":return 24*t*60*60;case"millisecond":return 24*t*60*60*1e3;default:throw new Error("Unknown unit "+e)}},lang:me.fn.lang,locale:me.fn.locale,toIsoString:o("toIsoString() is deprecated. Please use toISOString() instead (notice the capitals)",function(){return this.toISOString()}),toISOString:function(){var e=Math.abs(this.years()),t=Math.abs(this.months()),i=Math.abs(this.days()),n=Math.abs(this.hours()),s=Math.abs(this.minutes()),o=Math.abs(this.seconds()+this.milliseconds()/1e3);return this.asSeconds()?(this.asSeconds()<0?"-":"")+"P"+(e?e+"Y":"")+(t?t+"M":"")+(i?i+"D":"")+(n||s||o?"T":"")+(n?n+"H":"")+(s?s+"M":"")+(o?o+"S":""):"P0D"},localeData:function(){return this._locale}}),me.duration.fn.toString=me.duration.fn.toISOString;for(_e in it)i(it,_e)&&function(e){me.duration.fn[e]=function(){return this._data[e]}}(_e.toLowerCase());me.duration.fn.asMilliseconds=function(){return this.as("ms")},me.duration.fn.asSeconds=function(){return this.as("s")},me.duration.fn.asMinutes=function(){return this.as("m")},me.duration.fn.asHours=function(){return this.as("h")},me.duration.fn.asDays=function(){return this.as("d")},me.duration.fn.asWeeks=function(){return this.as("weeks")},me.duration.fn.asMonths=function(){return this.as("M")},me.duration.fn.asYears=function(){return this.as("y")},me.locale("en",{ordinal:function(e){var t=e%10;return e+(1===T(e%100/10)?"th":1===t?"st":2===t?"nd":3===t?"rd":"th")}}),Me?module.exports=me:"function"==typeof define&&define.amd?(define("moment",function(e,t,i){return i.config&&i.config()&&!0===i.config().noGlobal&&(ye.moment=ve),me}),pe(!0)):pe()}.call(this),$(function(){$(window).on("resize",($(".user-details").height($(".dashboard-head").height()),$(".tags-panel").height($(".pro-dev-shared-resources").height()+60),void $(".events").height($(".events").parent().height()))),$(".choice-select").choice(),$(".choice-select-form-book").choice(),$(".choice-select-form-type").choice({selected:function(e,t){$(".field-wrap.hide").hide(),1==t?($(".field-wrap.link-field").show(),$('.field-wrap.link-field input[name="link"]').focus()):$(".field-wrap.file-field").show()}}),$(".select-link").children().each(function(){var e=window.location.href;$(this).prop("selected",!1),$(this).val()===e&&$(this).prop("selected",!0)}),$(".select-link").change(function(){var e=$(this).val();window.location.href=e}),$(".login").find(".field").on("focus",function(){$(this).parent().addClass("focus")}).on("blur",function(){$(this).parent().removeClass("focus")});$(".filters").find("a").on("click",function(e){e.preventDefault();var t=$(e.currentTarget).data("type"),i=$(".resources .wrap").children();if("all"===t)return i.show();i.hide(),$(".resource-wrap[data-resource="+t+"]").show()}),$(".resources").on("mouseenter",".resource",function(){$(this).find(".open-illustration, .open-video","open-link").fadeIn(100)}).on("mouseleave",".resource",function(){$(this).find(".open-illustration, .open-video","open-link").fadeOut(100)}),$(".user-nav-container").on("click",function(){$(this).toggleClass("active").find("ul").toggle()});var e=function(e){var t=$(".tips-table").find("tr");if("#"===e)return t.show();t.hide(),$(".tips-table tr[data-category="+e+"]").show()};$(".choice-select-tipscat").choice({selected:function(t,i){e(i)}});var i=function(e){var i="",n=$.getJSON("/user/book/"+e);template=new t($("#bookTemp").html()),n.then(function(e){$.each(e,function(e,t){var n=template.render(t);i+=n}),$(".books .wrap").empty().html(i)})};$(".choice-select-book").choice({selected:function(e,t){console.log(t),i(t)}});$(".resources").on("click",".open-illustration",function(e){e.preventDefault();var i=$(e.currentTarget),n=i.data("sliderid"),s=i.data("bookid"),o=$.getJSON("/user/book-slideshows-slides/"+n),r=new t($("#sliderTemp").html());container=4==s?$("<div />").addClass("slider-container-purple").hide():$("<div />").addClass("slider-container").hide(),o.then(function(e){var t=r.render({images:e});container.append(t).appendTo("body").fadeIn(300);var i=function(){var e=$(window).width()-100,t=$(window).height()-100;container.find(".slider-modal, .slider-modal .slider, .slider-modal .slide").width(e).height(t)};4==s&&$(".caption").css("background-color","#755080"),$(window).on("resize",i),i(),container.find(".slider").slick({autoplay:!1,autoplaySpeed:1e3,infinite:!1,prevArrow:".slider-previous",nextArrow:".slider-next",fade:!0}),container.on("click",".close",function(){container.fadeOut(300,function(){container.remove()})})})}),$(".btn-add-resource").on("click",function(e){e.preventDefault(),$(".add-resource-window").fadeIn()}),$(".close-form").on("click",function(){$(".add-resource-window").fadeOut()});var n=function(){var e=$(this).find("input"),t=$(this).find(".box");t.on("click",function(){e.prop("checked",!e.is(":checked")),t.toggleClass("checked")})};$(".register-check").each(n),$(".form-check").each(n),$(".register-field").on("focus blur",function(e){var t=$(this).parent().find("label"),i=t.innerWidth(),n="blur"===e.type?10:i+10;t.toggleClass("focused"),$(this).css({"padding-left":n})}),$(".register-form-wrap").find("label").on("click",function(){$(this).parent().find(".register-field").focus()})});