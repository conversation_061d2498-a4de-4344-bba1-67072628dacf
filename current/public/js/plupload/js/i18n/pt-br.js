// Brazilian Portuguese
plupload.addI18n({
   'Select files' : 'Escolha os arquivos',
   'Add files to the upload queue and click the start button.' : 'Adicione os arquivos abaixo e clique no botão "Iniciar o envio".',
   'Filename' : 'Nome do arquivo',
   'Status' : 'Status',
   'Size' : '<PERSON>an<PERSON>',
   'Add Files' : 'Adicionar arquivo(s)',
   'Stop Upload' : 'Parar o envio',
   'Start Upload' : 'Iniciar o envio',
   'Add files' : 'Adicionar arquivo(s)',
   'Add files.' : 'Adicionar arquivo(s)',
   'Stop upload' : 'Parar o envio',
   'Start upload' : 'Iniciar o envio',
   'Uploaded %d/%d files': 'Enviado(s) %d/%d arquivo(s)',
   'N/A' : 'N/D',
   'Drag files here.' : 'Arraste os arquivos pra cá',
   'File extension error.': 'Tipo de arquivo não permitido.',
   'File size error.': 'Tamanho de arquivo não permitido.',
   'File count error.': 'Erro na contagem dos arquivos',
   'Init error.': 'Erro inicializando.',
   'HTTP Error.': 'Erro HTTP.',
   'Security error.': 'Erro de segurança.',
   'Generic error.': 'Erro genérico.',
   'IO error.': 'Erro de E/S.',
   'File: %s': 'Arquivo: %s',
   'Close': 'Fechar',
   '%d files queued': '%d arquivo(s)',
   'Using runtime: ': 'Usando: ',
   'File: %f, size: %s, max file size: %m': 'Arquivo: %f, tamanho: %s, máximo: %m',
   'Upload element accepts only %d file(s) at a time. Extra files were stripped.': 'Só são aceitos %d arquivos por vez. O que passou disso foi descartado.',
   'Upload URL might be wrong or doesn\'t exist': 'URL de envio está errada ou não existe',
   'Error: File too large: ': 'Erro: Arquivo muito grande: ',
   'Error: Invalid file extension: ': 'Erro: Tipo de arquivo não permitido: '
});
