.shadow{overflow:hidden}.cpMainContainer{background-color:#2f2f2f;height:100%;width:100%;padding:0;position:absolute}.blocker{position:fixed;left:0;top:0;display:none}#slide_transition_canvas{display:none;left:0;top:0;position:absolute;width:100%;height:100%}.loadingBackground{background-color:#777;opacity:.5}.loadingString{color:#fff;font-family:"Lucida Sans Unicode","Lucida Grande",sans-serif;font-size:12px}.cp-movie{display:none;overflow:hidden;position:absolute}.cp-rewrap{display:block;position:absolute;-webkit-transform:translate3d(0px,0px,0px)}.cp-frameset{display:none;overflow:visible;position:absolute;-moz-user-select:none;-webkit-user-select:none;-ms-user-select:none;-webkit-transform:translate3d(0px,0px,0px)}.cp-input{display:block;position:absolute;left:0;top:0;width:0;height:0}.cp-audio{display:none;position:fixed;left:0;top:0;width:0;height:0}.cp-video,.cp-shape,.cp-mc,.cp-gf{display:block;position:absolute;left:0;top:0;width:0;height:0}body{background-color:#fff}.autoPlayButton,.autoPlayButton:hover{background:url('../htmlimages/Play_icon.png') no-repeat;width:116px;height:116px;float:left;position:absolute;left:50px}.cp-vtxt{display:block;position:absolute}.cp-actualText::first-line{line-height:100%}.gestureIcon{top:10px;right:0;width:32px;height:32px;position:absolute;display:block;background:url('../htmlimages/gesturemobileicon.png') no-repeat}.gestureHintDiv{left:0;top:0;right:0;bottom:0;margin:auto;width:100%;height:100%;z-index:10;display:none;position:fixed;background-color:rgba(0,0,0,0.5)}@media all and (max-width:335px){.gesturesHint{left:0;top:0;right:0;bottom:0;margin:auto;width:186px;height:469px;display:block;position:fixed;background:url('../htmlimages/gesturemobileimage2.png') no-repeat}}@media all and (max-width:531px) and (min-width:335px){.gesturesHint{left:0;top:0;right:0;bottom:0;margin:auto;width:335px;height:281px;display:block;position:fixed;background:url('../htmlimages/gesturemobilelandscape.png') no-repeat}}@media all and (min-width:531px){.gesturesHint{left:0;top:0;right:0;bottom:0;margin:auto;width:531px;height:277px;display:block;position:fixed;background:url('../htmlimages/gesturetabletimage.png') no-repeat}}.gestureAnimation{left:0;top:0;right:0;bottom:0;margin:auto;display:block;position:absolute}.playAnimation{width:104px;height:104px;background:url('../htmlimages/Play2x.gif') no-repeat}.pauseAnimation{width:104px;height:104px;background:url('../htmlimages/Pause2x.gif') no-repeat}.cp-liFirstLine{display:table-cell;padding-left:.5em}ul{list-style:none;margin:0;padding-left:0}li>ul,li>ol{list-style:none;padding-left:1em}ol{list-style:none;margin:0;padding-left:0}.cp-reviewUL{list-style:initial;margin:1em;text-indent:0}.cp-reviewULIE{list-style:circle;margin:1em;text-indent:0}.cp-numbering{display:table-cell;text-decoration:none;width:1em;text-align:right}.cp-accessibility{opacity:0;width:0;height:0;left:-1999px;position:fixed}.main_content{width:100%;height:80%;display:block;position:absolute;left:0%;top:0%;overflow:auto}.debugWinHolder{width:100%;height:30%;display:block;position:absolute;background-color:#e6e1e1;left:0%;bottom:0%}.reportWinHolder{width:0%;height:0%;display:block;position:absolute;right:0%;bottom:0%}.debugWin,.reportWin{width:100%;height:100%}.curtain{display:none;position:absolute;left:0;top:0;width:100%;height:100%;background-color:gray;z-index:100000;text-align:center}.curtainMsg{display:table-cell;vertical-align:middle}@-webkit-keyframes fadeinfadeout{0%{opacity:0}50%{opacity:1}to{opacity:0}}#cpFakeGeo{position:fixed;top:30px;left:20px;background-color:gray}#cpFakeGeoTitle{background-color:black;color:white;padding:10px;margin:5px}#cpFakeGeoList{width:95%;padding:2px;margin:4px}input[type="button"]{white-space:normal}.cpTextElement{min-width:100px;min-height:100px;color:white;font-size:25px;max-width:400px;text-align:center;vertical-align:middle;border:2px solid #fff;border-radius:15px;background-color:rgba(0,0,0,0.85);position:absolute;word-wrap:break-word;padding-bottom:2px;padding-left:2px;padding-right:2px;padding-top:2px;z-index:-1;left:0;top:0;-webkit-user-select:none;-moz-user-select:-moz-none;-ms-user-select:none}.cpAlertElement{min-width:100px;color:white;height:auto;min-height:30px;font-size:12px;text-align:center;vertical-align:middle;border-radius:5px;background-color:rgba(0,0,0,0.55);position:absolute;word-wrap:break-word;padding-bottom:2px;padding-left:2px;padding-right:2px;padding-top:2px;z-index:-1;left:0;top:0;-webkit-user-select:none;-moz-user-select:-moz-none;-ms-user-select:none;font-family:Arial,Helvetica,sans-serif}input[type=checkbox]:checked,input[type=checkbox]:disabled,input[type=checkbox],input[type=checkbox]:disabled:checked,input[type=radio]:checked,input[type=radio],input[type=radio]:disabled,input[type=radio]:disabled:checked{background:none;-webkit-appearance:none;border:none;-webkit-background-size:22px 22px}.spanRadioInput,.spanCheckBoxInput{position:fixed;display:block;width:22px;height:22px}.spanComboBox{background-color:#edeeef;border:1px;border-radius:5px;position:absolute;display:block}.spanArrow{background:url(../htmlimages/dd_arrow.png) no-repeat 0 0;float:right;display:inline-block;width:12px;height:12px;cursor:pointer}.dropIndicator{background:url(../htmlimages/arrow_right.gif) no-repeat 0 0;width:16px;height:16px;display:block;position:fixed}.sequenceReviewImage{background:url(../htmlimages/sequenceReview.png) no-repeat 0 0;width:17px;height:13px;display:block;position:fixed;cursor:pointer;border:0}.sequenceReviewArea{background-color:#fff098;display:block;position:fixed;border:solid 2px;font-size:10px;border-radius:5px;border-color:black}select option{background:#fff}select{border:1px;border-radius:5px;background:url(../htmlimages/dd_arrow.png) no-repeat 100% 4px #edeeef;outline:none;display:inline-block;-webkit-appearance:none;-moz-appearance:none;cursor:pointer}.questionOverlay{pointer-events:none;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;z-index:3;display:block;position:absolute;background-color:rgba(0,0,0,0.8);height:382px;width:719px;border-color:rgba(0,0,0,0.9);border-width:4px;border-radius:10px;box-shadow:rgba(255,255,255,0.5) 0 0 0 1px;border-style:solid;top:0;left:0}.questionSlide{height:382px;width:719px}.questionTextWrapper,.reviewModeQuestionTextWrapper{left:0;top:0;margin:12px 12px 16px;white-space:pre-line;font-size:19px;font-family:SourceSansProRegular;color:#fff;position:absolute;height:80px;overflow:hidden;width:478px;outline:currentcolor none medium;box-shadow:#fff 0 0 0}.answerArea{left:12px;width:480px;height:196px;top:80px;max-height:200px;overflow-x:hidden;overflow-y:hidden;position:absolute}.mcqanswercontainer{left:1px;width:478px;height:30px;margin-bottom:8px;border-radius:3px;border-width:1px;border-style:solid;outline:currentcolor none medium;border-color:rgba(0,0,0,0);background-color:transparent}.reviewModeMCQAnswerContainer{left:1px;width:478px;height:24px;margin-bottom:8px;border-radius:3px;border-width:1px;border-style:solid;outline:currentcolor none medium;border-color:rgba(0,0,0,0);background-color:transparent}.mcqicon{width:18px;height:18px;margin:4px 10px;background-size:cover;position:absolute;left:20px;pointer-events:all}.answerText{color:white;font-size:13px;font-family:SourceSansProRegular}.answeritem{width:440px;overflow:hidden;line-height:24px;height:24px;font-size:13px;font-family:SourceSansProRegular;color:#fff;float:right;position:absolute;left:55px}.resultSlide{height:382px;width:719px}.resultslidetable{margin-left:10%;margin-top:5%;background-color:transparent;border-color:transparent}.quizresulttablecell{background-color:transparent;border-color:transparent}.quizresulttitletext{margin-left:10%}.questionButton{color:white;background-color:#006eba;border-color:#006eba;overflow:hidden;float:left;height:26px;top:1px;margin-left:5px;width:110px;font-size:13px;font-family:SourceSansProBold;border-radius:13.85px;border-width:1px;border-style:solid;outline:currentcolor none medium;pointer-events:all}.buttonPanel{height:28px;padding:16px 12px 12px;width:80%;bottom:0;position:absolute;z-index:1000}.questionFeedback{position:static;display:block;margin-top:10px;width:97%;height:90%;background-color:transparent;border-radius:3px;text-align:center;resize:none;overflow:hidden}.feedbackPanel{height:296px;width:183px;background-color:transparent;border-color:rgba(0,0,0,0.2);border-width:1px;border-radius:3px;border-style:solid;font-size:13px;font-family:SourceSansProRegular;color:#fff;padding:8px;overflow:auto;top:12px;left:508px;position:absolute;box-shadow:#fff 0 0 0}.reviewIcon{width:20px;height:20px;float:left}.playButton
{
	background:url('../Playbar_icons/Play_icon.png') no-repeat;
	width:58px;
	height:59px;
	float:left;
	position:absolute;
	left:50px;
}
.playButton:hover
{
	background:url('../Playbar_icons/Play_icon.png') no-repeat;
	width:58px;
	height:59px;
	float:left;
	position:absolute;
	left:50px;
}
.playbarBackGround
{
	height:45px;
}
.playbarSmallButton
{
	width:51px;
	height:36px;
	float:left;
	display:inline;
	-moz-user-select: none; 
    -khtml-user-select: none; 
    -webkit-user-select: none; 
    -o-user-select: none; 
}
.playbarSmallButton:hover {cursor:pointer;} 
.playbarBigButton
{
	width:80px;
	height:36px;
	float:left;
	display:inline;
	-moz-user-select: none; 
    -khtml-user-select: none; 
    -webkit-user-select: none; 
    -o-user-select: none; 
}
.playbarBigButton:hover {cursor:pointer;} 
.playbarSlider
{
	width:60px;
	float:left;
	display:inline;
	-moz-user-select: none; 
    -khtml-user-select: none; 
    -webkit-user-select: none; 
    -o-user-select: none; 
}
.playbarSliderThumb
{
	position : absolute;
	top : 9px;
}
.playbarSliderThumb:hover {cursor:pointer;}