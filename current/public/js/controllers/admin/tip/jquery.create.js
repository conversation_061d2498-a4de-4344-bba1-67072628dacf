//When the page loads detect the value of the dropdown. If it is not theology then
//Do not show the book dropdown. Otherwise detect a change in the dropdown and show
//Book dropdown if category is equal to Theology. 
$(document).ready(function(){
    UpdateBookDropDown();
});
		
$('#cat_drop').change(function(){
    UpdateBookDropDown();
});

function UpdateBookDropDown()
{
    var text = $('#cat_drop option:selected').text();
    console.log(text);
    if(text === 'Theology'){
        console.log('show');
        $('#book_drop').show();
    }else{
        $('#book_drop').hide();
    }
}