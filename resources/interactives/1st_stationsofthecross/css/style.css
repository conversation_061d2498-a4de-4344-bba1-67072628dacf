/****************************/
/* General					*/
/****************************/


body, html {
	background-color: #6d5f46;
	/*overflow: hidden;*/
}

.content,.content:before, .content:after {
	background-color: #938261;
}

.content {
	position: relative;
	background: url("../img/background.png") 128px 166px no-repeat, url("../../general/img/bg_cloud.png") top center no-repeat;
	background-color: #938261;
	background-size: 80%, 100%;
}

h1 {
	color: #fff;
}

h2 {
	color: #333;
}

li:after {
	border-top: 3px solid white;
	width: 95px;
	content: "";
	position: absolute;
	margin-top: 10px;
	margin-left: 16px;
}

ul > li:last-child:after {
	border: none;
}

.hidden {
	display: none;
}

.clearfix:after {
	content: " ";
	display: block; 
	height: 0; 
	clear: both;
}

.heading {
	margin-top: -20px;
}

.heading p{
	margin-bottom: 30px;
	color: #fff;
}

/****************************/
/* Play area				*/
/****************************/

.maze {
	position: absolute;
	top: 237px;
	left: 0;
	right: 0;
	margin: 0 auto;
	/* background: #d1dbdb; */
	width: 416px;
	height: 416px;
}

/* paths */
.paths .start {
	position: absolute;
	top: 650px;
	left: 516px;
	width: 32px;
	height: 48px;
	border-radius: 0 0 20px 20px;
	background: #fff2ca;
}

.paths .b1 {
	position: absolute;
	top: 571px;
	left: 260px;
	width: 50px;
	height: 34px;
	border-radius: 0 0 0 0;
	background: #fff2ca;
}

.paths .b2 {
	position: absolute;
	top: 285px;
	left: 260px;
	width: 50px;
	height: 34px;
	border-radius: 0 0 0 0;
	background: #fff2ca;
}

.paths .b3 {
	position: absolute;
	top: 195px;
	left: 475px;
	width: 34px;
	height: 50px;
	border-radius: 0 0 0 0;
	background: #fff2ca;
}

.paths .b4 {
	position: absolute;
	top: 367px;
	left: 714px;
	width: 50px;
	height: 34px;
	border-radius: 0 0 0 0;
	background: #fff2ca;
}

.paths .b5 {
	position: absolute;
	top: 489px;
	left: 714px;
	width: 50px;
	height: 34px;
	border-radius: 0 0 0 0;
	background: #fff2ca;
}





/* places */
.places .p1 {
	position: absolute;
	top: 480px;
	left: 170px;
	width: 160px;
	height: 160px;
	background: url("../img/stories/places.png") no-repeat;
	background-position: -160px 0px;
}

.places .p1.active {
	background-position: 0px 0px;
}

.places .p2 {
	position: absolute;
	top: 190px;
	left: 180px;
	width: 160px;
	height: 160px;
	background: url("../img/stories/places.png") no-repeat;
	background-position: -480px 0;
}

.places .p2.active {
	background-position: -320px 0;
}

.places .p3 {
	position: absolute;
	top: 70px;
	left: 420px;
	width: 160px;
	height: 160px;
	background: url("../img/stories/places.png") no-repeat;
	background-position: -800px 0;
}

.places .p3.active {
	background-position: -640px 0;
}

.places .p4 {
	position: absolute;
	top: 270px;
	left: 700px;
	width: 160px;
	height: 160px;
	background: url("../img/stories/places.png") no-repeat;
	background-position: -1120px 0;
}

.places .p4.active {
	background-position: -960px 0;
}

.places .p5 {
	position: absolute;
	top: 440px;
	left: 740px;
	width: 160px;
	height: 160px;
	background: url("../img/stories/places.png") no-repeat;
	background-position: -1440px 0;
}

.places .p5.active {
	background-position: -1280px 0;
}




/* doors */
.doors .d1 {
	position: absolute;
	top: 567px;
	left: 515px;
	width: 43px;
	height: 43px;
	background: url("../img/stories/door.png") no-repeat;
	background-size: 43px 43px;
}

.doors .d2 {
	position: absolute;
	top: 403px;
	left: 635px;
	width: 43px;
	height: 43px;
	background: url("../img/stories/door.png") no-repeat;
	background-size: 43px 43px;
}

.doors .d3 {
	position: absolute;
	top: 444px;
	left: 469px;
	width: 43px;
	height: 43px;
	background: url("../img/stories/door.png") no-repeat;
	background-size: 43px 43px;
}

.doors .d4 {
	position: absolute;
	top: 241px;
	left: 390px;
	width: 43px;
	height: 43px;
	background: url("../img/stories/door.png") no-repeat;
	background-size: 43px 43px;
}

.doors .d5 {
	position: absolute;
	top: 525px;
	left: 349px;
	width: 43px;
	height: 43px;
	background: url("../img/stories/door.png") no-repeat;
	background-size: 43px 43px;
}

.wrapper {
	position: relative;
}

/****************************/
/* Character movement		*/
/****************************/

.character {
	position: absolute;
	top: 651px;
	left: 512px;
	width: 40px;
	height: 40px;
	background: url("../img/character_movement/character.png") no-repeat;
	background-position: 0 -40px;
	z-index: 5;
}

.character.up {
    background: url("../img/character_movement/character.png") no-repeat;
	background-position: 0 -40px;
    animation: play_up .2s steps(12) infinite;
}

.character.down {
    background: url("../img/character_movement/character.png") no-repeat;
	background-position: 0 0;
    animation: play_down .2s steps(12) infinite;
}

.character.left {
    background: url("../img/character_movement/character.png") no-repeat;
	background-position: 0 -80px;
    animation: play_left .2s steps(12) infinite;
}

.character.right {
    background: url("../img/character_movement/character.png") no-repeat;
	background-position: 0 -120px;
    animation: play_right .2s steps(12) infinite;
}

.character.stopped {
	animation: stop;
}

@keyframes play_up {
	100% { background-position: -480px -40px; }
}

@keyframes play_down {
	100% { background-position: -480px 0; }
}

@keyframes play_left {
	100% { background-position: -480px -80px; }
}

@keyframes play_right {
	100% { background-position: -480px -120px; }
}

/****************************/
/* Win, loose, start screen */
/****************************/

.question-screen,
.win-screen,
.loose-screen,
.start-screen {
	display: none;
	z-index: 5;
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	overflow: hidden;
	background: rgba(0,0,0,0.5);
}

.start-screen {
	display: block;
}

.question-screen .container,
.win-screen .container,
.loose-screen .container,
.start-screen .container {
	position: absolute;
	background: white;
	border-radius: 10px;
	width: 550px;
	height: 600px;
	top: 50%;
	margin-top: -320px;
	left: 50%;
	margin-left: -275px;
	padding: 30px 60px;
}

.question-screen .container {
	height: 250px;
}

.win-screen .container,
.loose-screen .container {
	padding-top: 60px;
}

.start-screen .container {
	height: 700px;
	margin-top: -325px;
}

.start-screen .container h3 {
	margin-bottom: 30px;
}

.start-screen .container .col1,
.start-screen .container .col2 {
	width: 50%;
	margin: 0 auto;
	float: left;
}

.start-screen .container .row1 .col1 img {
	margin-top: 20px;
	/*	border-radius: 50%;*/
}

.start-screen .container .row2 img {
	height: 120px;
	/*	border-radius: 50%;*/
}

.start-screen .container .row2 .col1 {

	border-right: 2px solid #d5dae0;

}

.start-screen .container .col2 .up,
.start-screen .container .col2 .left,
.start-screen .container .col2 .down,
.start-screen .container .col2 .right {
	position: relative;
	width: 55px;
	height: 55px;
	background: #e0d5d1;
	margin: 0 auto;
	padding: 0;
	border-radius: 5px;
	padding-top: 15px;
}

.start-screen .container .col2 .up {
	margin-top: 10px;
}

.start-screen .container .col2 .down,
.start-screen .container .col2 .left,
.start-screen .container .col2 .right {
	float: left;
	margin-top: 6px;
	margin-right: 6px;
}

.start-screen .container .col2 .left {
	margin-left: 19px;
}

.win-screen .container h2,
.loose-screen .container h2 {
	padding: 50px 0 40px 0;
	font-size: 42px;
}

.win-screen .container a,
.loose-screen .container a,
.start-screen .container a {
	width: 100%;
}

.container .win,
.container .loose {
	margin: 0 auto;
	border-radius: 50%;
	width: 180px;
	height: 180px;
	margin-top: 30px;
	margin-bottom: 30px;
}

.container .win {
	background: #79cee8;
}

.container .loose {
	background: #ce4e6d;
}

.start-screen .container .win,
.start-screen .container .loose {
	width: 120px;
	height: 120px;
}

.start-screen .container .win {
	background: #f9b46e;
}

.start-screen .container .divider {
	height: 2px;
	background: #d5dae0;
	margin: 40px 0;
}

.start-screen .container .win img {
	margin-top: 25px;
}

.start-screen .container .loose img {
	margin-top: 35px;
}

.win-screen .container .win img {
	margin-top: 50px;
}

.loose-screen .container .loose img {
	margin-top: 65px;	
}




/****************************/
/* Buttons					*/
/****************************/

.container .big-btn {
	background: #ac88b5;
	border-bottom: solid 3px #816289;
	/*	margin-top: 40px;*/
}

.container .big-btn.start {
	margin-top: 30px;
}

.container .big-btn:hover {
	border-bottom: solid 3px #9a75a3;
}

.cancelBtnHolder {
	position: absolute;
	top: 0;
	right: 20px;
	margin: auto;
	z-index: 5;
}

.cancelBtnHolder .big-btn {
	background-image: url('../../general/img/close.png');
	border-color: #fff;
}
