// French
plupload.addI18n({
    'Select files' : 'Sélectionnez les fichiers',
    'Add files to the upload queue and click the start button.' : 'Ajoutez des fichiers à la file et appuyez sur le bouton démarrer.',
    'Filename' : 'Nom de fichier',
    'Status' : 'Status',
    'Size' : 'Taille',
    'Add files' : 'Ajouter Fichiers',
    'Stop current upload' : 'Arrêter l\'envoi en cours',
    'Start uploading queue' : 'Démarrer l\'envoi',
    'Uploaded %d/%d files': '%d/%d fichiers envoyés',
    'N/A' : 'Non applicable',
    'Drag files here.' : 'Déposer les fichiers ici.',
    'File extension error.': 'Erreur extension fichier',
    'File size error.': 'Erreur taille fichier.',
    'Init error.': 'Erreur d\'initialisation.',
    'HTTP Error.': 'Erreur HTTP.',
    'Security error.': 'Erreur de sécurité.',
    'Generic error.': 'Erreur générique.',
    'IO error.': 'Erreur E/S.',
    'Stop Upload': 'Arrêter les envois.',
    'Add Files': 'Ajouter des fichiers',
    'Start Upload': 'Démarrer les envois.',
    '%d files queued': '%d fichiers en attente.'
});