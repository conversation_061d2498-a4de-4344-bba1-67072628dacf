Array.prototype.clone = function() {
    return this.slice(0);
};

$(function() {
    var scenarios = [
        [
            "img/stories/ir_choice_choices_01.png",
            "img/stories/ir_choice_choices_01a.png",
            "img/stories/ir_choice_choices_01b.png",
        ],[
            "img/stories/ir_choice_choices_02.png",
            "img/stories/ir_choice_choices_02a.png",
            "img/stories/ir_choice_choices_02b.png",
        ],[
            "img/stories/ir_choice_choices_03.png",
            "img/stories/ir_choice_choices_03a.png",
            "img/stories/ir_choice_choices_03b.png",
        ],[
            "img/stories/ir_choice_choices_04.png",
            "img/stories/ir_choice_choices_04a.png",
            "img/stories/ir_choice_choices_04b.png"
        ]
    ];

    var goodImages = [
        "img/stories/ir_choice_choices_01b.png",
        "img/stories/ir_choice_choices_02a.png",
        "img/stories/ir_choice_choices_03b.png",
        "img/stories/ir_choice_choices_04b.png"
    ];

    var audiotypes={
        "mp3": "audio/mpeg",
        "mp4": "audio/mp4",
        "ogg": "audio/ogg",
        "wav": "audio/wav"
    };

    var correctSelections = 0;

    function ss_soundbits(sound, loop){
        sound = '/interactives/dragndrop/sounds/' + sound;

        var audio_element = document.createElement('audio');
        if (audio_element.canPlayType){
            for (var i=0; i == 0 && i<arguments.length; i++){
                for(var key in audiotypes) {
                    var source_element = document.createElement('source');
                    source_element.setAttribute('src', arguments[i] + "." + key);
                    source_element.setAttribute('type', audiotypes[key]);
                    audio_element.appendChild(source_element);
                }
            }

            //audio_element.play()
            audio_element.playclip=function(){
                this.pause();
            //this.currentTime=0
            this.play();
            };

            if(loop) {
                audio_element.addEventListener('ended', function() {
                    ss_soundbits(sound.replace('/interactives/dragndrop/sounds/', ''), loop).playclip();
                }, false);
            }

            return audio_element;
        }
    }

    function loadImages(index) {
        var images = scenarios[index];

        $('.main').attr('src', images[0]);
        $('.choice1').attr('src', images[1]);
        $('.choice2').attr('src', images[2]);

        $('.choice').click(function () {
            $('.choice').removeClass('selected');
            $(this).addClass('selected');
            ss_soundbits('dragndrop').playclip();
        });

        $('.scenario-count ul li').each(function (i, item) {
            if (index === i) {
                $(this).addClass('active');
            }

            if (index > i) {
                $(this).addClass('selected');
                $(this).removeClass('active');
            }
        });
    }

    function arraysEqual(arr1, arr2) {
        if(arr1.length !== arr2.length)
            return false;
        for(var i = arr1.length; i--;) {
            if(arr1[i] !== arr2[i])
                return false;
        }

        return true;
    }

    var soundOnePlayed = false;

    $(document).ready(function () {
        var count = 0;
        loadImages(count);
        var selected = [];

        $('#next').click(function() {
        
            if ($('.choice.selected').length) {
                var src = $('.choice.selected').attr('src');

                if (goodImages.indexOf(src) > -1) {
                    ss_soundbits('win').playclip();
                    $('.choice').removeClass('selected');
                    count++;

                    if (count === 4) {
                        $('.scenario').css('display', 'none');
                        $('.summary').css('display', 'block');
                        $('#next').css('display', 'none');
                        $('#replay').css('display', 'inherit');
                        $('.scenario-count').css('display', 'none');

                        /*$(".good img").each(function (i, item) {
                            var src = $(this).attr('src');
                            if (src === selected[i]) {
                                $(this).addClass('right');
                            }
                        });

                        $(".bad img").each(function (i, item) {
                            var src = $(this).attr('src');
                            if ($(this).attr("src") === selected[i]) {
                                $(this).addClass('wrong');
                            }
                        });*/
                    } else {
                        loadImages(count);
                    }

                } else {
                    ss_soundbits('negative').playclip();
                }               
            }
        });

        $('#replay').click(function (e) {
            window.location.reload();
            e.preventDefault();
            e.stopPropagation();
        });
    });

});