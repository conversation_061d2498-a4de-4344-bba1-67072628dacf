// Republic of Korea
plupload.addI18n({
   'Select files' : '파일 선택',
   'Add files to the upload queue and click the start button.' : '파일을 업로드 큐에 추가하여 시작 버튼을 클릭하십시오.',
   'Filename' : '파일 이름',
   'Status' : '상태',
   'Size' : '크기',
   'Add Files' : '파일 추가',
   'Stop Upload': '업로드 중지',
   'Start Upload': '업로드',
   'Add files': '파일 추가',
   'Stop current upload': '현재 업로드를 정지',
   'Start uploading queue': '업로드',
   'Stop upload': '업로드 중지',
   'Start upload': '업로드',
   'Uploaded % d / % d files': '업로드 중 % d / % d 파일',
   'N / A': 'N / A',
   'Drag files here': '여기에 파일을 드래그',
   'File extension error': '파일 확장자 오류',
   'File size error': '파일 크기 오류',
   'File count error': '이미지 : 오류',
   'Init error': '초기화 오류',
   'HTTP Error': 'HTTP 오류',
   'Security error': '보안 오류',
   'Generic error': '오류',
   'IO error': 'IO 오류',
   'File : % s': '파일 % s',
   'Close': '닫기',
   '% d files queued': '% d 파일이 추가되었습니다',
   'Using runtime :': '모드',
   'File : % f, size : % s, max file size : % m': '파일 : % f, 크기 : % s, 최대 파일 크기 : % m',
   'Upload element accepts only % d file (s) at a time. Extra files were stripped': '업로드 가능한 파일의 수는 % d입니다. 불필요한 파일은 삭제되었습니다 ',
   'Upload URL might be wrong or doesn \'t exist ':'업로드할 URL이 존재하지 않습니다 ',
   'Error : File too large :': '오류 : 크기가 너무 큽니다',
   'Error : Invalid file extension :': '오류 : 확장자가 허용되지 않습니다 :'
});