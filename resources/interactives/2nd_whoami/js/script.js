Array.prototype.clone = function() {
	return this.slice(0);
};

$(function() {
    var pictureSequence = [
        "img/stories/whoami_1.png",
        "img/stories/whoami_2.png",
        "img/stories/whoami_3.png",
        "img/stories/whoami_4.png",
        "img/stories/whoami_5.png",
    ];

    var audioSequence = [
        "audio1",
        "audio2",
        "audio3",
        "audio4",
        "audio5",
    ];
        
        
    var audiotypes={
        "mp3": "audio/mpeg",
        "mp4": "audio/mp4",
        "ogg": "audio/ogg",
        "wav": "audio/wav"
    };

    function ss_soundbits(sound, loop){
        sound = 'sounds/' + sound;
        
        var audio_element = document.createElement('audio');
        if (audio_element.canPlayType){
            for (var i=0; i === 0 && i<arguments.length; i++){
                for(var key in audiotypes) {
                    var source_element = document.createElement('source');
                    source_element.setAttribute('src', arguments[i] + "." + key);
                    source_element.setAttribute('type', audiotypes[key]);
                    audio_element.appendChild(source_element);
                }
            }
            
            //audio_element.play()
            audio_element.playclip=function(){
                this.pause();
                //this.currentTime=0
                this.play();
            };
            
            if(loop) {
                audio_element.addEventListener('ended', function() {
                    ss_soundbits(sound.replace('sounds/', ''), loop).playclip();
                }, false);
            }
            
            return audio_element;
        }
    }

    function shuffleAudio() {
        var o = audioSequence.clone();
        
        for(var j, x, i = o.length; i; j = Math.floor(Math.random() * i), x = o[--i], o[i] = o[j], o[j] = x);
    
        $(".top-stories .story").each(function (i, item) {
            var index = audioSequence.indexOf(o[i]);
            $(this).attr('audio', index);
            $(this).on('click', function() {
                var audioId = '';
                var imageId = '';
                //is matched already
                if($(this).hasClass('matched')) {
                    audioId = $(this).attr('audio');

                    $.each(matches, function(a){
                        if(matches[a].audio === audioId) {
                            imageId = matches[a].image;
                            matches.splice(a,1);
                            return false;
                        }
                    });


                    $(this).removeClass('matched');
                    $(".bottom-stories .story[image='" + imageId + "']").removeClass('matched');
                    $(".story").removeClass('active');
                    $(this).addClass('active');
                //is active
                } else if($(this).hasClass('active')){
                   $(this).removeClass('active');
                //is not matched
                } else {
                    //set active
                    ss_soundbits(o[i]).playclip();
                    $(".top-stories .story").removeClass('active');
                    $(this).addClass('active');

                    //if bottom is selected also
                    if($(".bottom-stories .active")[0]){
                        var bottomStory = $(".bottom-stories .active");
                        var topStory = $(this);

                        //create a match
                        audioId = topStory.attr('audio');
                        imageId =  bottomStory.attr('image');

                        if(audioId === imageId) {
                            //create a match

                            //animate move
                            moveElement(topStory);
                            moveElement(bottomStory);

                            if(topStory.hasClass('purple')) {
                                bottomStory.addClass('purple');
                            } else if(topStory.hasClass('pink')) {
                                bottomStory.addClass('pink');
                            } else if(topStory.hasClass('blue')) {
                                bottomStory.addClass('blue');
                            } else if(topStory.hasClass('darkblue')) {
                                bottomStory.addClass('darkblue');
                            } else if(topStory.hasClass('orange')) {
                                bottomStory.addClass('orange');
                            }

                            //create a match
                            bottomStory.removeClass('active');
                            bottomStory.addClass('matched');

                            setTimeout(function () {
                                topStory.addClass('matched');
                                topStory.removeClass('active');
                            }, 500);

                            ss_soundbits('win').playclip();

                        } else {
                            ss_soundbits('negative').playclip();
                        }

                    }
                }
            });
        });
    }



    function shuffleImages() {
        var o = pictureSequence.clone();
        
        for(var j, x, i = o.length; i; j = Math.floor(Math.random() * i), x = o[--i], o[i] = o[j], o[j] = x);
    
        $(".bottom-stories .story").each(function (i, item) {
            $(this).on('click', function() {
                var imageId = '';
                var audioId = '';

                //is matched
                //unmatch it
                if($(this).hasClass('matched')) {
                    imageId = $(this).attr('image');
                    $.each(matches, function(a){
                        if(matches[a].image === imageId) {
                            audioId = matches[a].audio;
                            matches.splice(a,1);
                            return false;
                        }
                    });


                    $(this).removeClass('matched');
                    $(".top-stories .story[audio='" + audioId + "']").removeClass('matched');
                    $(".story").removeClass('active');
                    $(this).addClass('active');

                    if($(this).hasClass('purple')) {
                        $(this).removeClass('purple');
                    } else if($(this).hasClass('pink')) {
                        $(this).removeClass('pink');
                    } else if($(this).hasClass('blue')) {
                        $(this).removeClass('blue');
                    } else if($(this).hasClass('darkblue')) {
                        $(this).removeClass('darkblue');
                    } else if($(this).hasClass('orange')) {
                        $(this).removeClass('orange');
                    }

                //is active
                //make inactive
                } else if($(this).hasClass('active')){
                   $(this).removeClass('active');
                //is not active or matched
                } else {
                    //set as active
                    $(".bottom-stories .story").removeClass('active');
                    $(this).addClass('active');

                    //if top is also selected
                    if($(".top-stories .active")[0]){
                        var topStory = $(".top-stories .active");
                        var bottomStory = $(this);

                        //create a match
                        imageId = $(this).attr('image');
                        audioId =  topStory.attr('audio');

                        if(audioId === imageId) {
                            //create a match

                            //animate move
                            moveElement(bottomStory);
                            moveElement(topStory);

                            if(topStory.hasClass('purple')) {
                                bottomStory.addClass('purple');
                            } else if(topStory.hasClass('pink')) {
                                bottomStory.addClass('pink');
                            } else if(topStory.hasClass('blue')) {
                                bottomStory.addClass('blue');
                            } else if(topStory.hasClass('darkblue')) {
                                bottomStory.addClass('darkblue');
                            } else if(topStory.hasClass('orange')) {
                                bottomStory.addClass('orange');
                            }

                            bottomStory.removeClass('active');
                            bottomStory.addClass('matched');
                            
                            setTimeout(function() {
                                topStory.addClass('matched');
                                topStory.removeClass('active');
                            }, 500);

                            ss_soundbits('win').playclip();

                        } else {
                            ss_soundbits('negative').playclip();
                        }
                    }
                }
                
            });
            $(this).find("img").first().attr("src", o[i]).attr('original-image', o[i]);
            var index = pictureSequence.indexOf(o[i]);
            $(this).attr('image', index);
        });
    }

    function moveElement(el) {
        // all the LIs above the el
        var previousAll = el.prevAll();

        // only proceed if it's not already on top (no previous siblings)
        if(previousAll.length > 0) {
            // top LI
            var left = $(previousAll[previousAll.length - 1]);

            // immediately previous LI
            var previous = $(previousAll[0]);

            // how far up do we need to move the el
            var moveUp = el.attr('offsetLeft') - left.attr('offsetLeft');

            // how far down do we need to move the previous siblings?
            var moveDown = (el.offset().left + el.outerWidth()) - (previous.offset().left + previous.outerWidth());

            // let's move stuff
            el.css('position', 'relative');
            previousAll.css('position', 'relative');
            el.animate({'left': -moveUp});
            previousAll.animate({'left': moveDown}, {
                complete: function() {
                  // rearrange the DOM and restore positioning when we're done moving
                  el.parent().prepend(el);
                  el.css({'position': 'relative', 'left': 0});
                  previousAll.css({'position': 'relative', 'left': 0}); 
                }
            });
        }
    }
    

    $(document).ready(function () {

        shuffleImages();
        shuffleAudio();
        
    });
});