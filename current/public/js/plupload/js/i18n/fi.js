// .fi file like language pack
plupload.addI18n({
	'Select files' : '<PERSON><PERSON><PERSON> tiedostoja',
	'Add files to the upload queue and click the start button.' : 'Lisää tiedostoja latausjonoon ja klikkaa aloita-nappia.',
	'Filename' : '<PERSON><PERSON><PERSON><PERSON><PERSON>',
	'Status' : 'Tila',
	'Size' : 'Koko',
	'Add files' : 'Lisää tiedostoja',
	'Stop current upload' : 'Pysäytä nykyinen lataus',
	'Start uploading queue' : 'Aloita jonon lataus',
	'Drag files here.' : 'Raahaa tiedostot tänne.',
	'Start upload' : 'Aloita lataus',
	'Uploaded %d/%d files': 'Ladattu %d/%d tiedostoa',
	'Stop upload': 'Pysäytä lataus',
	'Start upload': 'Aloita lataus',
	'%d files queued': '%d tiedostoa jonossa',
	'File: %s': 'Tiedosto: %s',
	'Close': 'Sulje',
	'Using runtime: ': 'Käytetään ajonaikaista: ',
	'File: %f, size: %s, max file size: %m': 'Tiedosto: %f, koko: %s, maksimi tiedostokoko: %m',
	'Upload element accepts only %d file(s) at a time. Extra files were stripped.': 'Latauselementti sallii ladata vain %d tiedosto(a) kerrallaan. Ylimääräiset tiedostot ohitettiin.',
	'Upload URL might be wrong or doesn\'t exist': 'Lataus URL saattaa olla väärin tai ei ole olemassa',
	'Error: File too large: ': 'Virhe: Tiedosto liian suuri: ',
	'Error: Invalid file extension: ': 'Virhe: Kelpaamaton tiedostopääte: ',
	'File extension error.': 'Tiedostopäätevirhe.',
	'File size error.': 'Tiedostokokovirhe.',
	'File count error.': 'Tiedostolaskentavirhe.',
	'Init error.': 'Init virhe.',
	'HTTP Error.': 'HTTP virhe.',
	'Security error.': 'Tietoturvavirhe.',
	'Generic error.': 'Yleinen virhe.',
	'IO error.': 'I/O virhe.'
});