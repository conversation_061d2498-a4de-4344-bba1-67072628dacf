// Persian
plupload.addI18n({
   'Select files' : 'انتخاب فایل',
   'Add files to the upload queue and click the start button.' : 'اضافه کنید فایل ها را به صف آپلود و دکمه شروع را کلیک کنید.',
   'Filename' : 'نام فایل',
   'Status' : 'وضعیت',
   'Size' : 'سایز',
   'Add Files' : 'افزودن فایل',
   'Stop Upload' : 'توقف انتقال',
   'Start Upload' : 'شروع انتقال',
   'Add files' : 'افزودن فایل',
   'Add files.' : 'افزودن فایل',
   'Stop current upload' : 'توقف انتقال جاری',
   'Start uploading queue' : 'شروع صف انتقال',
   'Stop upload' : 'توقف  انتقال',
   'Start upload' : 'شروع انتقال',
   'Uploaded %d/%d files': 'منتقل شد %d/%d از فایلها',
   'N/A' : 'N/A',
   'Drag files here.' : 'بکشید فایل ها رو به اینجا',
   'File extension error.': 'خطا پیشوند فایل',
   'File size error.': 'خطای سایز فایل',
   'File count error.': 'خطای تعداد فایل',
   'Init error.': 'خطا در استارت اسکریپت',
   'HTTP Error.': 'HTTP خطای',
   'Security error.': 'خطای امنیتی',
   'Generic error.': 'خطای عمومی',
   'IO error.': 'IO خطای',
   'File: %s': ' فایل ها : %s',
   'Close': 'بستن',
   '%d files queued': '%d فایل در صف',
   'Using runtime: ': 'استفاده میکنید از : ',
   'File: %f, size: %s, max file size: %m': فایل: %f, سایز: %s, بزرگترین سایز فایل: %m',
   'Upload element accepts only %d file(s) at a time. Extra files were stripped.': 'عنصر بارگذار فقط %d فایل رو در یک زمان  می پذیرد. سایر فایل ها مجرد از این موضوع هستند.',
   'Upload URL might be wrong or doesn\'t exist': 'آدرس آپلود اشتباه می باشد یا وجود ندارد',
   'Error: File too large: ': 'خطا: فایل حجیم است :: ',
   'Error: Invalid file extension: ': 'خطا پسوند فایل معتبر نمی باشد : '
});
