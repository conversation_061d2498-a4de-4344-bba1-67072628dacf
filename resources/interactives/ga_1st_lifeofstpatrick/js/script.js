Array.prototype.clone = function() {
	return this.slice(0);
};


$(function() {

	var game = {

		moveStep: 1.5, // px
		isPaused: true,
		score: 0,
		soundOnePlayed: false,

		items: [
			"img/item1.png",	
			"img/item2.png",	
			"img/item3.png",	
			"img/item4.png",	
		],

		questions: [
			{ 
				question: "Thóg fog<PERSON>aithe Pádraig ón a bhaile. ",
				answer: true
			},
			{ 
				question: "<PERSON>uair a tháinig sé go hÉirinn, d'oibrigh <PERSON> ma<PERSON> chócaire.",
				answer: false
			},
			{ 
				question: "<PERSON><PERSON> a lán ama ag caint le Dia.",
				answer: true
			},
			{ 
				question: "<PERSON>’éalaigh <PERSON> ar ais go dtí a thír féin.",
				answer: true
			},
			{ 
				question: "Níor tháinig Pádraig ar ais go hÉirinn riamh.",
				answer: false
			},
		],

		audiotypes: {
			"mp3": "audio/mpeg",
			"mp4": "audio/mp4",
			"ogg": "audio/ogg",
			"wav": "audio/wav"
		}

	};

	function ss_soundbits(sound, loop) {
		sound = '/interactives/dragndrop/sounds/' + sound;

		var audio_element = document.createElement('audio')
		if (audio_element.canPlayType){
			for (var i=0; i == 0 && i<arguments.length; i++){
				for(var key in game.audiotypes) {
					var source_element = document.createElement('source')
					source_element.setAttribute('src', arguments[i] + "." + key)
					source_element.setAttribute('type', game.audiotypes[key])
					audio_element.appendChild(source_element);
				}
			}

			//audio_element.play()
			audio_element.playclip=function(){
				this.pause()
				//this.currentTime=0
				this.play()
			}

			if(loop) {
				audio_element.addEventListener('ended', function() {
					ss_soundbits(sound.replace('/interactives/dragndrop/sounds/', ''), loop).playclip();
				}, false);
			}

			return audio_element
		}
	}

	function init() {

		game.lane4 = $('.lane4 .lane');
		game.lane3 = $('.lane3 .lane');
		game.lane2 = $('.lane2 .lane');
		game.lane1 = $('.lane1 .lane');

		game.character = $('.character');

		game.item3 = $('.lane3 .item');
		game.item2 = $('.lane2 .item');
		game.item1 = $('.lane1 .item');

		game.hole3 = $('.lane3 .hole');
		game.hole2 = $('.lane2 .hole');
		game.hole1 = $('.lane1 .hole');

		game.scoreBoard = $('.score-board img');

		game.question = $('.question');

		randomizeItems();

		startGame();

		startAnimations();

	}

	function randomizeItems(item) {


		var d = [];

		for(var i=0; i<3; i++) {
			d[i] = Math.floor((Math.random()) * 200);
		}

		game.item1.css({left:game.item1.position().left+game.hole1.width()+d[0]});
		game.item2.css({left:game.item2.position().left+game.hole2.width()+d[1]});
		game.item3.css({left:game.item3.position().left+game.hole3.width()+d[2]});


	}

	function startGame() {

		game.isPaused = false;
		game.character.removeClass("stopped");
	}

	function pauseGame() {

		game.isPaused = true;
		game.character.addClass("stopped");

	}

	// score
	function addScore() {

		game.score++;

		ss_soundbits('win2').playclip();

		if(game.score == 1) {
			game.scoreBoard.eq(0).removeClass("hidden");
		} else if(game.score == 2) {
			game.scoreBoard.eq(1).removeClass("hidden");
		} else if(game.score == 3) {
			game.scoreBoard.eq(2).removeClass("hidden");
			pauseGame();
			showWinScreen();
		}

	}

	// lanes

	function startAnimations() {

		game.lanes = setInterval(function () {

			if(!game.isPaused) {

				var hole1Min = game.hole1.position().left;
				var hole1Max = hole1Min + game.hole1.width();

				var hole2Min = game.hole2.position().left;
				var hole2Max = hole2Min + game.hole2.width();

				var hole3Min = game.hole3.position().left;
				var hole3Max = hole3Min + game.hole3.width();

				var item1Min = game.item1.position().left;
				var item1Max = item1Min + game.item1.width();

				var item2Min = game.item2.position().left;
				var item2Max = item2Min + game.item2.width();

				var item3Min = game.item3.position().left;
				var item3Max = item3Min + game.item3.width();

				var item1Restart = false;
				var item2Restart = false;
				var item3Restart = false;

				var hole1Restart = false;
				var hole2Restart = false;
				var hole3Restart = false;

				game.lane4.css({left: "-="+game.moveStep});
				game.lane3.css({left: "-="+(game.moveStep*1.5)});
				game.lane2.css({left: "-="+(game.moveStep*2.5)});
				game.lane1.css({left: "-="+(game.moveStep*3.5)});

				if(parseInt(game.lane4.css("left")) < game.lane4.width()/2*-1) {
					game.lane4.css({left:0});
				}

				if(parseInt(game.lane3.css("left")) < game.lane3.width()/2*-1) {
					game.lane3.css({left:0});
				}

				if(parseInt(game.lane2.css("left")) < game.lane2.width()/2*-1) {
					game.lane2.css({left:0});
				}

				if(parseInt(game.lane1.css("left")) < game.lane1.width()/2*-1) {
					game.lane1.css({left:0});
				}


				// item3
				game.item3.css({left: "-="+(game.moveStep*1.5)});
				// check if scored
				if(game.item3.position().left < 100 && game.item3.position().left > 0) {
					if(game.character.data("lane") == 3) {
						addScore();
						item3Restart = true;
					}
				}
				// restart
				if(game.item3.position().left < game.item3.width()*-1 || item3Restart === true) {
					game.item3.css({left: game.lane3.width()/2+game.item3.width()});
				}
				// check for collision
				if((game.item3.position().left + game.item3.width()) >= hole3Min && game.item3.position().left <= hole3Max) {
					game.item3.css({left: game.item3.position().left + game.hole3.width()});
				}


				// item2
				game.item2.css({left: "-="+(game.moveStep*2.5)});
				// check if scored
				if(game.item2.position().left < 100 && game.item2.position().left > 0) {
					if(game.character.data("lane") == 2) {
						addScore();
						item2Restart = true;
					}
				}
				// restart
				if(game.item2.position().left < game.item2.width()*-1 || item2Restart === true) {
					game.item2.css({left: game.lane2.width()/2+game.item2.width()});
				}
				// check for collision
				if((game.item2.position().left + game.item2.width()) >= hole2Min && game.item2.position().left <= hole2Max) {
					game.item2.css({left: game.item2.position().left + game.hole2.width()});
				}

				// item1
				game.item1.css({left: "-="+(game.moveStep*3.5)});

				// check if scored
				if(game.item1.position().left < 100 && game.item1.position().left > 0) {
					if(game.character.data("lane") == 1) {
						addScore();
						item1Restart = true;
					}
				}
				// restart
				if(game.item1.position().left < game.item1.width()*-1 || item1Restart === true) {
					game.item1.css({left: game.lane1.width()/2+game.item1.width()});
				}
				// check for collision
				if((game.item1.position().left + game.item1.width()) >= hole1Min && game.item1.position().left <= hole1Max) {
					game.item1.css({left: game.item1.position().left + game.hole1.width()});
				}


				// hole3
				game.hole3.css({left: "-="+(game.moveStep*1.5)});
				// check if scored
				if(game.hole3.position().left < 100 && game.hole3.position().left > 0) {
					if(game.character.data("lane") == 3) {
						showQuestion();
						hole3Restart = true;
					}
				}
				// restart
				if(game.hole3.position().left < game.hole3.width()*-1 || hole3Restart === true) {
					game.hole3.css({left: game.lane3.width()/2+game.hole3.width()});
				}


				// hole2
				game.hole2.css({left: "-="+(game.moveStep*2.5)});

				// check if scored
				if(game.hole2.position().left < 100 && game.hole2.position().left > 0) {
					if(game.character.data("lane") == 2) {
						showQuestion();
						hole2Restart = true;
					}
				}
				// restart
				if(game.hole2.position().left < game.hole2.width()*-1 || hole2Restart === true) {
					game.hole2.css({left: game.lane2.width()/2+game.hole2.width()});
				}

				// hole1
				game.hole1.css({left: "-="+(game.moveStep*3.5)});

				// check if scored
				if(game.hole1.position().left < 100 && game.hole1.position().left > 0) {
					if(game.character.data("lane") == 1) {
						showQuestion();
						hole1Restart = true;
					}
				}
				// restart
				if(game.hole1.position().left < game.hole1.width()*-1 || hole1Restart === true) {
					game.hole1.css({left: game.lane1.width()/2+game.hole1.width()});
				}


			}

		}, 20);

	}

	// Character
	function startCharacter() {
		game.character.removeClass('stopped');
	}

	function stopCharacter() {
		game.character.addClass('stopped');
	}

	// Miscellaneous
	function showQuestion() {

		if(game.questions.length > 0) {

			pauseGame();

			ss_soundbits('negative').playclip();

			$('.question-screen').show();

			var q = Math.floor(Math.random() * game.questions.length);
			var a;

			game.question.text(game.questions[q].question);
			a = game.questions[q].answer;

			$('.question-screen input[name=answer]').val(a);

			game.questions.splice(q, 1);


		} else {

			showLooseScreen();

		}

	}

	function addMovementListener() {

		$('body').on("keyup", function(e) {
			if((e.which && e.which == 38) || (e.keyCode && e.keyCode == 38) && !game.isPaused) { // arrow down
				if(game.character.position().top > 180) {
					game.character.animate({top:"-=170"}, 50, "linear", function () {
						setCharacterLane();
					});
				}
			}
			if((e.which && e.which == 40) || (e.keyCode && e.keyCode == 40) && !game.isPaused) { // arrow up
				if(game.character.position().top < 520) {
					game.character.animate({top:"+=170"}, 50, "linear", function () {
						setCharacterLane();
					});
				}
			}

		});

	}

	function setCharacterLane() {

		if(Math.round(game.character.position().top) == 180) {
			game.character.data("lane", 3)
		}
		if(Math.round(game.character.position().top) == 350) {
			game.character.data("lane", 2)
		}
		if(Math.round(game.character.position().top) == 520) {
			game.character.data("lane", 1)
		}

	}

	function showLooseScreen() {

		ss_soundbits('negative').playclip();
		$(".loose-screen").show();

	}

	function showWinScreen() {

		ss_soundbits('win').playclip();
		$(".win-screen").show();

	}

	$(document).ready(function () {

		$('.replay').click(function (e) {
			window.location.reload();
			e.preventDefault();
			e.stopPropagation();
		});

		$('.question-screen .answer').click(function () {

			var answer = $(this).hasClass("true") ? "true" : "false";
			var correctAnswer = $(this).parent().find("input[name=answer]").val();

			if(answer == correctAnswer) {
				$('.question-screen').hide();
				startGame();
			} else {
				pauseGame();
				showLooseScreen();
			}

		});

		$('.start').click(function (e) {

			e.preventDefault();
			e.stopPropagation();

			init();

			addMovementListener();
			$('.start-screen').hide();
		});

		$(".score-board").click(function () {

			if(game.isPaused === true) {
				startGame();
			} else {
				pauseGame();
			}
		})

	});

});
