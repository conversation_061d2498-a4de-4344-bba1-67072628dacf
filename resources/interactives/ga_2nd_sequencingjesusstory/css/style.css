body,html {
	background-color: #367aa4;
}

.content.step2 {
	background: url("../img/stories/map2.png") no-repeat center center;
}
.content.step2:after {
	background: url("../img/stories/map1.png") no-repeat center bottom;
}
.content.step2:before {
	background: url("../img/stories/map3.png") no-repeat center top;
}

.content.step1 {
	background-color: #79cee8;
	background-image: url(../img/bg_cloud.png);
}

.content.step1:after,
.content.step1:before {
	background-color: #79cee8;
}

.steps {
	display: none;
}

.scenario-count {
	position: absolute;
	top: 45px;
	left: 0;
	right: 0;
	margin: 0 auto;
}

.scenario-count ul {
    margin-bottom: 40px;
}

.scenario-count ul li {
  list-style: none;
  color: white;
  font-size: 14px;
  display: inline;
  border: 3px solid white;
  border-radius: 50%;
  padding: 6px 10px;
  font-weight: bold;
  margin: 0 50px;
}

.scenario-count ul li.selected {
    background-color: white;
    color: black;
}

.scenario-count ul li.active {
    background-color: white;
    color: black;
    padding: 16px 21px;
    margin: 0 40px;
}

.scenario-count li:after {
  border-top: 3px solid white;
  width: 95px;
  content: "";
  position: absolute;
  margin-top: 10px;
  margin-left: 16px;
}

.scenario-count ul > li:last-child:after {
    border: none;
}

.heading {
	margin-top: -30px;
}

h1,
.heading p {
	color: #fff;
}

/* step 1 */
.step1:not(.content) {
	margin-top: 160px;
}

.step1 .top-stories,
.step1 .top-stories-backgrounds{
    float: left;
    width: 100%;
    height: 163px;
}

.step1 .top-stories-backgrounds{
    padding: 0px 30px;
}

.step1 .top-stories .story,
.step1 .top-stories-backgrounds .story,
.step1 .bottom-stories .story{
    width: 166px;
    float: left;
    margin-left:12.5px;
    margin-right:12.5px;
    text-align: center;
    padding-left: 12.5px;
    padding-right: 12.5px;
    padding-top: 25px;
    padding-bottom: 25px;
    background-color: #ffffff;
    -moz-border-radius: 4px;
    -webkit-border-radius: 4px;
    border-radius: 4px;
    height: 163px;
}
.step1 .top-stories-backgrounds {
    position: absolute;
	top: 160px;
    left: 0;
}

.step1 .story:not(.ui-draggable-dragging){
    left: 0px!important;
}

.step1 .top-stories-backgrounds .story {
    background-color: #367AA4;
    background-image: url(../img/empty-image.png);
    background-position: center;
    background-repeat: no-repeat;
}

.step1 .bottom-stories .story {
    background-position: center;
    background-repeat: no-repeat;
}

.step1 .bottom-stories .story.story1 {
    background-image: url(../img/stories/empty.jpg);
}
.step1 .bottom-stories .story.story2 {
    background-image: url(../img/stories/empty.jpg);
}
.step1 .bottom-stories .story.story3 {
    background-image: url(../img/stories/empty.jpg);
}
.step1 .bottom-stories .story.story4 {
    background-image: url(../img/stories/empty.jpg);
}
.step1 .bottom-stories .story.story5 {
    background-image: url(../img/stories/empty.jpg);
}

.step1 .bottom-stories .story.filled {
    background-image:none;
    background-color: #fff;
}

.step1 .bottom-stories .story.first,
.step1 .top-stories .story.first {
    
}
.step1 .bottom-stories .story.first,
.step1 .top-stories .story.last {
    
}

.step1 .bottom-stories .story img,
.step1 .top-stories .story img {
    width: 100%;
}

.step1 .top-stories .story {
    position: relative;
    z-index:3;
}

/* step 2 */
.step2 .arrow_right {
	position: absolute;
	width: 45px;
	height: 45px;
	top: 370px;
    left: 330px;
	background: url("../img/arrow_right.png") no-repeat;
	z-index: 2;
}

.step2 .compass {
	position: absolute;
	width: 150px;
	height: 150px;
	bottom: 0;
	right: 30px;
	background: url("../img/compass.png") no-repeat;
	background-size: 150px 150px;
	z-index: 2;
}

.step2 .top-stories {
	position: absolute;
	top: 40px;
	left: 60px;
	background: url("../img/empty-image.png") repeat-x center top;
}

.step2 .top-stories .bg {
	background: #367aa4 url("../img/empty-image.png") no-repeat center center;
	border-radius: 3px;
}

.step2 .top-stories .story {
	border-radius: 3px;
	margin-bottom: 5px;
}

.step2 .top-stories .story img {
	display: block;
	width: 180px;
	height: 135px;
	background-color: #fff;
	overflow: hidden;
	border-radius: 3px;
}

.step2 .top-stories-backgrounds .story {
	background-color: #367AA4;
	background-image: url(../img/empty-image.png);
	background-position: center;
	background-repeat: no-repeat;
}

.step2 .bottom-stories {
	position: absolute;
	width: 75%;
	height: 100%;
	top: 0;
	left: 25%;
}

.step2 .bottom-stories .wrap {
	position: relative;
}


.step2 .bottom-stories .story {

	position: absolute;
	width: 180px;
	height: 135px;
	border-radius: 3px;

}

.step2 .bottom-stories .story p {
	position: absolute;
	bottom: 52px;
	left: 0;
	right: 0;
	margin: 0 auto;
	color: #78cee7;
	font-weight: 900;
	font-size: 24px;
	z-index: 1;
}

.step2 .bottom-stories .story p.hidden {
	display: none;
}

.step2 .bottom-stories .story.story1 {
	top: 550px;
	left: 200px;
	background: #367aa4;
	background-size: 180px 135px;
}
.step2 .bottom-stories .story.story1:after {
	left: 100%;
	top: 50%;
	border: solid transparent;
	content: " ";
	height: 0;
	width: 0;
	position: absolute;
	pointer-events: none;
	border-color: rgba(86, 176, 203, 0);
	border-left-color: #367aa4;
	border-width: 20px;
	margin-top: -20px;
}
.step2 .bottom-stories .story.story1.filled:after {
	left: 100%;
	top: 50%;
	border: solid transparent;
	content: " ";
	height: 0;
	width: 0;
	position: absolute;
	pointer-events: none;
	border-color: rgba(255, 255, 255, 0);
	border-left-color: #ffffff;
	border-width: 20px;
	margin-top: -20px;
}
.step2 .bottom-stories .story.story2 {
	top: 200px;
	left: 255px;
	background: #367aa4;
	background-size: 180px 135px;
}
.step2 .bottom-stories .story.story2:after {
	left: 100%;
	top: 50%;
	border: solid transparent;
	content: " ";
	height: 0;
	width: 0;
	position: absolute;
	pointer-events: none;
	border-color: rgba(86, 176, 203, 0);
	border-left-color: #367aa4;
	border-width: 20px;
	margin-top: -20px;
}
.step2 .bottom-stories .story.story2.filled:after {
	left: 100%;
	top: 50%;
	border: solid transparent;
	content: " ";
	height: 0;
	width: 0;
	position: absolute;
	pointer-events: none;
	border-color: rgba(255, 255, 255, 0);
	border-left-color: #ffffff;
	border-width: 20px;
	margin-top: -20px;
}
.step2 .bottom-stories .story.story3 {
	top: 380px;
	left: 430px;
	background: #367aa4;
	background-size: 180px 135px;
}
.step2 .bottom-stories .story.story4 {
	top: 150px;
	left: 550px;
	background: #367aa4;
	background-size: 180px 135px;
}
.step2 .bottom-stories .story.story4:after {
	right: 100%;
	top: 50%;
	border: solid transparent;
	content: " ";
	height: 0;
	width: 0;
	position: absolute;
	pointer-events: none;
	border-color: rgba(86, 176, 203, 0);
	border-right-color: #367aa4;
	border-width: 20px;
	margin-top: -20px;
}
.step2 .bottom-stories .story.story4.filled:after {
	right: 100%;
	top: 50%;
	border: solid transparent;
	content: " ";
	height: 0;
	width: 0;
	position: absolute;
	pointer-events: none;
	border-color: rgba(255, 255, 255, 0);
	border-right-color: #ffffff;
	border-width: 20px;
	margin-top: -20px;
}
.step2 .bottom-stories .story.story5 {
	top: 520px;
	left: 430px;
	background: #367aa4;
	background-size: 180px 135px;
}
.step2 .bottom-stories .story.story5:after {
	right: 100%;
	top: 50%;
	border: solid transparent;
	content: " ";
	height: 0;
	width: 0;
	position: absolute;
	pointer-events: none;
	border-color: rgba(86, 176, 203, 0);
	border-right-color: #367aa4;
	border-width: 20px;
	margin-top: -20px;
}

.step2 .bottom-stories .story.story5.filled:after {
	right: 100%;
	top: 50%;
	border: solid transparent;
	content: " ";
	height: 0;
	width: 0;
	position: absolute;
	pointer-events: none;
	border-width: 20px;
	margin-top: -20px;
	border-color: rgba(255, 255, 255, 0);
	border-right-color: #ffffff;
}

.step2 .bottom-stories .story.filled {
	background-image:none;
	background-color: #fff;
}

.step2 .bottom-stories .story.filled img{
	width: 180px;
	height: 135px;
}

.step2 .top-stories .story {
	position: relative;
	z-index:3;
}


/* start/win/loose */
.win-screen,
.loose-screen,
.start-screen,
.start-screen2 {
	display: none;
	z-index: 5;
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	overflow: hidden;
	background: rgba(0,0,0,0.5);
}

.start-screen {
	display: block;
}

.win-screen .container,
.loose-screen .container,
.start-screen .container,
.start-screen2 .container {
	position: absolute;
	background: white;
	border-radius: 10px;
	width: 550px;
	height: 600px;
	top: 50%;
	margin-top: -320px;
	left: 50%;
	margin-left: -275px;
	padding: 30px 60px;
}

.win-screen .container,
.loose-screen .container {
	padding-top: 60px;
}

.start-screen .container,
.start-screen2 .container {
	height: 320px;
	margin-top: -100px;
}

.start-screen .container h3,
.start-screen2 .container h3 {
	margin-bottom: 30px;
}

.start-screen2 .container img,
.start-screen .container img {
	width: 120px;
	height: 120px;
	border-radius: 50%;
	margin-top: 15px;
	border: 3px solid rgba(0,0,0,0.1);
}

.start-screen .container img.rotate {
	-ms-transform: rotate(90deg); /* IE 9 */
	-webkit-transform: rotate(90deg); /* Chrome, Safari, Opera */
	transform: rotate(90deg);
}

.start-screen .container .start,
.start-screen2 .container .start2 {
	margin-top: 30px;
}


.start-screen .container .divider,
.start-screen2 .container .divider {
	height: 2px;
	background: #d5dae0;
	margin: 30px 0;
}


.win-screen .container h2,
.loose-screen .container h2 {
	padding: 50px 0 40px 0;
	font-size: 42px;
}

.win-screen .container a,
.loose-screen .container a,
.start-screen .container a,
.start-screen2 .container a {
	width: 100%;
}

.container .win,
.container .loose {
	margin: 0 auto;
	border-radius: 50%;
	width: 180px;
	height: 180px;
	margin-top: 30px;
	margin-bottom: 30px;
}

.container .win {
	background: #79cee8;
}

.win-screen .container .win img {
	margin-top: 50px;
}




.big-btn {
	background-color: #f9b56e;
	color: #fff;
	border-bottom: solid 3px #f69025;
}
.big-btn:hover {
	border-bottom: solid 3px #f9b56e;
}

.confirmBtnHolder {
	color: #816e81;
}

.cancelBtnHolder .big-btn {
	background-color: transparent;
	border: 2px solid #fff;
	color: #956f9e;
	background-image: url('../img/close.png');  
}

.cancelBtnHolder .big-btn:hover {
	background-color: rgba(255,255,255,0.3);    
}

img[src=""] { display: none; }

.ui-draggable-dragging {
	opacity: 1;
	-webkit-transition: opacity 300ms ease;
	-moz-transition: opacity 300m sease;
	-o-transition: opacity 300ms ease;
	transition: opacity 300ms ease;  
}

.ui-draggable-dragging {
	opacity: 0.85;
}

.btn-reset {
	background-color: #e96982;
	border-bottom: solid 3px #e23d5d;
}
.btn-reset:hover {
	border-bottom: solid 3px #e96982;
}

#verifySequence {
	background: #e86981;
	border-bottom: solid 3px #b35163;
}