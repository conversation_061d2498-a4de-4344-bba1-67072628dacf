.top-stories {
    float: left;
    width: 100%;
    height: 163px;
}

h1 {
    color: #686262;
    margin-bottom: 0px;
}

.heading p {
    margin-top: 0px;
    margin-bottom: 55px;
    color: #686262;
}

.story{
    cursor: pointer;
}

.top-stories .story,
.bottom-stories .story{
    width: calc(20% - 26px);
    float: left;
    margin-left:12.5px;
    margin-right:12.5px;
    text-align: center;
    padding-left: 12.5px;
    padding-right: 12.5px;
    padding-top: 25px;
    padding-bottom: 25px;
    -moz-border-radius: 4px;
    -webkit-border-radius: 4px;
    border-radius: 4px;
    height: 163px;
}

.bottom-stories .story{
    background-color: #fff;
}

.top-stories .matched:after {
  width: 4px;
  background-color: #dbd0cb;
  height: 80px;
  bottom: -70px;
  display: block;
  content: '';
  position: relative;
  margin: 0 auto;
}

.top-stories .story.purple,
.top-stories .story.purple.active:before,
.story.purple.matched:before, 
.story.purple.matched:after {
    background-color: #816289;
}

.top-stories .story.blue,
.top-stories .story.blue.active:before,
.story.blue.matched:before,
.story.blue.matched:after {
    background-color: #479dc0;
}

.top-stories .story.orange,
.top-stories .story.orange.active:before,
.story.orange.matched:before,
.story.orange.matched:after  {
    background-color: #f7bc80;
}

.top-stories .story.pink,
.top-stories .story.pink.active:before,
.story.pink.matched:before,
.story.pink.matched:after   {
    background-color: #ce4e6d;
}

.top-stories .story.darkblue,
.top-stories .story.darkblue.active:before,
.story.darkblue.matched:before,
.story.darkblue.matched:after  {
    background-color: #31425b;
}

.top-stories .story.purple .color_block {
    background-color: #ac88b5;
}

.top-stories .story.blue .color_block {
    background-color: #79cee8;
}

.top-stories .story.orange .color_block {
    background-color: #e59751;
}

.top-stories .story.pink .color_block {
    background-color: #e86981;
}

.top-stories .story.darkblue .color_block {
    background-color: #516382;
}


.story:not(.ui-draggable-dragging){
    left: 0px!important;
}

.bottom-stories .story {
    background-position: center;
    background-repeat: no-repeat;
}

.bottom-stories .story.filled {
    background-image:none;
    background-color: #fff;
}

.bottom-stories .story img,
.top-stories .story img {
    width: 100%;
}

.top-stories img.audio_icon {
    width: 50px;
}

.top-stories .story {
    position: relative;
    z-index:999;
}

.color_block {
    position: absolute;
    content: "";
    display: block;
    width: 100%;
    height: 30%;
    box-sizing: border-box;
    bottom: 0px;
    left: 0;
    border-bottom-left-radius: 5px;
    border-bottom-right-radius: 5px;
    background-color: red;
}

.spacer {
    float: left;
    background-position: center;
    background-repeat: no-repeat;
    width:100%;
    height: 150px;
}


img[src=""] { display: none; }

.ui-draggable-dragging {
    opacity: 1;
     -webkit-transition: opacity 300ms ease;
    -moz-transition: opacity 300m sease;
    -o-transition: opacity 300ms ease;
    transition: opacity 300ms ease;  
}

.ui-draggable-dragging {
    opacity: 0.85;
}

.btn-reset {
    background-color: #e96982;
    border-bottom: solid 3px #e23d5d;
}
.btn-reset:hover {
    border-bottom: solid 3px #e96982;
}

.story .audio_btn {
    top: 10px;
    position: relative;
    display: block;
    margin: 0 auto;
    border-radius: 50%;
    width: 60px;
    height: 60px;
    background-color: #fff;
}

.story .audio_btn .button_bg {
    background-image: url('/interactives/whoami/img/arrow.png');
    display: block;
    height: 60px;
    background-repeat: no-repeat;
    background-position: 50%;
}

.story .audio_btn:hover {
    cursor: pointer
}

.bottom-stories .story:before {
  border-radius: 50%;
  position: relative;
  display: block;
  width: 20px;
  height: 20px;
  content: "";
  background-color: #dbd0cb;
  top: -70px;
  margin: 0 auto;
}

.bottom-stories .story.active:before {
    background-color: #686262;
}


.top-stories .story:before {
  border-radius: 50%;
  position: absolute;
  display: block;
  width: 20px;
  height: 20px;
  content: "";
  background-color: #dbd0cb;
  bottom: -50px;
  left: calc(50% - 10px);
}

.bottom-stories .story img {
    top: -30px;
    position: relative;
}

