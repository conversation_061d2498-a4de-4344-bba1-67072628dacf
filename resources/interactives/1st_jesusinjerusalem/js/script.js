Array.prototype.clone = function() {
	return this.slice(0);
};

/*

TODO:
- Need door image in better res for start-screen

*/

$(function() {

	// 0 - path
	// 1 - wall
	// 2-5 - doors
	// 6-10 - building
	var maze = [[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,10,1,0,0,0],
				[0,0,0,0,0,0,0,0,0,0,1,0,1,0,1,0,1,0,0,0,0,0,0],
				[0,0,0,0,0,0,0,0,0,1,0,1,0,0,0,0,0,1,0,1,0,0,0],
				[0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0],
				[0,0,0,0,0,0,0,0,0,1,0,1,0,0,0,1,6,0,0,1,0,0,0],
				[0,0,0,0,0,0,1,0,1,0,0,0,0,0,0,0,1,0,1,0,1,0,0],
				[0,0,0,0,0,1,0,0,0,0,0,1,0,1,0,1,0,0,0,0,0,1,0],
				[1,0,1,0,1,0,0,0,1,0,1,0,0,0,0,0,1,0,1,0,0,0,0],
				[9,1,0,0,0,0,0,0,0,0,0,1,0,1,0,0,0,0,0,1,0,1,0],
				[1,0,1,0,1,0,1,0,1,0,0,0,0,0,1,0,1,0,0,0,0,0,0],
				[0,1,0,1,0,0,4,0,0,0,0,0,5,0,0,1,0,0,0,1,0,1,0],
				[0,0,0,0,0,0,0,0,1,0,1,0,1,0,0,0,0,0,1,0,0,0,1],
				[0,1,0,1,0,1,0,1,0,0,0,0,0,0,0,1,0,0,0,1,0,1,11],
				[0,0,0,0,0,0,0,0,0,0,1,0,1,0,1,0,1,0,0,0,0,0,1],
				[0,1,0,0,0,1,0,1,0,1,0,0,0,0,0,0,0,1,0,0,0,1,0],
				[1,0,1,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,1,0,1,0,0],
				[8,1,0,0,0,1,3,1,0,1,12,1,0,0,0,0,0,0,0,0,0,0,0],
				[1,0,1,0,1,0,0,0,1,0,1,0,0,0,0,0,0,0,0,0,0,0,0],
				[0,0,0,1,0,1,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0],
				[0,0,0,0,0,0,1,0,0,0,1,0,1,0,0,0,0,0,0,0,0,0,0],
				[0,0,0,1,0,0,0,0,0,0,2,0,0,1,0,0,0,0,0,0,0,0,0],
				[0,0,0,0,1,0,1,0,1,0,1,0,0,0,0,0,0,0,0,0,0,0,0],
				[0,0,0,1,7,1,0,0,0,0,0,1,0,1,0,0,0,0,0,0,0,0,0]];

	var tempMapOfINdex = [0, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22];

	var character;
	var charStep = 46;

	var keys = {};

	var score = 0;
	var status;

	var question;

	var isPaused = true;

	var questions = [
		{ 
			question: "Pilate sentenced Jesus to death.",
			answer: true
		},
		{ 
			question: "The soldiers carried the cross for Jesus.",
			answer: false
		},
		{ 
			question: "Jesus died in the city of Bethlehem.",
			answer: false
		},
		{ 
			question: "Jesus fell three times while he was carrying the cross.",
			answer: true
		},
		{ 
			question: "We call the day on which we remember Jesus' death 'Good Friday'.",
			answer: true
		},
	];

	var soundOnePlayed = false;

	var audiotypes={
		"mp3": "audio/mpeg",
		"mp4": "audio/mp4",
		"ogg": "audio/ogg",
		"wav": "audio/wav"
	}


	function init() {

		character = $('.character');

		status = $('.status img');

		question = $('.question');

		resumeGame();

	}

	function ss_soundbits(sound, loop) {
		sound = '/interactives/dragndrop/sounds/' + sound;

		var audio_element = document.createElement('audio')
		if (audio_element.canPlayType){
			for (var i=0; i == 0 && i<arguments.length; i++){
				for(var key in audiotypes) {
					var source_element = document.createElement('source')
					source_element.setAttribute('src', arguments[i] + "." + key)
					source_element.setAttribute('type', audiotypes[key])
					audio_element.appendChild(source_element);
				}
			}

			//audio_element.play()
			audio_element.playclip=function(){
				this.pause()
				//this.currentTime=0
				this.play()
			}

			if(loop) {
				audio_element.addEventListener('ended', function() {
					ss_soundbits(sound.replace('/interactives/dragndrop/sounds/', ''), loop).playclip();
				}, false);
			}

			return audio_element
		}
	}

	function resumeGame() {

		isPaused = false;

	}

	function pauseGame() {

		isPaused = true;

	}

	// score
	function addScore() {

		score++;

		ss_soundbits('win2').playclip();

		if(score == 1) {
			setTimeout(function () {
				pauseGame();
				showWinScreen();
			}, 300);
		}

	}

	// Character
	function startCharacter() {
		character.removeClass('stopped');
	}

	function stopCharacter() {
		character.addClass('stopped');
	}

	// Miscellaneous
	function showQuestion(door) {

		if(questions.length > 0) {

			$('.question-screen').show();

			var q = Math.floor(Math.random() * questions.length);
			var answer;

			question.text(questions[q].question);
			answer = questions[q].answer;

			$('.question-screen input[name=answer]').val(answer);
			$('.question-screen input[name=door]').val(door);

			questions.splice(q, 1);


		} else {

			showLooseScreen();

		}

	}

	function updateDoor(door) {

		// update maze matrix
		for(var i = 0; i < maze.length; i++) {
			for(var k = 0; k < maze[i].length; k++) {
				if(maze[i][k] == door) {
					maze[i][k] = 0;
					return;
				}
			}
		}

	}

	function revealBuilding(building) {

		var add = false;
		
		if(building == 7 && !$('.buildings .b1').hasClass("active")) {
			$('.buildings .b1').addClass("active");
			add = true;
		}

		if(building == 8 && !$('.buildings .b2').hasClass("active")) {
			$('.buildings .b2').addClass("active");
			add = true;

		}

		if(building == 9 && !$('.buildings .b3').hasClass("active")) {
			$('.buildings .b3').addClass("active");
			add = true;

		}

		if(building == 10 && !$('.buildings .b4').hasClass("active")) {
			$('.buildings .b4').addClass("active");
			add = true;

		}

		if(building == 11 && !$('.buildings .b5').hasClass("active")) {
			$('.buildings .b5').addClass("active");
			add = true;

		}

		if(building == 12 && !$('.buildings .b6').hasClass("active")) {
			$('.buildings .b6').addClass("active");
			add = true;
		}

		if(add === true) {
			addScore();
		}

	}

	function gameLoop() {

		var leftBound = 236;
		var topBound = 195;

		/*var matrix = [];

		for(var i = 0; i < 23; i++) {
			var positions = [];
			for(var k = 0; k < 23; k++) {
				positions.push(0);
			}
			matrix.push(positions);
		}*/


		var posX = character.css('left').replace('px' ,'');
		var posY = character.css('top').replace('px' ,'');

		var tempK = tempMapOfINdex[(posX - leftBound) / charStep];
		var tempI = tempMapOfINdex[(posY - topBound) / charStep];

		var skipLoop = false;

		/*if((e.which && String.fromCharCode(e.which).toLowerCase() == 'a') || (e.keyCode && String.fromCharCode(e.which).toLowerCase() == 'a') && !isPaused) {
			//left wall	
			if(tempK - 1 > 0) {
				matrix[tempI][tempK - 1] = matrix[tempI][tempK - 1] == 0 ? 1 : 0;
			}
		}
		else if((e.which && String.fromCharCode(e.which).toLowerCase() == 's') || (e.keyCode && String.fromCharCode(e.which).toLowerCase() == 's') && !isPaused) {
			// bottom wall	
			if(tempI + 1 < matrix.length) {
				matrix[tempI + 1][tempK] = matrix[tempI + 1][tempK] == 0 ? 1 : 0;
			}
		}
		else if((e.which && String.fromCharCode(e.which).toLowerCase() == 'd') || (e.keyCode && String.fromCharCode(e.which).toLowerCase() == 'd') && !isPaused) {
			// right wall
			if(tempK + 1 < matrix[tempI].length) {
				matrix[tempI][tempK + 1] = matrix[tempI][tempK + 1] == 0 ? 1 : 0;
			}
		} else if((e.which && String.fromCharCode(e.which).toLowerCase() == 'w') || (e.keyCode && String.fromCharCode(e.which).toLowerCase() == 'w') && !isPaused) {
			//top wall
			if(tempI - 1 > 0) {
				matrix[tempI - 1][tempK] = matrix[tempI - 1][tempK] == 0 ? 1 : 0;
			}
		} else if((e.which && String.fromCharCode(e.which).toLowerCase() == 'q') || (e.keyCode && String.fromCharCode(e.which).toLowerCase() == 'q') && !isPaused) {
			//top wall
			if(tempI > 0) {
				matrix[tempI][tempK] = 10;
			}
		}*/

		if(tempI !== 0 && !tempI) {
			skipLoop = true;
		}
		if(tempK !== 0 && !tempK) {
			skipLoop = true;
		}

		if(isPaused) {
			skipLoop = true;
		}

		if(!skipLoop) {

			// arrow down (657)
			if(keys[40]) {
				// bottom wall	
				if(tempI + 1 < maze.length && maze[tempI + 1][tempK] != 1) {
					character.removeClass().addClass("character down");
					character.stop(false, true).animate({
						top:'+='+charStep
					}, {
						duration: 200, 
						easing: "linear",
						complete: function () {

							var pX = character.css('left').replace('px' ,'');
							var pY = character.css('top').replace('px' ,'');
							var tI = tempMapOfINdex[(pY - topBound) / charStep];
							var tK = tempMapOfINdex[(pX - leftBound) / charStep];
							if(maze[tI][tK] != 0) {
								// doors
								if(maze[tI][tK] >= 2 && maze[tI][tK] <=6) {
									pauseGame();
									showQuestion(maze[tI][tK]);
								}
								// buildings
								if(maze[tI][tK] >= 7 && maze[tI][tK] <= 12) {
									revealBuilding(maze[tI][tK]);
								}
							}

							character.addClass("stopped");
						}
					})
				}
			}

			// arrow up (239)
			else if(keys[38]) {
				if(tempI - 1 > 0 && maze[tempI - 1][tempK] != 1) {
					character.removeClass().addClass("character up");
					character.stop(false, true).animate({
						top:'-='+charStep
					}, {
						duration: 200, 
						easing: "linear",
						complete: function () {

							var pX = character.css('left').replace('px' ,'');
							var pY = character.css('top').replace('px' ,'');
							var tI = tempMapOfINdex[(pY - topBound) / charStep];
							var tK = tempMapOfINdex[(pX - leftBound) / charStep];
							if(maze[tI][tK] != 0) {
								// doors
								if(maze[tI][tK] >= 2 && maze[tI][tK] <=6) {
									pauseGame();
									showQuestion(maze[tI][tK]);
								}
								// buildings
								if(maze[tI][tK] >= 7 && maze[tI][tK] <= 12) {
									revealBuilding(maze[tI][tK]);
								}
							}

							character.addClass("stopped");
						}	
					});
				}
			}

			// arrow left (282)
			else if(keys[37]) {
				if(tempK - 1 > 0 && maze[tempI][tempK - 1] != 1) {
					character.removeClass().addClass("character left");
					character.stop(false, true).animate({
						left:'-='+charStep
					}, {
						duration: 200,
						easing: "linear",
						complete: function () {

							var pX = character.css('left').replace('px' ,'');
							var pY = character.css('top').replace('px' ,'');
							var tI = tempMapOfINdex[(pY - topBound) / charStep];
							var tK = tempMapOfINdex[(pX - leftBound) / charStep];
							if(maze[tI][tK] != 0) {
								// doors
								if(maze[tI][tK] >= 2 && maze[tI][tK] <=6) {
									pauseGame();
									showQuestion(maze[tI][tK]);
								}
								// buildings
								if(maze[tI][tK] >= 7 && maze[tI][tK] <= 12) {
									revealBuilding(maze[tI][tK]);
								}
							}

							character.addClass("stopped");
						}
					});
				}
			}

			// arrow right (700)
			else if(keys[39]) {
				if(tempK + 1 < maze[tempI].length && maze[tempI][tempK + 1] != 1) {
					character.removeClass().addClass("character right");
					character.stop(false, true).animate({
						left:'+='+charStep
					}, {
						duration: 200, 
						easing: "linear",
						complete: function () {

							var pX = character.css('left').replace('px' ,'');
							var pY = character.css('top').replace('px' ,'');
							var tI = tempMapOfINdex[(pY - topBound) / charStep];
							var tK = tempMapOfINdex[(pX - leftBound) / charStep];
							if(maze[tI][tK] != 0) {
								// doors
								if(maze[tI][tK] >= 2 && maze[tI][tK] <=6) {
									pauseGame();
									showQuestion(maze[tI][tK]);
								}
								// buildings
								if(maze[tI][tK] >= 7 && maze[tI][tK] <= 12) {
									revealBuilding(maze[tI][tK]);

								}
							}

							character.addClass("stopped");
						}
					});
				}
			}
		}
		
		setTimeout(function () {
			gameLoop()
		}, 20);

	}

	function showLooseScreen() {

		ss_soundbits('negative').playclip();
		$(".loose-screen").show();

	}

	function showWinScreen() {

		ss_soundbits('win').playclip();
		$(".win-screen").show();

	}

	$(document).ready(function () {

		$('.replay').click(function (e) {
			window.location.reload();
			e.preventDefault();
			e.stopPropagation();
		});

		$('.question-screen .answer').click(function () {

			var answer = $(this).hasClass("true") ? "true" : "false";
			var correctAnswer = $(this).parent().find("input[name=answer]").val();
			var door = $(this).parent().find("input[name=door]").val();

			if(answer == correctAnswer) {
				$(".question-screen").hide();
				$(".doors div[data-door='" + door + "']").remove();
				updateDoor(door);
				resumeGame();
			} else {
				pauseGame();
				showLooseScreen();
			}

		});

		$('.start').click(function (e) {
			e.preventDefault();
			e.stopPropagation();

			init();
			gameLoop();

			$('.start-screen').hide();
		});

		$(document).on("keydown", function(event) {
			keys[event.which] = true;
		}).keyup(function(event){
			delete keys[event.which];
		});

	});

});
