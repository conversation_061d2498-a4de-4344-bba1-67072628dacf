<!DOCTYPE html>
<!--
Copyright (c) 2003-2014, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
-->
<html>
<head>
	<meta charset="utf-8">
	<title>Mathematical Formulas &mdash; CKEditor Sample</title>
	<script src="../../../ckeditor.js"></script>
	<link href="../../../samples/sample.css" rel="stylesheet">
	<meta name="ckeditor-sample-name" content="Mathematics plugin">
	<meta name="ckeditor-sample-group" content="Plugins">
	<meta name="ckeditor-sample-description" content="Create mathematical equations in TeX and display them in visual form.">
	<meta name="ckeditor-sample-isnew" content="1">
	<script>
		CKEDITOR.disableAutoInline = true;
	</script>
</head>
<body>
	<h1 class="samples">
		<a href="../../../samples/index.html">CKEditor Samples</a> &raquo; Mathematical Formulas
	</h1>

	<div class="description">
		<p>
			This sample shows the usage of the CKEditor mathematical plugin that introduces a MathJax widget. You can now use it to create or modify equations using TeX.
		</p>
		<p>
			TeX content will be automatically replaced by a widget when you put it in a <code>&lt;span class="math-tex"&gt;</code> element. You can also add new equations by using the <strong>Math</strong> toolbar button and entering TeX content in the plugin dialog window. After you click <strong>OK</strong>, a widget will be inserted into the editor content.
		</p>
		<p>
			The output of the editor will be plain TeX with <a href="http://www.mathjax.org/">MathJax</a> delimiters: <code>\(</code> and <code>\)</code>, as in the code below:
		</p>
<pre class="samples">
&lt;span class="math-tex"&gt;\( \sqrt{1} + (1)^2 = 2 \)&lt;/span&gt;
</pre>
		<p>
			To transform TeX into a visual equation, a page must include the <a href="http://docs.mathjax.org/en/latest/start.html">MathJax script</a>.
		</p>
		<p>
			In order to use the new plugin, include it in the <code><a class="samples" href="http://docs.ckeditor.com/#!/api/CKEDITOR.config-cfg-extraPlugins">config.extraPlugins</a></code> configuration setting.
		</p>
<pre class="samples">
CKEDITOR.replace( '<em>textarea_id</em>', {
	<strong>extraPlugins: 'mathjax'</strong>
} );
</pre>
		<p id="ie8-warning">
			Please note that this plugin is not compatible with Internet Explorer 8.
		</p>
	</div>

	<textarea id="editor1" cols="10" rows="10">

		&lt;p&gt;The following equations are represented in the HTML source code as LaTeX expressions.&lt;/p&gt;&lt;h1&gt;The Cauchy-Schwarz Inequality&lt;/h1&gt;&lt;p&gt;&lt;span class=&quot;math-tex&quot;&gt;\( \left( \sum_{k=1}^n a_k b_k \right)^2 \leq \left( \sum_{k=1}^n a_k^2 \right) \left( \sum_{k=1}^n b_k^2 \right) \)&lt;/span&gt;&lt;/p&gt;&lt;h1&gt;The probability of getting &lt;span class=&quot;math-tex&quot;&gt;\(k\)&lt;/span&gt; heads when flipping &lt;span class=&quot;math-tex&quot;&gt;\(n\)&lt;/span&gt; coins is&lt;/h1&gt;&lt;p&gt;&lt;span class=&quot;math-tex&quot;&gt;\(P(E) = {n \choose k} p^k (1-p)^{ n-k} \)&lt;/span&gt;&lt;/p&gt;&lt;p&gt;Finally, while displaying equations is useful for demonstration purposes, the ability to mix math and text in a paragraph is also important. This expression &lt;span class=&quot;math-tex&quot;&gt;\(\sqrt{3x-1}+(1+x)^2\)&lt;/span&gt; is an example of an inline equation.  As you see, MathJax equations can be used this way as well, without disturbing the spacing between the lines.&lt;/p&gt;

	</textarea>
	<script>

		CKEDITOR.replace( 'editor1', {
			extraPlugins: 'mathjax',
			height: 350
		} );

		if ( CKEDITOR.env.ie && CKEDITOR.env.version == 8 )
			document.getElementById( 'ie8-warning' ).className = 'warning';

	</script>

	<div id="footer">
		<hr>
		<p>
			CKEditor - The text editor for the Internet - <a class="samples" href="http://ckeditor.com/">http://ckeditor.com</a>
		</p>
		<p id="copy">
			Copyright &copy; 2003-2014, <a class="samples" href="http://cksource.com/">CKSource</a> - Frederico
			Knabben. All rights reserved.
		</p>
	</div>
</body>
</html>
