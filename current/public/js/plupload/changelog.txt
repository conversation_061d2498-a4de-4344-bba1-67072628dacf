Version 1.5.4 (2012-04-12)
	Flash: Disable scripting if swf was loaded from another domain.
Version 1.5.3 (2012-04-05)
	HTML5: Check if xhr object is initialized, before calling abort() on it.
	HTML4: Postpone form removal until uploaders state changes, to avoid error on resuming stopped uploads.
	BrowserPlus: Fix mechanical typo, that caused error during mime type check.
	BrowserPlus: browserPlus.Uploader.Cancel() has two required parameters, dies with the error if not passed.
	Flash: Improve runtime's behaviour during upload cancellation.
	Flash: Survive the case when GPSVersionID contains unexpected value.
	Flash: Fix random freeze in Chrome's bundled Flash Player.
	Flash: Avoid the silent break when URLStream not yet open, but close is called on it.
	Flash: Move Destroy handler out of Flash:Init handler, since it might be called not only after Flash:Init but also before it.
	Flash: Avoid warning during build with mxmlc.
	Try removeEventListener first in IE and only if it fails - detachEvent.
	Fix plupload.getPos to return proper value in IE8+.
	Do not initiate plupload.STARTED state, if file queue is empty.
	Additional language packs: Estonian, Polish, Korean, French-Canadian, Greek, Persian/Farsi.
Version 1.5.2 (2012-01-06)
	UI Widget: Do not show UI if no runtime can be initialized.
	UI Widget: Timely update file size and total size if resize in action.
	UI Widget: Constrain renaming feature to queued files only.
	UI Widget: Disable Add button properly, if requested, rather then just hide.
	HTML4/HTML5/BrowserPlus: Avoid adding mime type twice to dialog trigger.
	HTML5: fix regression, when unresized images were failing on FF3.6.
	HTML5: Constrain Gecko 2,5,6 workaround to multipart mode only.
	HTML5/Flash: Take into account weird possibilities of ExifVersion being a string, rather then standard Undefined.
	Flash: Simplify event dispatching in BitmapDataUnlimited class, in order to avoid freezing on resizing in FP11.
	Add ability to disable file dialog trigger on request (uploader.disableBrowse(true/false)).
	Support for immediate abort of upload process, be it chunked upload or regular one.
	Abort all activity, before destroying uploader.
	Revive temporary file removal logic in upload.php.
	Fix potential vulnerability in dump.php and upload.php.
	Additional MIME types: application/vnd.openxmlformats-officedocument.*, application/x-javascript, application/json, text/css,css, application/vnd.oasis.opendocument.formula-templat.
	Additional language packs: Hungarian, Croatian, Serbian, Romanian.
Version ******* (2011-09-27)
	HTML5: Fix mechanical typo, that successfully broke drag and drop, wherever could.
Version 1.5.1 (2011-09-26)
	HTML4: Add support for server responses in HTML format.
	HTML5: Disable multiple file selection in Safari 5.x for Windows (see #363).
	HTML5: Gecko 2/5/6 should upload chunks as binary strings when in chunking mode and client side resize is requested.
	Flash: Enforce URLStream mode when custom headers are passed.
	Flash: Fix embedding problems in IE9 (and all other IEs).
	Flash/Gears/BrowserPlus/SilverLight: Expose multi_selection feature, to be used in required_features (mainly to overcome Safari for Windows problem).
	SilverLight: Properly handle custom and null headers.
	UploadComplete moved to fire after the last StateChanged event.
	Additional language packs: Finnish.
Version 1.5b (2011-09-11)
	UI Widget: Fix sortable logic.
	UI Widget: Fix bug, when message was displayed simultaneously across all Plupload UI instances on the page.
	UI Widget: notify() method is now public - users can throw their own messages into the widget header.
	HTML4/HTML5: Revise input[type=file] placement logic to support cursor styling on Geko 2+.
	HTML5: Revise Drag'n'Drop detection logic.
	HTML5: Make Exif and GPS information available to user, introduce two new events: ExifData and GpsData.
	HTML5: Add support for File.prototype.slice() method (mozSlice/webkitSlice) in order to be able to upload files in chunks without preloading.
	HTML5: Remove any JPEG headers before restoring them, 'cause user agent (like Chrome), might be inserting it's own.
	Flash: Remove a limit on the depth of image header check, since it still fails in some cases and performance gain is negligible.
	Flash: Fix regression, when runtime hanged when not images where passed in as images.	
	SilverLight: Fix bug, when JSON serializer was failing on null.
	SilverLight: Remove cast to integer for chunk boundary, which resulted in a wrong size for last chunks on huge files.
	SilverLight: Increase read buffer, add disposal of ImageStream and FileStream, optimize for performance.
	Updated build.xml to include language packs in release package under js/ folder.
	Gears/BrowserPlus: Add support for * file filter.
	BeforeUpload now can cancel upload if required.
	Additional MIME types: text/csv, image/photoshop, video/x-ms-wmv, video/avi, video/webm support
	Additional language packs: Japanese
	Cleaned examples.
Version ******* (2011-04-13)
	Fixed bug in HTML5 runtime, when was reduced by a factor of 100 after every upload.
Version ******* (2011-04-12)
	Fixed build script, mistakenly populating jquery.plupload.queue directory from jquery.ui.plupload sources.
	Fixed script urls in all examples, build script now will alter them automatically from dev to release when needed.
	Fixed isEmptyObj undefined error in HTML4 runtime.
	Fixed bug in UI Widget preventing UploadComplete from triggering.
Version 1.4.3 (2011-04-11)
	Added Latvian language pack and updated French.
	Fixed bug in Flash runtime when JPEG header was not investigated deep enough to reach SOFn marker in large images.
	Fixed bug, when PNGs were cropped to width in Flash runtimes, rather then resized. 
	Fixed Flash to allow multiple uploading of the same file, with different settings.
	Fixed Flash runtime to clean anonymous listeners properly. 
	Fixed HTML5 runtime to resolve to mimeType in case-insensitive way.
	Fixed HTML5/Flash/SilverLight/Gears runtimes for inconsistency in naming of chunks feature, comparing to other runtimes.
	Fixed HTML4/HTML5 runtimes for input[type=file] to outsize contaner effectively enough to fill the whole click area.
	Fixed all runtimes to preserve position (relative/absolute) rule on containers that already have it.
	Fixed SilverLight runtime to support large files (over 2GB).
	Restructured the examples, src and build scripts to make it more clear that jQuery is optional.
	Added support for *.* filter.
	Added support for preserving ICC and IPTC headers when resizing JPEGs.
	Added Image.onerror/onabort handlers to HTML5 in order to gracefully bypass faulty images.
	Added ability to drop image size (by lowering quality), while preserving original dimension (HTML5/Flash/Gears).
	Ported EXIF, ICC, IPTC preservation code to Flash runtime. 
Version 1.4.2 (2011-02-20)
	Added Brazilian Portuguese, German, Russian and Spanish translations.
	Added support for file_data_name option to SilverLight runtime.
	Added support for better quality image resizing to Flash runtime.
	Added support for properly handling images with dimensions up to 8191x8191 pixels to Flash runtime.
	Added 'updatelist' event to UI Widget, which will be triggered every time file list will get redrawn. 
	Added support for dynamically changing options to UI Widget.
	Fixed HTML4 runtime bug, when UploadFile handler was attached twice. 
	Fixed HTML5 to use FileReader.readAsBinaryString() instead of File.getAsBinary() on newer WebKit browsers (like Chrome 9).
	Fixed Flash runtime from sending duplicate Filename param, when using FileReference.upload(). 
	Updated S3 example to illustrate support for a proper progress indication. 
Version 1.4.1 (2011-02-01)
	Added an example on how to use Plupload with Amazon S3 written in PHP but can easily be ported to other languages.
	Fixed bug where hidden input elements wasn't created when the multiple_queues option wasn't used.
	Fixed bug where FF4 would produce an exception about missing BlobBuilder.
Version 1.4.0 (2011-01-26)
	Added removeEvent and removeAllEvents methods and modified addEvent accordingly, in order to support dynamic unload.
	Added unbindAll method.
	Added UploadComplete event, which fires when internal iterator reaches the end of the queue.
	Added public destroy method to plupload object, new event - Destroy, and corresponding handlers to all runtimes.
	Added Czech, Italian, French, Dutch translations.
	Added support for translatable error messages.
	Added two new options: browse_button_hover and browse_button_active, in order to support browse_button interactivity.	
	Added support for 'multi_selection: false' to Silverlight runtime.
	Added support for video/mp4, video/x-m4v and audio/mp4 MIME Types. 
	Added artificial sendAsBinary method to XMLHttpRequest.prototype for browsers that have support for BlobBuilder and typed arrays.
	Added version tracking variable into plupload object and version comment to the header of every file.
	Fixed measurements of browse_button element in order to size and position input[type=file] element to fit it fully.
	Fixed Flash runtime behavior for multiple_select=false and other simpleUpload usage cases: basically new FileReference has to be created for every select dialog. 
	Fixed browser sniffer to match only Safari, for fakeSafariDragDrop (seems like Safari on Mac doesn't require it either).
	Fixed so that ExternalInterface escapes strings properly, before passing them to JS. 
	Fixed eventual reinitialization of flash/silverlight runtimes, especially for cases when object wrapper needed to be programmatically hidden and then shown again.
	Fixed so that Plupload will now ignore files with duplicate names when adding to the queue, in one set. Mainly introduced to work around Safari on Windows bug (https://bugs.webkit.org/show_bug.cgi?id=37957).
	Fixed bug, when final UploadProgress was firing after FileUploaded for Flash simpleUpload. 
	Fixed bug where upload would fail if an error was produced inside the FilesAdded event.
	Fixed bug in Flash runtime when it used a wrong size when resizing, but not chunking.
	Fixed bug in Silverlight runtime that would keep sending 0 byte packages when a picture was chunked before resized.
	Disabled blur filter (is going to be replaced with some bilinear resampling in next release).
	Completely revised UI Widget, to be more jQuery UI oriented. Optionally depends on UI Button, UI Sortable, UI ProgressBar.	
Version 1.3.0 (2010-11-24)
	Added new jQuery UI widget that supports jQuery UI themes.
	Added new multiple_queues option that enables you to upload multiple times in the queue widgets.
	Added support for crossdomain loading of the XAP and SWF files and crossdomain upload.
	Added new multiple_queues option that enables you to upload multiple times in the queue widgets.
	Added support for crossdomain loading of the XAP and SWF files and crossdomain upload.
	Added preinit/init options to to ease up the binding of custom events to queueWidget and the Uploader class.
	Added drag/drop support for Safari until they fix the broken drag/drop support on Windows.
	Added events example file that show how to bind all events and display event specific data.
	Added support for retaining Exif data on images when they where resized using the HTML5 runtime.
	Fixed logic issue with the upload.php example file. Chunking wasn't working correctly.
	Fixed issue with HTML4 not handling the form encoding correctly on older IE versions. Patch contributed by jinxdone.
	Fixed so the HTML4 runtime only submits the defined multipart_params arguments.
	Fixes issue where it wasn't possible to dynamically override url or mutlipart_params for the HTML4 runtime.
	Fixed so all runtimes pass the name, chunk and chunks parameters as multipart parameters instead of querystring parameters.
	Fixed so files are read using the newer FileReader class if it's available if not it tries the older getAsXXX on Gecko.
	Fixed bug where IE 9 beta 1 wouldn't render Silverlight properly.
	Fixed bug where Flash would do extra empty requests if images below a specific size would be uploaded.
	Fixed bug where Google Gears would resize and re-encode images even if the it wasn't changed in scale.
	Fixed bug where the HTML5 runtime wouldn't free memory after each request on Gecko.
Version 1.2.4 (2010-09-08)
	Added new BeforeUpload event to make it easier to override settings before a file is uploaded.
	Added new automatic usage of FileReference in Flash if it's possible. Contributed by Marcel Jackwerth.
	Added new chunking support for Chrome 5 and Firefox 3.6 using the HTML 5 runtime.
	Added new multipart upload support for WebKit using the HTML 5 runtime and the FormData object.
	Added new image scaling method for the Flash runtime contributed by rcoopman.
	Added new alert error message if the user selected invalid files.
	Added new automatic unique name generation to the example.php script. Contributed by Brandon Kelly.
	Changed so the default upload method is multipart and the default chunk size is 0.
	Fixed progress issue with the HTML5 runtime running on Gecko.
	Fixed so longer extensions can be used such as .tar.gz.
	Fixed so the file extension is retained when using the unique_names option.
Version 1.2.3 (2010-05-27)
	Added new drag/drop support for HTML5 running on Chrome beta.
	Added new multipart state for the features object. It's now possible to detect multipart support.
	Added new getFeatures function to all runtime. Basic concept by Javier Martinez Fernandez.
	Fixed bug where runtimes where initialized even if they didn't match the required_features setting.
Version ******* (2010-05-04)
	Added new headers option, enables you to set custom headers for the upload requests.
	Fixed bug where the file extension checking was case sensitive.
Version 1.2.2 (2010-04-26)
	Added new file_data_name option that enables you to set the multipart file data param. Patch contributed by Alex Ganov.
	Added new FILE_SIZE_ERROR type that will be triggered if the user selected a file that is to large or zero bytes.
	Added new FILE_EXTENSION_ERROR type that will be triggered if you add a file with an invalid file extension.
	Added new required_features setting, enables you to specify a list of required features that the runtime must have.
	Fixed so the plupload.buildUrl function uses the UTF compatible encodeURIComponent method instead of escape.
	Fixed so that all file types can be selected if you don't specify a filter setting.
	Fixed so more valid HTTP status codes are accepted as valid responses.
	Fixed so all runtimes fills the features object with available features.
	Fixed some issues with the HTML4 runtime if there wasn't any existing forms on the page.
	Fixed some conflict issues with HTML4 runtime and forms with the input names of action or target.
	Fixed bug where some Gecko versions would produce exceptions when checking the HTTP status of a XHR.
Version 1.2.1 (2010-03-22)
	Fixed bug with incorrect aspect ratio in Flash image scaling.
	Fixed bug where chunked uploads could get scrambled in the Flash runtime. Patch contributed by Grady Werner.
	Fixed bug where a beta version of Chrome wouldn't handle drag/drop correctly because of missing drag effect.
	Fixed so the HTML 4 runtime displays N/A for file sizes and the progress is based on uploaded files instead of bytes.
	Fixed so chunking can be disabled properly in Flash but that will affect the progress bar.
	Fixed so queue widget displays the drag/drop message if file queue is emptied.
	Fixed small files are uploaded as one single chunk and not forced into 4 chunks in the Flash runtime.
Version 1.2 (2010-03-09)
	Added new rename file support for jQuery queue widget, click on a file name to rename it if it's enabled.
	Added official ChunkUploaded event, it similar to FileUploaded but executed for each chunk.
	Added bytes per second support to total queue progress.
	Added better error handling to core API using the new Error event.
	Added better error handling to jQuery queue widget.
	Fixed so chunking uploads is dispatch from JS not from inside Flash/Silverlight.
Version 1.1.1 (2010-02-25)
	Added new setup setting to queue widget. Makes it easier to bind custom events to uploader instance.
	Fixed so it's possible to disable chunking compleatly. It's now disabled by default.
	Fixed bug where multipart mode was enabled all the time in the Flash runtime.
	Fixed bug where chunked uploading in Silverlight would fail.
	Fixed bug where the delete button was visible while uploading.
	Fixed bug where unique_names setting wasn't working when the core API was used.
	Fixed bug where the queue widget wouldn't display the currently uploaded file if the unique_names was enabled.
Version 1.1 (2010-02-24)
	Added new multipart and multipart_params support.
	Added new container option, enables you to specify where flash/silverlight objects would be added.
	Added chunking support to BrowserPlus runtime, contributed by Steve Spencer.
	Added FileUploaded event that fires when a file is uploaded.
	Added more easily understandable buttons to queue widget.
	Added html4 runtime, contributed by Ryan Demmer.
	Fixed issues with i18n support and added a Swedish and Danish language pack.
	Fixed bug where the Flash runtime could do empty requests if the image was scaled down.
	Fixed bug where uploading small images in Silverlight would produce an exception.
	Fixed so the runtime list can include whitespace or missing runtimes. Patch contributed by �yvind Sean Kinsey.
	Fixed so to large files are ignored and never dispatched to the FilesAdded event.
Version 1.0 (2010-02-03)
	First official release of Plupload.
