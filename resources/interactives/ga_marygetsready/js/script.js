Array.prototype.clone = function() {
    return this.slice(0);
};

$(function() {
    var pictures = [
        "img/stories/ir_draggoodbad_marygetsready_01.png",
        "img/stories/ir_draggoodbad_marygetsready_02.png",
        "img/stories/ir_draggoodbad_marygetsready_03.png",
        "img/stories/ir_draggoodbad_marygetsready_04.png",
        "img/stories/ir_draggoodbad_marygetsready_05.png",
        "img/stories/ir_draggoodbad_marygetsready_06.png"
    ];

    var goodPictures = [
        "img/stories/ir_draggoodbad_marygetsready_01.png",
        "img/stories/ir_draggoodbad_marygetsready_03.png",
        "img/stories/ir_draggoodbad_marygetsready_06.png"
    ];

    var audiotypes={
        "mp3": "audio/mpeg",
        "mp4": "audio/mp4",
        "ogg": "audio/ogg",
        "wav": "audio/wav"
    };

    var correctSelections = 0;

    function ss_soundbits(sound, loop){
        sound = '/interactives/dragndrop/sounds/' + sound;
        
        var audio_element = document.createElement('audio');
        if (audio_element.canPlayType){
            for (var i=0; i == 0 && i<arguments.length; i++){
                for(var key in audiotypes) {
                    var source_element = document.createElement('source');
                    source_element.setAttribute('src', arguments[i] + "." + key);
                    source_element.setAttribute('type', audiotypes[key]);
                    audio_element.appendChild(source_element);
                }
            }
            
            //audio_element.play()
            audio_element.playclip=function(){
                this.pause();
                //this.currentTime=0
                this.play();
            };
            
            if(loop) {
                audio_element.addEventListener('ended', function() {
                    ss_soundbits(sound.replace('/interactives/dragndrop/sounds/', ''), loop).playclip();
                }, false);
            }
            
            return audio_element;
        }
    }

    function shuffleImages() {
        var o = pictures.clone();
        for(var j, x, i = o.length; i; j = Math.floor(Math.random() * i), x = o[--i], o[i] = o[j], o[j] = x);
        
        $('.images-holder img').each(function (i, item) {
            $(this).attr("src", o[i]);
        });

        $('.images-holder img').draggable({
            cursor: 'move',
            revert: true
        });
    }

    function handleDropEvent( event, ui ) {
      var draggable = ui.draggable;
      var src = draggable.attr('src');
      var offset = $(this).offset();

        if (goodPictures.indexOf(src) >= 0) {
            correctSelections++;
            ui.draggable.draggable( 'option', 'revert', false );
            ss_soundbits('dragndrop').playclip();

            if (correctSelections == 1) {
                
                draggable.offset({ top: (offset.top + 300), left: (offset.left - 30) });

            } else if (correctSelections == 2) {

                draggable.offset({ top: (offset.top + 300), left: (offset.left + 90) });

            } else if (correctSelections == 3) {

                draggable.offset({ top: (offset.top + 300), left: (offset.left + 210) });

                if(!soundOnePlayed) {
                    ss_soundbits('win').playclip();
                } else {
                    ss_soundbits('win2').playclip();
                }
            }

            draggable.addClass('dropped');
            draggable.draggable('disable');
            $(this).addClass('dropped');

        } else {
            ss_soundbits('negative').playclip();
        }
    }

    var soundOnePlayed = false;
    
    $(document).ready(function () {
        shuffleImages();

        $('.center-drop').droppable({
            hoverClass: 'hovered',
            drop: handleDropEvent
        });
    });

});