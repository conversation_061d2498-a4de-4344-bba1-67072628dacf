<!DOCTYPE html>
<!--
Copyright (c) 2003-2014, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
-->
<html>
<head>
	<meta charset="utf-8">
	<title>Using DevTools Plugin &mdash; CKEditor Sample</title>
	<script src="../../../ckeditor.js"></script>
	<link rel="stylesheet" href="../../../samples/sample.css">
	<meta name="ckeditor-sample-name" content="Developer Tools plugin">
	<meta name="ckeditor-sample-group" content="Plugins">
	<meta name="ckeditor-sample-description" content="Using the Developer Tools plugin to display information about dialog window UI elements to allow for easier customization.">
</head>
<body>
	<h1 class="samples">
		<a href="../../../samples/index.html">CKEditor Samples</a> &raquo; Using the Developer Tools Plugin
	</h1>
	<div class="description">
		<p>
			This sample shows how to configure CKEditor instances to use the
			<strong>Developer Tools</strong> (<code>devtools</code>) plugin that displays
			information about dialog window elements, including the name of the dialog window,
			tab, and UI element. Please note that the tooltip also contains a link to the
			<a href="http://docs.ckeditor.com/#!/api">CKEditor JavaScript API</a>
			documentation for each of the selected elements.
		</p>
		<p>
			This plugin is aimed at developers who would like to customize their CKEditor
			instances and create their own plugins. By default it is turned off; it is
			usually useful to only turn it on in the development phase. Note that it works with
			all CKEditor dialog windows, including the ones that were created by custom plugins.
		</p>
		<p>
			To add a CKEditor instance using the <strong>devtools</strong> plugin, insert
			the following JavaScript call into your code:
		</p>
<pre class="samples">
CKEDITOR.replace( '<em>textarea_id</em>', {
	<strong>extraPlugins: 'devtools'</strong>
});</pre>
		<p>
			Note that <code><em>textarea_id</em></code> in the code above is the <code>id</code> attribute of
			the <code>&lt;textarea&gt;</code> element to be replaced with CKEditor.
		</p>
	</div>
	<form action="../../../samples/sample_posteddata.php" method="post">
		<p>
			<label for="editor1">
				Editor 1:
			</label>
			<textarea cols="80" id="editor1" name="editor1" rows="10">&lt;p&gt;This is some &lt;strong&gt;sample text&lt;/strong&gt;. You are using &lt;a href="http://ckeditor.com/"&gt;CKEditor&lt;/a&gt;.&lt;/p&gt;</textarea>
			<script>

				// This call can be placed at any point after the
				// <textarea>, or inside a <head><script> in a
				// window.onload event handler.

				// Replace the <textarea id="editor"> with an CKEditor
				// instance, using default configurations.
				CKEDITOR.replace( 'editor1', {
					extraPlugins: 'devtools'
				});

			</script>
		</p>
		<p>
			<input type="submit" value="Submit">
		</p>
	</form>
	<div id="footer">
		<hr>
		<p>
			CKEditor - The text editor for the Internet - <a class="samples" href="http://ckeditor.com/">http://ckeditor.com</a>
		</p>
		<p id="copy">
			Copyright &copy; 2003-2014, <a class="samples" href="http://cksource.com/">CKSource</a> - Frederico
			Knabben. All rights reserved.
		</p>
	</div>
</body>
</html>
