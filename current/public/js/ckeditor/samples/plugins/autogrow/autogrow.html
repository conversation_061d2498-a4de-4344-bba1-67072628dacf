<!DOCTYPE html>
<!--
Copyright (c) 2003-2014, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
-->
<html>
<head>
	<meta charset="utf-8">
	<title>AutoGrow Plugin &mdash; CKEditor Sample</title>
	<script src="../../../ckeditor.js"></script>
	<link rel="stylesheet" href="../../../samples/sample.css">
	<meta name="ckeditor-sample-name" content="AutoGrow plugin">
	<meta name="ckeditor-sample-group" content="Plugins">
	<meta name="ckeditor-sample-description" content="Using the AutoGrow plugin in order to make the editor grow to fit the size of its content.">
</head>
<body>
	<h1 class="samples">
		<a href="../../../samples/index.html">CKEditor Samples</a> &raquo; Using AutoGrow Plugin
	</h1>
	<div class="description">
		<p>
			This sample shows how to configure CKEditor instances to use the
			<strong>AutoGrow</strong> (<code>autogrow</code>) plugin that lets the editor window expand
			and shrink depending on the amount and size of content entered in the editing area.
		</p>
		<p>
			In its default implementation the <strong>AutoGrow feature</strong> can expand the
			CKEditor window infinitely in order to avoid introducing scrollbars to the editing area.
		</p>
		<p>
			It is also possible to set a maximum height for the editor window. Once CKEditor
			editing area reaches the value in pixels specified in the
			<code><a class="samples" href="http://docs.ckeditor.com/#!/api/CKEDITOR.config-cfg-autoGrow_maxHeight">autoGrow_maxHeight</a></code>
			configuration setting, scrollbars will be added and the editor window will no longer expand.
		</p>
		<p>
			To add a CKEditor instance using the <code>autogrow</code> plugin and its
			<code>autoGrow_maxHeight</code> attribute, insert the following JavaScript call to your code:
		</p>
<pre class="samples">
CKEDITOR.replace( '<em>textarea_id</em>', {
	<strong>extraPlugins: 'autogrow',</strong>
	autoGrow_maxHeight: 800,

	// Remove the Resize plugin as it does not make sense to use it in conjunction with the AutoGrow plugin.
	removePlugins: 'resize'
});</pre>
		<p>
			Note that <code><em>textarea_id</em></code> in the code above is the <code>id</code> attribute of
			the <code>&lt;textarea&gt;</code> element to be replaced with CKEditor. The maximum height should
			be given in pixels.
		</p>
	</div>
	<form action="../../../samples/sample_posteddata.php" method="post">
		<p>
			<label for="editor1">
				CKEditor using the <code>autogrow</code> plugin with its default configuration:
			</label>
			<textarea cols="80" id="editor1" name="editor1" rows="10">&lt;p&gt;This is some &lt;strong&gt;sample text&lt;/strong&gt;. You are using &lt;a href="http://ckeditor.com/"&gt;CKEditor&lt;/a&gt;.&lt;/p&gt;</textarea>
			<script>

				CKEDITOR.replace( 'editor1', {
					extraPlugins: 'autogrow',
					removePlugins: 'resize'
				});

			</script>
		</p>
		<p>
			<label for="editor2">
				CKEditor using the <code>autogrow</code> plugin with maximum height set to 400 pixels:
			</label>
			<textarea cols="80" id="editor2" name="editor2" rows="10">&lt;p&gt;This is some &lt;strong&gt;sample text&lt;/strong&gt;. You are using &lt;a href="http://ckeditor.com/"&gt;CKEditor&lt;/a&gt;.&lt;/p&gt;</textarea>
			<script>

				CKEDITOR.replace( 'editor2', {
					extraPlugins: 'autogrow',
					autoGrow_maxHeight: 400,
					removePlugins: 'resize'
				});

			</script>
		</p>
		<p>
			<input type="submit" value="Submit">
		</p>
	</form>
	<div id="footer">
		<hr>
		<p>
			CKEditor - The text editor for the Internet - <a class="samples" href="http://ckeditor.com/">http://ckeditor.com</a>
		</p>
		<p id="copy">
			Copyright &copy; 2003-2014, <a class="samples" href="http://cksource.com/">CKSource</a> - Frederico
			Knabben. All rights reserved.
		</p>
	</div>
</body>
</html>
