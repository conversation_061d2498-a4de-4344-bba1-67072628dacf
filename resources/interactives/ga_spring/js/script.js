
$(function() {
    var audiotypes={
        "mp3": "audio/mpeg",
        "mp4": "audio/mp4",
        "ogg": "audio/ogg",
        "wav": "audio/wav"
    };

    function ss_soundbits(sound, loop){
        sound = '/interactives/dragndrop/sounds/' + sound;
        
        var audio_element = document.createElement('audio')
        if (audio_element.canPlayType){
            for (var i=0; i == 0 && i<arguments.length; i++){
                for(var key in audiotypes) {
                    var source_element = document.createElement('source')
                    source_element.setAttribute('src', arguments[i] + "." + key)
                    source_element.setAttribute('type', audiotypes[key])
                    audio_element.appendChild(source_element);
                }
            }
            
            //audio_element.play()
            audio_element.playclip=function(){
                this.pause()
                //this.currentTime=0
                this.play()
            }
            
            if(loop) {
                audio_element.addEventListener('ended', function() {
                    ss_soundbits(sound.replace('/interactives/dragndrop/sounds/', ''), loop).playclip();
                }, false);
            }
            
            return audio_element
        }
    }

    var visible = 0;

    function checkAll() {
        if(visible >= $('.zoom-images img').length) {
            ss_soundbits('win').playclip();
        }
    }

    $(document).ready(function () {
        $('map area').click(function() {
            ss_soundbits('win2').playclip();
            visible++;
            checkAll();
        });

        $('#leaves').click(function(e) {
            $('.difference-leaves').show();
        });

        $('#sky').click(function(e) {
            $('.difference-sky').show();
        });

        $('#clothes').click(function(e) {
            $('.difference-clothes').show();
        });

        $('#birds').click(function(e) {
            $('.difference-birds').show();
        });

        $('#plants').click(function(e) {
            $('.difference-plants').show();
        });
    });
});