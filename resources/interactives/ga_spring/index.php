<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8"> 
    <link rel="stylesheet" type="text/css" href="/interactives/general/css/style.css"/>
    <link rel="stylesheet" type="text/css" href="css/jquery-ui.min.css" />
    <link rel="stylesheet" type="text/css" href="css/style.css" />
</head>
<body>
<script>
	(function(i,s,o,g,r,a,m){i['GoogleAnalyticsObject']=r;i[r]=i[r]||function(){
		(i[r].q=i[r].q||[]).push(arguments)},i[r].l=1*new Date();a=s.createElement(o),
		m=s.getElementsByTagName(o)[0];a.async=1;a.src=g;m.parentNode.insertBefore(a,m)
	})(window,document,'script','https://www.google-analytics.com/analytics.js','ga');

	ga('create', 'UA-2014879-79', 'auto');
	ga('send', 'pageview');

</script>

    <?php 
        $configString = file_get_contents("config.json");
        $config = json_decode($configString);
        $title = $config->title;
        $description = $config->description;
    ?>

    <div class='content'>
        <div class='heading'>
            <h1><?php echo $title; ?></h1>
            <p><?php echo $description; ?></p>
        </div>
        
        <div class="content-holder">
            <div class="diff-image-holder">
                <img src="img/stories/spring.png" class="diff-img" usemap="#Map" >

                <map name="Map" id="Map">
                    <area alt="" title="" shape="circle" coords="100,120,70" id="leaves"/>
                    <area alt="" title="" shape="circle" coords="80,260,70" id="birds"/>
                    <area alt="" title="" shape="circle" coords="400,100,150" id="sky"/>
                    <area alt="" title="" shape="circle" coords="280,520,150" id="clothes"/>
                    <area alt="" title="" shape="circle" coords="80,550,70" id="plants"/>
                </map>

                <div class="zoom-images">
                    <img src="img/stories/difference_01.png" class="difference-clothes">
                    <img src="img/stories/difference_02.png" class="difference-leaves">
                    <img src="img/stories/difference_03.png" class="difference-birds">
                    <img src="img/stories/difference_04.png" class="difference-plants">
                    <img src="img/stories/difference_05.png" class="difference-sky">
                </div>
            </div>

            <div class="diff-image-holder">
                <img src="img/stories/winter.png">
            </div>
        </div>
        
        <?php
        $referer = isset($_SERVER['HTTP_REFERER']) ? $_SERVER['HTTP_REFERER'] : '';

        if($referer != '') {?>
            <div class='cancelBtnHolder'>
                <a class='big-btn' onclick="window.close(); return false;" href="<?= $referer ?>"></a>
            </div>
        <?php } ?>
    </div>
    
    <script type="text/javascript" src="js/jquery-1.11.2.min.js"></script>
    <script type="text/javascript" src="js/jquery-ui.min.js"></script>
    <script type="text/javascript" src="js/script.js"></script>
</body>
</html>