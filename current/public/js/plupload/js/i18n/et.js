// Estonian translation, et.js
plupload.addI18n({
	'Select files' : '<PERSON>i faile',
	'Add files to the upload queue and click the start button.' : 'Lisa failid üleslaadimise järjekorda ja klõpsa alustamise nupule.',
	'Filename' : '<PERSON><PERSON><PERSON><PERSON>',
	'Status' : 'Ole<PERSON>',
	'Si<PERSON>' : 'Suurus',
	'Add files' : '<PERSON> faile',
	'Stop current upload' : 'Praeguse üleslaadimise peatamine',
	'Start uploading queue' : 'Järjekorras ootavate failide üleslaadimise alustamine',
	'Drag files here.' : 'Lohista failid siia.',
	'Start upload' : '<PERSON>usta üleslaadimist',
	'Uploaded %d/%d files': '<PERSON>les laaditud %d/%d',
	'Stop upload': 'Peata üleslaadimine',
	'Start upload': 'Alusta üleslaadimist',
	'%d files queued': '<PERSON><PERSON>r<PERSON>korras on %d faili',
	'File: %s': 'Fail: %s',
	'Close': 'Sulge',
	'Using runtime: ': 'Kasutatakse varianti: ',
	'File: %f, size: %s, max file size: %m': 'Fail: %f, suurus: %s, suurim failisuurus: %m',
	'Upload element accepts only %d file(s) at a time. Extra files were stripped.': 'Üleslaadimise element saab vastu võtta ainult %d faili ühe korraga. Ülejäänud failid jäetakse laadimata.',
	'Upload URL might be wrong or doesn\'t exist': 'Üleslaadimise URL võib olla vale või seda pole',
	'Error: File too large: ': 'Viga: fail on liiga suur: ',
	'Error: Invalid file extension: ': 'Viga: sobimatu faililaiend: ',
	'File extension error.': 'Faililaiendi viga.',
	'File size error.': 'Failisuuruse viga.',
	'File count error.': 'Failide arvu viga.',
	'Init error.': 'Lähtestamise viga.',
	'HTTP Error.': 'HTTP ühenduse viga.',
	'Security error.': 'Turvaviga.',
	'Generic error.': 'Üldine viga.',
	'IO error.': 'S/V (I/O) viga.'
});