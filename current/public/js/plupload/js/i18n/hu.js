// Hungarian
plupload.addI18n({
    'Select files' : 'Fájlok kiválasztása',
    'Add files to the upload queue and click the start button.' : 'Válaszd ki a fájlokat, majd kattints az Indítás gombra.',
    'Filename' : 'Fájlnév',
    'Status' : 'Állapot',
    'Size' : 'Méret',
    'Add files' : 'Hozzáadás',
    'Stop current upload' : 'Jelenlegi feltöltés megszakítása',
    'Start uploading queue' : 'Várakozási sor feltöltésének indítása',
    'Uploaded %d/%d files': 'Feltöltött fájlok: %d/%d',
    'N/A': 'Nem elérhe<PERSON>',
    'Drag files here.' : 'Húzd ide a fájlokat.',
    'Stop upload': 'Feltöltés megszakítása',
    'Start upload': 'Indítás',
    '%d files queued': '%d fájl sorbaállítva',
    'File: %s': 'Fájl: %s',
    'Close': 'Be<PERSON><PERSON><PERSON><PERSON>',
    'Using runtime: ': 'Használt runtime: ',
    'File: %f, size: %s, max file size: %m': 'Fájl: %f, méret: %s, maximális fájlméret: %m',
    'Upload element accepts only %d file(s) at a time. Extra files were stripped.': 'A feltöltés egyszerre csak %d fájlt fogad el, a többi fájl nem lesz feltöltve.',
    'Upload URL might be wrong or doesn\'t exist': 'A megadott URL hibás vagy nem létezik',
    'Error: File too large: ': 'Hiba: A fájl túl nagy: ',
    'Error: Invalid file extension: ': 'Hiba: Érvénytelen fájlkiterjesztés: ',
    'File extension error.': 'Hibás fájlkiterjesztés.',
    'File size error.': 'Hibás fájlméret.',
    'File count error.': 'A fájlok számával kapcsolatos hiba.',
    'Init error.': 'Init hiba.',
    'HTTP Error.': 'HTTP hiba.',
    'Security error.': 'Biztonsági hiba.',
    'Generic error.': 'Általános hiba.',
    'IO error.': 'I/O hiba.'
});
