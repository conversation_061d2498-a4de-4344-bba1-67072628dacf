
$(function() {
	var audiotypes={
		"mp3": "audio/mpeg",
		"mp4": "audio/mp4",
		"ogg": "audio/ogg",
		"wav": "audio/wav"
	};

	var game = {
		timer: 40, // secs
		timerRunning: false,
		timerFn: function () {
			setInterval(function() {
				if(game.timerRunning !== false && game.win === false) {
					game.timer--;
					$(".countdown").text(("0"+game.timer).slice(-2));

					if(game.timer == 0) {
						game.timerRunning = false;
						ss_soundbits('negative').playclip();
						$('.loose-screen').show();
					}
				}
			}, 1000)
		},
		checkWin: function () {
			var result = true;

			for(var i in game.items) {
				if(game.items[i] === false) {
					result = false;
					break;
				}
			}

			if(result === true) {
				game.win = true;
				setTimeout(function() {
					$(".pic svg").remove();
					setTimeout(function () {
						$('.win-screen').show();
						ss_soundbits('win').playclip();
					}, 1000);
				}, 500);
			}
		},
		items: {
			item1: false,
			item2: false,
			item3: false,
			item4: false
		},
		win: false
	};

	function ss_soundbits(sound, loop){
		sound = '/interactives/dragndrop/sounds/' + sound;

		var audio_element = document.createElement('audio')
		if (audio_element.canPlayType){
			for (var i=0; i == 0 && i<arguments.length; i++){
				for(var key in audiotypes) {
					var source_element = document.createElement('source')
					source_element.setAttribute('src', arguments[i] + "." + key)
					source_element.setAttribute('type', audiotypes[key])
					audio_element.appendChild(source_element);
				}
			}

			//audio_element.play()
			audio_element.playclip=function(){
				this.pause()
				//this.currentTime=0
				this.play()
			}

			if(loop) {
				audio_element.addEventListener('ended', function() {
					ss_soundbits(sound.replace('/interactives/dragndrop/sounds/', ''), loop).playclip();
				}, false);
			}

			return audio_element
		}
	}

	$(document).ready(function () {

		var mask1 = $('#mask1 circle')[0];
		var mask2 = $('#mask2 circle')[0];
		$('.pic').mousemove(function (event) {

			if(!game.win && game.timer > 0) {
				if(game.timerRunning === false) {
					game.timerRunning = true;
					game.timerFn();
				}

				event.preventDefault();
				var upX = Math.round(event.pageX / $('body').css('zoom') - $('.pic').offset().left);
				var upY = Math.round(event.pageY / $('body').css('zoom') - $('.pic').offset().top);
				mask1.setAttribute("cy", (upY - 5) + 'px');
				mask1.setAttribute("cx", (upX) + 'px');
				mask2.setAttribute("cy", (upY - 5) + 'px');
				mask2.setAttribute("cx", (upX) + 'px');
			}

		});

		$('.pic').click(function (event) {

			if(!game.win && game.timer > 0) {
				var posX = Math.round(event.pageX / $('body').css('zoom') - $('.pic').offset().left);
				var posY = Math.round(event.pageY / $('body').css('zoom') - $('.pic').offset().top);

				// console.log([posX, posY]);
				
				if((posX >= 139.5 && posY >= 20) && (posX <= 251.5 && posY <= 213)) {
					if(!game.items.item1) {
						ss_soundbits('win2').playclip();
						game.items.item1 = true;
						$(".item1").addClass("active");
					}
				}

				else if((posX >= 401.5 && posY >= 106) && (posX <= 456.5 && posY <= 268)) {
					if(!game.items.item2) {
						ss_soundbits('win2').playclip();
						game.items.item2 = true;
						$(".item2").addClass("active");
					}
				}

				else if((posX >= 713.5 && posY >= 259) && (posX <= 823.5 && posY <= 316)) {
					if(!game.items.item3) {
						ss_soundbits('win2').playclip();
						game.items.item3 = true;
						$(".item3").addClass("active");
					}
				}
				
				else if((posX >= 222.5 && posY >= 241) && (posX <= 329.5 && posY <= 305)) {
					if(!game.items.item4) {
						ss_soundbits('win2').playclip();
						game.items.item4 = true;
						$(".item4").addClass("active");
					}
				}
				
				else {
					ss_soundbits('negative').playclip();
				}

				game.checkWin();
			}

		});
		
		$('.replay').click(function (e) {
			window.location.reload();
			e.preventDefault();
			e.stopPropagation();
		});
		
		$('.start').click(function (e) {
			e.preventDefault();
			e.stopPropagation();

			$('.start-screen').hide();
		});

	});
});