/*
|--------------------------------------------------------------------------
| App
|--------------------------------------------------------------------------
*/

body {
	background-color: $brand-cream;
}


/*
|--------------------------------------------------------------------------
| Hero
|--------------------------------------------------------------------------
*/

.hero {
	overflow: hidden;

	&.purple {
		background-color: $brand-dark-purple;
		height: 500px;
	}
	&.candle {
		background-color: #404f70;
	}
	&.dashboard {
		background-color: darken($brand-dark-purple, 25%);
	}
	&.professional-development {
		background-color:  #707696;
	}
}

/*
|--------------------------------------------------------------------------
| Homepage
|--------------------------------------------------------------------------
*/

.intro {
	@include clearfix();
	padding-top: 100px;
	.left, .right {
		width: 50%;
		float: left;
		@include box-sizing(border-box);
		padding: 30px;
	}
	.right .image img{
		/*width: 200px;
		height: 144px;
		display: block;
		max-width: 100%;*/

	}

	.hero-img {
		width: 100%;
		height: auto;
	}
	color: $brand-dark-purple;
}

.scroll-down {
	padding: 20px 0;
	text-align: center;
	color: $brand-dark-purple;
	h2 {
		margin: 0;
	}
}

.about-events {

	@include clearfix();

	.left, .right {
		width: 50%;
		float: left;
		@include box-sizing(border-box);
		height: 289px;
	}
	.left {
		background-color: $brand-light-purple;
		border-bottom: solid 40px $brand-dark-purple;
		background-image: url('../image/icons/homepage-bgs-left.png');
		background-repeat: no-repeat;
		background-position: right top;
	}
	.right {
		background-color: #fff;
		border-bottom: solid 40px $brand-dark-purple;
		background-image: url('../image/icons/homepage-bgs-right.png');
		background-repeat: no-repeat;
		background-position: left top;
	}
	.events {
		display: block;
		margin: 0 0 0 60px;
		padding: 0;
		background-color: #fff;
		list-style: none;
		background-image: url('../image/icons/lines-bg.png');
		overflow: auto;
		color: $brand-dark-purple;
		.date {
			text-transform: uppercase;
			margin-right: 10px;
			color: $brand-orange;
		}
		li {
			padding: 25px 0 5px 30px;
		}
		li.first {
			margin-top: 11px;
		}
	}
	.welcome {
		width: 60%;
		float: right;
		padding: 30px;
		text-align: center;
		color: #fff;
		strong {
			display: block;
		}
		h1 {
			margin-bottom: 0;
		}
	}
	hr {
		border: 0;
		background-color: $brand-dark-purple;
		height: 4px;
		width: 75px;
		margin: 0 auto;
	}
}

.features {
	background-color: $brand-light-blue;
	overflow: hidden;
	h2 {
		display: block;
		text-align: center;
		color: #fff;
		margin: 0 0 30px 0;
		font-size: 30px;
		line-height: 36px;
	}
	padding: 50px 0 50px 0;
}

.feature-items {
	.left, .right {
		width: 50%;
		float: left;
		@include box-sizing(border-box);
	}
	.feature {
		font-size: 16px;
		line-height: 16px;
		padding: 17px;
		border-radius: 25px;
		background-color: lighten($brand-light-blue, 5%);
		display: block;
		margin-bottom:15px;
		margin-right: 100px;
		color: #fff;
	}
}


/*
|--------------------------------------------------------------------------
| Dashboard
|--------------------------------------------------------------------------
*/

.dashboard-head {
	@include clearfix();
	div {
		@include box-sizing(border-box);
	}
	.whats-new {
		width: 71.4285%;
		float: left;
		padding: 30px 15px;
		color: $brand-dark-purple;
		.video-col {
			@include make-column(8);
			.video {
				background-color: #333;
				height: 400px;
			}
		}
		.feed-col {
			@include make-column(4);
			.text {
				border-top: solid 3px darken($brand-cream, 10%);
				border-bottom: solid 3px darken($brand-cream, 10%);
				margin-bottom: 40px;
			}
		}
	}
	.user-details {
		width: 28.5714%;
		float: right;
		background-color: $brand-orange;
	}
	.stats {
		margin: 0;
		padding: 30px;
		list-style: none;
		border-bottom: solid 5px #f7bc84;
		li {
			color: #fff;
			padding: 10px 0;
			margin-bottom: 5px;
		}
	}
}



/*
|--------------------------------------------------------------------------
| Filter / Search
|--------------------------------------------------------------------------
*/

.filter-search {
	.filter {
		background-color: $brand-light-blue;
	}
	.search {
		background-color: $brand-dark-purple;
	}
	label {
		padding: 13px 20px 13px 0;
		float: left;
		color: #fff;
	}
	input {
		font-size: 18px;
		color: #fff;
	}
	.search-field {
		border-bottom: solid 3px darken($brand-dark-purple, 10%);
		@include placeholder($color: $brand-light-purple);
	}
	.Choice {
		border:solid 3px $brand-dark-blue;
	}
}



/*
|--------------------------------------------------------------------------
| Books
|--------------------------------------------------------------------------
*/


.books {
	padding: 30px;
	.wrap {
		@include make-row();
	}
	.book-wrap {
		@include make-column(3);
		.book {
			background-color: #E5DBD3;
			border-radius: $brdr-radius;
			padding: 25px;
			text-align: center;
		}
		.title {
			font-size: 24px;
		}
		.description {
			padding: 10px 0;
		}
	}
}


/*
|--------------------------------------------------------------------------
| Prayers Intro
|--------------------------------------------------------------------------
*/

.prayer-intro {
	@include make-row();
	color: #fff;
	.left {
		@include make-column(3);
		@include make-column-offset(3);
	}
	.right {
		@include make-column(3);
	}
	h1 {
		font-size: 40px;
	}
}

.prayer-bar {
	.left {
		background-color: $brand-light-pink;
	}
	.right {
		background-color: $brand-dark-pink;
	}
	.search-field {
		border-bottom: solid 3px darken($brand-dark-pink, 10%);
		@include placeholder($color: $brand-light-pink);
	}
	.title {
		font-size: 18px;
		color: #fff;
		padding: 12px 0;
	}
}

.prayers {
	padding: 30px;
	.wrap {
		@include make-row();
	}
	.prayer-wrap {
		@include make-column(3);
	}
}

.prayers {
	.prayer {
		background-color: #fff;
		border-radius: $brdr-radius;
		overflow: hidden;
		margin-bottom: 30px;
		.prayer-image {
			img {
				width: 100%;
				display: block;
			}
			border-bottom: solid 1px $brand-cream;
		}
		.title {
			color: $brand-light-pink;
			padding:15px;
			h2 {
				margin: 0;
			}
		}
		position: relative;
		.ov {
			display: none;
			opacity: 0.5;
			cursor:pointer;
			position: absolute;
			background-repeat: no-repeat;
			background-position: center center;
			background-image: url('../image/icons/icon_fullscreen.png');
			left: 0;
			top: 0;
			width: 100%;
			height: 100%;
			background-color: $brand-light-pink;
		}
	}
}



/*
|--------------------------------------------------------------------------
| New Dashboard
|--------------------------------------------------------------------------
*/

.book {
	.image {
		height: 400px;
		background-color: #333;
		margin-bottom: 30px;
	}

	.links {
		padding-top: 0;
		padding-bottom: 0;
		margin: 0;
		list-style: none;
		li {
			@include box-sizing(border-box);
			&.read {
				padding-right: 15px;
			}
			&.see {
				padding-left: 15px;
			}
		}
	}
}

.resource-filter-search {

	.left {

		background-color: $brand-light-purple;
	}

	.right {

		background-color: $brand-dark-purple;
	}
	.search-field {
		border: solid 3px transparent;
		border-bottom-color:darken($brand-dark-purple, 10%);
		@include placeholder($color: $brand-light-purple);
		
	}
	.Choice {
		border:solid 3px $brand-dark-purple;
		.ChoiceCurrent{
			background-image: url('../image/icons/icon_select_darkpurple.png');
			background-position: 95% center;
		}
	}
	.filters {
		float: left;
		width: 415px;
		@include box-sizing(border-box);
		margin: 0 0 0 30px;
		padding: 0;
		list-style: none;
		border: solid 3px $brand-dark-purple;
		border-radius: $brdr-radius;
		text-align: center;
		@include clearfix();
		li {
			@include box-sizing(border-box);
			float: none;
			display: inline-block;
			border-right: solid 3px $brand-dark-purple;
			color: #fff;
			&.last {
				border: 0;
			}
		}
		a {
			display: block;
			padding: 10px;
			width: 100px;
			@include box-sizing(border-box);
			color: #fff;
			text-decoration: none;
			text-align: center;
		}
	}
}

.book-resource {
	@include make-row();
	padding-top: 100px;
	padding-bottom: 100px;
	.book {
		@include make-column(6);
		@include make-column-offset(3);
		.info {
			@include make-row();
			.image-wrap {
				@include make-column(12);
			}
			.links {
				@include make-column(12);
				li {
					width: 50%;
					float: left;
				}
			}
		}
	}
}


.resources {
	padding: 30px;
	.wrap {
		@include make-row();
	}
	.resource-wrap {
		@include make-column(3);
	}
}

.resources {
	.resource {
		border-radius: $brdr-radius;
		border-bottom: solid 3px;
		position: relative;
		margin-bottom: 30px;
		overflow: hidden;
		.image {
			// height: 200px;
		}
		.info {
			background-position: 15px center;
			background-repeat: no-repeat;
			margin: 0;
			padding: 0;
			list-style: none;
			padding: 10px 10px 10px 70px;
			.title {
				font-size: 18px;
			}
		}
		a {
			text-decoration: none;
			color: #fff;
		}
		.ov {
			display: none;
			position: absolute;
			left: 0;
			top: 0;
			width: 100%;
			height: 100%;
			background-image: url('../image/icons/icon_fullscreen.png');
			background-repeat: no-repeat;
			background-position: center center;
		}
		&.illustration {
			background-color: $brand-orange;
			border-color: darken($brand-orange, 10%);
			.image { background-color: lighten($brand-orange, 5%); }
			.info { background-image: url('../image/icons/icon_resource_illustration.png'); }
			.ov { background-color: $brand-orange; opacity: 0.5; }
		}
		&.video {
			background-color: $brand-dark-pink;
			border-color: darken($brand-dark-pink, 10%);
			.image { background-color: $brand-light-pink; }
			.info { background-image: url('../image/icons/icon_resource_video.png'); }
			.ov { background-color: $brand-light-pink; opacity: 0.5; }
		}
		&.audio {
			background-color: $brand-dark-pink;
			border-color: darken($brand-dark-pink, 10%);
			.image { background-color: $brand-light-pink; }
			.info { background-image: url('../image/icons/icon_resource_audio.png'); }
			.ov { background-color: $brand-light-pink; opacity: 0.5; }
		}
		&.link {
			background-color: $brand-light-blue;
			border-color: darken($brand-dark-blue, 10%);
			.info { background-image: url('../image/icons/icon_resource_link.png'); }
			.ov { background-color: $brand-light-blue; opacity: 0.5;}
		}
	}
}


/*
|--------------------------------------------------------------------------
| Professional Development
|--------------------------------------------------------------------------
*/

.pro-dev {
	text-align: center;
	padding: 100px 0;
	color: #fff;
}

.filter-pro-dev-shared {
	.left {
		background-color: $brand-light-blue;
	}
	.right {
		background-color: $brand-dark-blue;
	}
	.Choice {
		border:solid 3px $brand-dark-blue;
		margin-right: 30px;
		.ChoiceCurrent{
			background-image: url('../image/icons/icon_select_blue.png');
			background-position: 95% center;
		}
	}
	.search-field {
		border: solid 3px transparent;
		border-bottom-color:darken($brand-dark-blue, 10%);
		@include placeholder($color: $brand-light-blue);
		
	}
}

.pro-dev-shared-resources {
	padding: 30px;
	table {
		width: 100%;
		overflow: hidden;
		border-radius: 3px;
		color: $brand-dark-purple;
		td {
			padding: 10px;
			border-right:solid 1px $brand-cream;
			position: relative;
			&.icon {
				width: 50px;
				border:0;
				background-repeat: no-repeat;
				background-position: center center;
			}
			a.full-col-link {
				width: 100%;
				height: 100%;
				position: absolute;
				top: 0;
				left: 0;
			}
		}
		tr {
			width: 100%;
			margin-bottom: 15px;
			border-radius: 3px;
			overflow: hidden;
			background-color: #fff;
			&.even {
				background-color: #f9f9f9;
			}
		}
		td.tags {
			.tag {
				display: inline-block;
				padding: 5px 10px;
				border: solid 1px $brand-cream;
				border-radius: 20px;
				text-decoration: none;
				margin-right: 10px;
				color: $brand-dark-purple;
			}
		}
        td.tip-title
        {
          width: 20%;
        }
		.pdf {
			background-color: $brand-light-blue;
			background-image: url('../image/icons/icon_shared_pdf.png');
		}
		.video {
			background-color: $brand-light-purple;
			background-image: url('../image/icons/icon_shared_video.png');
		}
		.photo {
			background-color: $brand-dark-blue;
			background-image: url('../image/icons/icon_shared_photo.png');
		}
		.word {
			background-color: $brand-orange;
			background-image: url('../image/icons/icon_shared_word.png');
		}
		.link {
			background-color: $brand-light-pink;
			background-image: url('../image/icons/icon_shared_link.png');
		}

		a {
			color: $brand-dark-purple;
		}
	}
}



/*
|--------------------------------------------------------------------------
| Shared resources Hero
|--------------------------------------------------------------------------
*/

.shared-wrap {
	@include clearfix();
	.left {
		width: 71.4285%;
		float: left;
		h1 {
			display: block;
			text-align: center;
			margin: 100px 0 100px 0;
			color: $brand-dark-blue;
		}
	}
	.right {
		height: 300px;
		width: 28.5714%;
		float: right;
		background-color: #367aa4;
		padding: 30px;
		color: #fff;
		@include box-sizing(border-box);
		p {
			font-size: 18px;
		}
		background-position: right bottom;
		background-repeat: no-repeat;
	}
}


.table-tags {
	@include clearfix();
	.pro-dev-shared-resources {
		@include box-sizing(border-box);
		width: 71.4285%;
		float: left;
	}
	.tags-panel {
		width: 28.5714%;
		float: left;
		background-color: #e6dcd5;
	}
}

.tags-panel {
	height: auto !important;

	.tags-title {
		padding: 30px 30px 15px 30px;
		h1 { margin:  0; color: $brand-dark-purple; }
	}
	.tags-cloud {
		padding: 15px 30px 30px 30px;
	}
	.tag {
		border:solid 2px #b7a7af;
		display: inline-block;
		padding: 10px;
		color: $brand-dark-purple;
		border-radius: $brdr-radius;
		margin-right: 10px;
		text-decoration: none;
		.num {
			display: inline-block;
			border-radius: 100%;
			padding: 5px 7px;
			font-size: 10px;
			background-color: $brand-dark-purple;
			color: #fff;
		}
	}
}


/*
|--------------------------------------------------------------------------
| Slide Modal
|--------------------------------------------------------------------------
*/

.slider-container {
	position: fixed;
	left: 0;
	top: 0;
	width: 100%;
	height: 100%;
	background-color: #397ba2;
	z-index: 999;
}

.slider-container-purple {
	position: fixed;
	left: 0;
	top: 0;
	width: 100%;
	height: 100%;
	background-color: #4f3955;
	z-index: 999;
}

.slider-modal-purple{
	background-color: #4f3955;
	margin: 50px;
	border-radius: 100px 0 100px 0;
	overflow: hidden;
	position: relative;
	.slider {
		background-color: #fff;
		margin: 0;
		padding: 0;
		list-style: none;
		overflow: hidden;
		border-radius: 100px 0 100px 0;
		.slide {
			background-color: #fff;
			background-position: center center;
			background-size: cover;
			background-repeat: no-repeat;
			border-radius: 100px 0 100px 0;
		}
		overflow: hidden;
		ul {
			margin: 0;
			padding: 0 50px 0 15px;
			border-radius: 0 25px 0 0;
			position: absolute;
			bottom: 0;
			left: 0;
			background-color: $brand-light-blue;
			color: #fff;
			list-style: none;
			li {
				display: block;
				float: left;
				padding: 15px 0;
				margin-right: 15px;
			}
		}
		.caption {
			.text {
				padding: 5px;
			}
			a {
				border: solid 2px #fff;
				border-radius: $brdr-radius;
				padding: 5px;
				display: inline-block;
				width: 60px;
				color: #fff;
				text-decoration: none;
				text-align: center;
			}
		}

		.cat {
			position: absolute;
			right: 20px;
			bottom: -30px;
			z-index: 999999;
			cursor: pointer;
		}
	}
}

.slider-modal {
	background-color: #fff;
	margin: 50px;
	border-radius: 100px 0 100px 0;
	overflow: hidden;
	position: relative;
	.slider {
		background-color: #fff;
		margin: 0;
		padding: 0;
		list-style: none;
		overflow: hidden;
		border-radius: 100px 0 100px 0;
		.slide {
			background-color: #fff;
			background-position: center center;
			background-size: cover;
			background-repeat: no-repeat;
			border-radius: 100px 0 100px 0;
		}
		overflow: hidden;
		ul {
			margin: 0;
			padding: 0 50px 0 15px;
			border-radius: 0 25px 0 0;
			position: absolute;
			bottom: 0;
			left: 0;
			background-color: $brand-light-blue;
			color: #fff;
			list-style: none;
			li {
				display: block;
				float: left;
				padding: 15px 0;
				margin-right: 15px;
			}
		}
		.caption {
			.text {
				padding: 5px;
			}
			a {
				border: solid 2px #fff;
				border-radius: $brdr-radius;
				padding: 5px;
				display: inline-block;
				width: 60px;
				color: #fff;
				text-decoration: none;
				text-align: center;
			}
		}

		.cat {
			position: absolute;
			right: 20px;
			bottom: -30px;
			z-index: 999999;
			cursor: pointer;
		}
	}
}





/*
|--------------------------------------------------------------------------
| Prayers Page
|--------------------------------------------------------------------------
*/

.prayer-setup {
	padding-top: 100px;
	padding-bottom: 100px;
	.display {
		position: relative;
		margin-bottom: 60px;
		height: 300px;
		.candle-icon {
			width: 100px;
			height: 300px;
			margin: 0 auto 30px auto;
			background-image: url('../image/icons/candle_body_day1.png');
			background-repeat: no-repeat;
			background-position: bottom center;
			position: relative;
			.flame {
				position: absolute;
				display: none;
				&.flame-center {
					left: 50%;
					margin-left: -11px;
					top: 110px;
					z-index: 999999;
				}
				&.flame-left {
					left: 50%;
					margin-left: -15px;
					top: 115px;
					z-index: 99999;
				}
				&.flame-right {
					left: 50%;
					//margin-left: -15px;
					top: 115px;
					z-index: 9999;
				}
				&.flame-glow {
					left: 50%;
					margin-left: -68px;
					top: 60px;
					z-index: 9999;
				}
			}
		}
	}
	.states {
		width: 500px;
		margin: 0 auto;
		position: relative;
		.steps {
			margin: 0;
			padding: 0;
			list-style: none;
			@include clearfix();
			li {
				width: 33.3333%;
				float: left;
				text-align: center;
				color:#fff;
				line-height: 40px;
			}
			.icon {
				width: 40px;
				height: 40px;
				margin: 0 auto;
				border: solid 2px #c0b3ae;
				border-radius: 100%;
				margin-bottom: 20px;
				color: #c0b3ae;
				@include transition(all 0.2s ease);
				&.active {
					background-color: #c0b3ae;
					color: #856f84;
				}
			}
			.text {
				text-align: center;
			}
		}
		.bar-left {
			background-color: #fff;
			position: absolute;
			left: 115px;
			top: 18px;
			z-index: 1;
			height: 2px;
			width: 100px;
		}
		.bar-right {
			background-color: #fff;
			position: absolute;
			right: 115px;
			top: 18px;
			z-index: 1;
			height: 2px;
			width: 100px;
		}
	}
	.candles-page {
		display: none;
		height: 300px;
	}
}

.prayer-form, .review-window {
	position: absolute;
	width: 300px;
	left: 60%;
	top: 0;
	z-index: 999;

	h2 {
		margin: 0;
	}

	.form-wrap {
		background-color: $brand-orange;
		padding: 20px;
		border-radius: $brdr-radius;
		margin-bottom: 20px;
		position: relative;
		color: black;
		.arrow {
			position: absolute;
			left: -25px;
			top: 50%;
			margin-top: -25px;
			z-index: -999;
		}
		.error {
			padding: 10px;
			color: #fff;
			text-align: center;
			border-radius: $brdr-radius;
			background-color: $brand-light-pink;
		}
	}

	.field {
		padding: 10px;
		border: solid 2px #000000;
		border-radius: $brdr-radius;
		background-color: $brand-orange;
		width: 100%;
		@include box-sizing(border-box);
		margin-bottom: 10px;
		color: #000000;
		@include placeholder($color: #000000);
		&:focus {
			outline: none;
		}
		&.last {
			margin-bottom: 0;
		}


	}

	.parsley-errors-list {
		margin: 0;
		padding: 0;
		list-style: none;
		color: darken($brand-orange, 35%);
		&.filled {
			margin-bottom: 10px;
		}
	}

}

.review-window {
	top: 40px;
	display: none;
	.review-wrap {
		background-color: $brand-orange;
		padding: 20px;
		border-radius: $brdr-radius;
		margin-bottom: 20px;
		color: #000000;
		position: relative;
		h2 {
			padding-bottom: 10px;
			border-bottom: solid 2px #fff;
		}
		.arrow {
			position: absolute;
			left: -25px;
			top: 50%;
			margin-top: -25px;
			z-index: -999;
		}
	}
}

.flame-glow {
	opacity: 0.7;
}


.candles-page {
	background-image: url('../image/icons/candle_bg_blur.png');
	background-repeat: no-repeat;
	background-position: center center;
	background-size: 100%;
	.candle-holder {
		width: 14.2857%;
		float: left;
		background-position: bottom center;
		background-repeat: no-repeat;
		position: relative;
		.flame {
			position: absolute;
			left: 50%;
			margin-left: -11px;
			&.flame-right, &.flame-left {
				opacity: 0.3;
			}
		}
		&.day-0 {
			background-image: url('../image/icons/candle_body_day1.png');
			.flame { 
				top: 110px; 
				&.flame-left {
					margin-left: -15px;
					top: 115px;
					z-index: 9999999;
				}
				&.flame-right {
					top: 115px;
					z-index: 9999999;
					margin-left: 0px;
				}
				&.flame-glow {
					left: 50%;
					margin-left: -68px;
					top: 60px;
					z-index: 9999;
				}
			}
		}
		&.day-1 {
			background-image: url('../image/icons/candle_body_day2.png');
			.flame { 
				top: 130px; 
				&.flame-left {
					margin-left: -15px;
					top: 135px;
					z-index: 999999999;
				}
				&.flame-right {
					top: 135px;
					z-index: 9999999;
					margin-left: 0px;
				}
			}
		}
		&.day-2 {
			background-image: url('../image/icons/candle_body_day3.png');
			.flame { 
				top: 150px; 
				&.flame-left {
					margin-left: -15px;
					top: 155px;
					z-index: 999999999;
				}
				&.flame-right {
					top: 155px;
					z-index: 99999999;
					margin-left: 0px;
				}
			}
		}
		&.day-3 {
			background-image: url('../image/icons/candle_body_day4.png');
			.flame { 
				top: 170px; 
				&.flame-left {
					margin-left: -15px;
					top: 175px;
					z-index: 99999;
				}
				&.flame-right {
					top: 175px;
					z-index: 9999;
					margin-left: 0px;
				}
			}
		}
		&.day-4 {
			background-image: url('../image/icons/candle_body_day5.png');
			.flame {
				top: 190px; 
				&.flame-left {
					margin-left: -15px;
					top: 195px;
					z-index: 99999;
				}
				&.flame-right {
					top: 195px;
					z-index: 9999;
					margin-left: 0px;
				}
			}
		}
		&.day-5 {
			background-image: url('../image/icons/candle_body_day6.png');
			.flame { 
				top: 210px; 
				&.flame-left {
					margin-left: -15px;
					top: 215px;
					z-index: 99999;
				}
				&.flame-right {
					top: 215px;
					z-index: 9999;
					margin-left: 0px;
				}
			}
		}
		&.day-6 {
			background-image: url('../image/icons/candle_body_day7.png');
			.flame { 
				top: 230px; 
				&.flame-left {
					margin-left: -15px;
					top: 235px;
					z-index: 99999;
				}
				&.flame-right {
					top: 235px;
					z-index: 9999;
					margin-left: 0px;
				}
			}
		}
		height: 300px;
		.prayer {
			display: none;
			width: 200px;
			padding: 20px;
			background-color: $brand-orange;
			color: #fff;
			border-radius: $brdr-radius;
			position: absolute;
			left: 80%;
			top: 50px;
			z-index: 9999;
			h2 {
				margin: 0;
				padding-bottom: 10px;
				border-bottom: solid 2px #fff;
			}
			.arrow {
				position: absolute;
				left: -25px;
				top: 50%;
				margin-top: -25px;
				z-index: -999;
			}
		}
	}
}


/*
|--------------------------------------------------------------------------
| Add Resource
|--------------------------------------------------------------------------
*/

.add-resource-window {
	background-color: rgba(0,0,0,0.4);
	position: fixed;
	left: 0;
	top: 0;
	width: 100%;
	height: 100%;
	display: none;
	.modal {
		position: relative;
		color: #fff;
		top: 300px;
		background-color: #367aa4;
		border: solid 1px darken(#367aa4, 10%);
		width: 400px;
		padding: 30px;
		border-radius: $brdr-radius;
		margin: 50px auto 0 auto;
		@include box-shadow(0 0 3px rgba(0,0,0,0.1));
		h2 {
			margin: 0;
			display: block;
			text-align: center;
		}
		.Choice {
			width: 100%;
			float: none;
			.ChoiceCurrent {
				background-color: #fff;
				color: #555;
				border: solid 2px darken(#367aa4, 10%);
				border-radius: $brdr-radius;
				background-image: url('../image/icons/icon_select_blue.png');
				background-repeat: no-repeat;
				background-position: 95% center;
			}
			.ListChoice {
				background-color: #fff;
				color: #555;
				border: solid 2px darken(#367aa4, 10%);
				border-top: 0;
				border-radius: 0 0 $brdr-radius $brdr-radius;
				top:43px;
				left:0;
				width: 100%;
				position: static;
				@include box-sizing(border-box);
			}

		}
		.field-field {
			background-color: darken(#367aa4, 10%);
			padding: 10px;
			border-radius: $brdr-radius;
		}
		.field {
			color: #555;
		}
		.close-form {
			position: absolute;
			top:-10px;
			right: -10px;
			z-index: 999;
			cursor: pointer;
		}
		.submit-wrap {
			margin-top: 20px;
		}
	}
	.parsley-errors-list {
		margin: 0;
		padding: 0;
		list-style: none;
		li {
			padding: 5px 0;
			color: $brand-orange;
		}
	}
}



.prayer-modal {

	h1{
		color: #e86a82;
		font-size: 50px;
	}

	p{
		color: #684e70;
		font-size: 35px;
	}

	.hearts{
		margin-left: auto;
		margin-right: auto;
		display:block;
		width: 300px;
		margin-top: 50px;
	}

	background-image: url('../image/icons/BG_prayer_lightbox.jpg');
	background-color: $brand-cream;
	position: fixed;
	left: 0;
	top: 0;
	width: 100%;
	height: 100%;
	.close-prayer {
		position: absolute;
		top: 30px;
		right: 30px;
		z-index: 9999l;
		border: solid 2px $brand-dark-pink;
		border-radius: $brdr-radius;
		color: $brand-dark-pink;
		padding: 15px 20px;
		display: block;
		font-weight: bold;
		cursor: pointer;
	}
	.prayer {
		background-color: #fff;
		width: 500px;
		margin: 50px auto;
		padding: 50px;
		font-size: 18px;
		text-align: center;
		color: $brand-dark-pink;
		border-radius: $brdr-radius;
		
	}
}



/*
|--------------------------------------------------------------------------
| Song player
|--------------------------------------------------------------------------
*/
.song_player {
	position: relative;
	width: 100%;
	height: 100%;
}

.song_player > audio {
	margin: auto;
    position: absolute;
    top: 0; bottom: 0; left: 0; right: 0;
}

/* ------------------ General UI -------------------- */

body {
	font-family: CambridgeRound-Light, "Helvetica Neue", Helvetica, Arial, sans-serif;
	font-size: 16px;
	line-height: 1.5;
	min-width: 1000px

}

.intro .right .image img {
	/*width: 200px;
    height: 144px;
    display: block;
    max-width: 100%;*/
}

footer {
	background-color: #479dbf !important;
	border-color: #404F70 !important;
}

header .login {
	height: 111px;
}

header .login .error {
	padding-top: 17.5px;
	height: 100%;
}

header .login form {
	height: 75px;
	padding-bottom: 17.5px;
	padding-top: 17.5px;
}

header .login form .field-wrap, header .login form .submit-wrap {
	margin-bottom: 0px !important;
}

header .login form .field-wrap a{
	font-size : 12px;
	line-height : 16px;
	color : #FFFFFF;
}

header .login form .field-wrap .login-field {
	-webkit-transition: all 300ms ease;
	-moz-transition: all 300 m sease;
	-o-transition: all 300ms ease;
	transition: all 300ms ease;
}

header .login form .btn {
	height: 45px;
}

header .login .login-field label {
	height: 41px;
}

.btn {
	text-shadow: none !important;
	-webkit-transition: all 100ms ease;
	-moz-transition: all 100 m sease;
	-o-transition: all 100ms ease;
	transition: all 100ms ease;
}

.btn:hover {
	border-width: 0;
}

header nav li {
	-webkit-transition: all 300ms ease;
	-moz-transition: all 300 m sease;
	-o-transition: all 300ms ease;
	transition: all 300ms ease;
}

header nav li.active {
	border-bottom: 4px solid #bebebe;
}


header nav .active{
	background: #fff
}

.btn.btn-large {
	height: 60px;
}

a, a:link, a:active, a:visited, a:hover {
	text-decoration: none;
	outline: none !important;
	cursor: pointer;
}

header nav ul.authed li.user .user-dropdown {
	padding: 10px 0px 10px 80px !important;
	position: relative;
	height: 40px;
	padding: 10px;
	-webkit-border-radius: 60px;
	-moz-border-radius: 60px;
	border-radius: 60px;
	-webkit-transition: all 300ms ease;
	-moz-transition: all 300 m sease;
	-o-transition: all 300ms ease;
	transition: all 300ms ease;
}

header nav ul.authed li.user .user-dropdown.active {
	-webkit-border-radius: 30px;
	-webkit-border-bottom-right-radius: 0px;
	-webkit-border-bottom-left-radius: 0px;
	-moz-border-radius: 30px;
	-moz-border-radius-bottomright: 0px;
	-moz-border-radius-bottomleft: 0px;
	border-radius: 30px;
	border-bottom-right-radius: 0px;
	border-bottom-left-radius: 0px;
}

header nav ul.authed li.user .user-dropdown:after {
	background: url(../image/icons/user_default_avatar.png) center center no-repeat;
	height: 60px;
	width: 60px;
	content: "";
	position: absolute;
	bottom: 0px;
	left: 0px;
	-webkit-border-radius: 60px;
	-moz-border-radius: 60px;
	border-radius: 60px;
	-webkit-transition: all 300ms ease;
	-moz-transition: all 300 m sease;
	-o-transition: all 300ms ease;
	transition: all 300ms ease;
}

header nav ul.authed li.user .user-dropdown.active:after {
	-webkit-border-radius: 0px;
	-moz-border-radius: 0px;
	border-radius: 0px;
	-webkit-border-radius: 0px;
	-webkit-border-top-left-radius: 30px;
	-moz-border-radius: 0px;
	-moz-border-radius-topleft: 30px;
	border-radius: 0px;
	border-top-left-radius: 30px;
}

header nav ul.authed li ul {
	-webkit-border-radius: 0px;
	-webkit-border-bottom-right-radius: 30px;
	-webkit-border-bottom-left-radius: 30px;
	-moz-border-radius: 0px;
	-moz-border-radius-bottomright: 30px;
	-moz-border-radius-bottomleft: 30px;
	border-radius: 0px;
	border-bottom-right-radius: 30px;
	border-bottom-left-radius: 30px;
	box-shadow: none !important
}

header nav ul.authed li.user .user-dropdown ul {
	opacity: 0;
	-webkit-transition: all 300ms ease;
	-moz-transition: all 300 m sease;
	-o-transition: all 300ms ease;
	transition: all 300ms ease;
}

header nav ul.authed li.user .user-dropdown.active ul {
	opacity: 1;

}

/* ------------------ Homepage ------------------- */

.intro {
	color: #404f70;
	padding-top: 60px;
}

.intro .right p {
	margin: 30px 0px
}

.intro .center {
	width: 100%;
	display: block;
	float: left;
	margin: 0px 0px 40px 0px !important;
}

.intro .center a {
	width: 240px;
	display: block;
	margin: 0 auto;
}

.intro .center p {
	width: 100%;
	display: block;
	text-align: center;
	display: inline-block;
	color: #404F70;
	font-style: normal;
	margin: 0px 0px 7px 0px !important;
}

.intro .center i {
	width: auto;
	display: 100%;
	font-size: 20px;
	color: #404F70;
	font-weight: 700;
	text-align: center;
	display: inline-block;
	font-style: normal;
	margin: 0px 0px 10px 0px !important;
}

.intro .center img {
	width: 15px;
	height: 15px;
	display: block;
	margin: 0 auto;
}

.intro .center .f_btn_down {
	width: 100%;
	display: block;
	text-align: center;
}

.about-events {
	display: table;
	width: 100%;
	border-bottom: 4px solid #438cb2;
	color: #404F70;
}

.about-events .events {
	background-repeat: repeat-x;
	height: 100% !important;
}

.about-events .left {
	background-color: #f9b56e !important;
	border-bottom: 25px solid #F69025 !important;
	background-image: none;
	-webkit-border-radius: 0px;
	-webkit-border-top-right-radius: 25px;
	-moz-border-radius: 0px;
	-moz-border-radius-topright: 25px;
	border-radius: 0px;
	border-top-right-radius: 25px;
	position: relative;
	background-repeat: repeat-y;
	float: none !important;
	height: auto !important;
	display: table-cell !important;
	vertical-align: top !important;
}

.about-events .left:after {
	background: url(../image/icons/homepage-bgs-left_small.png) center center no-repeat;
	height: 25px;
	width: 25px;
	content: "";
	position: absolute;
	bottom: -25px;
	right: 0px;
}

.about-events .right {
	border-bottom: 25px solid #F69025 !important;
	-webkit-border-radius: 0px;
	-webkit-border-top-left-radius: 25px;
	-moz-border-radius: 0px;
	-moz-border-radius-topleft: 25px;
	border-radius: 0px;
	border-top-left-radius: 25px;
	position: relative;
	background-repeat: repeat-y;
	float: none !important;
	height: auto !important;
	display: table-cell !important;
	vertical-align: top !important;
}

.about-events .right:after {
	background: url(../image/icons/homepage-bgs-right_small.png) center center no-repeat;
	height: 25px;
	width: 25px;
	content: "";
	position: absolute;
	bottom: -25px;
	left: 0px;
}

.about-events hr {
	background-color: #F69025;
	border: 0 none;
	height: 2px;
	margin: 0 auto;
	width: 75px;
}

.about-events h1 {
	margin-top: 17px;
}

.about-events p {
	margin: 25px 0px;
}

.about-events .events li.first {
	margin-top: 15px !important;
}

.about-events .events li {
	padding: 23px 0 5px 30px !important;
}

.feature-items .feature {
	padding-left: 65px;
	position: relative;
}

.feature-items .feature:before {
	height: 50px;
	width: 50px;
	content: "";
	display: block;
	background-repeat: no-repeat;
	position: absolute;
	top: 0px;
	left: 0px;

}

.feature-items .left .feature:first-child:before {
	background: url(../image/icons/home_feature_01.png);
}

.feature-items .left .feature:nth-child(2):before {
	background: url(../image/icons/home_feature_02.png);
}

.feature-items .left .feature:nth-child(3):before {
	background: url(../image/icons/home_feature_03.png);
}

.feature-items .right .feature:first-child:before {
	background: url(../image/icons/home_feature_05.png);
}

.feature-items .right .feature:nth-child(2):before {
	background: url(../image/icons/home_feature_04.png);
}

.feature-items .right .feature:nth-child(3):before {
	background: url(../image/icons/home_feature_06.png);
}

/* ------------------ Dashboard ------------------- */

.dashboard-head {
	color: #fff;
	width: 100%;
}

.dashboard-head .user-details h2 {
	color: #fff;
}

.dashboard-head {
	display: table;
	table-layout: fixed;
}

.dashboard-head .user-details ul li p {
	line-height: 40px;
	display: inline-block;
	margin: 0px !important;
}

.dash_candle_icon {
	display: inline-block;
	width: 40px;
	height: 40px;
	margin-right: 15px;
	float: left;
}

.dash_candle_icon.dash_candle_icon_class {
	background: url(../image/icons/user_icon_candleClass.png) center center no-repeat;
}

.dash_candle_icon.dash_candle_icon_school {
	background: url(../image/icons/user_icon_candleSchool.png) center center no-repeat;
}

.dash_candle_icon.dash_candle_icon_world {
	background: url(../image/icons/user_icon_candleWorld.png) center center no-repeat;
}

.dashboard-head .whats-new {
	float: none;
	display: table-cell;
	vertical-align: top;
	table-layout: fixed;
	overflow: hidden;
	padding: 0px !important;
}

.dashboard-head .user-details {
	float: none;
	display: table-cell;
	vertical-align: top;
	table-layout: fixed;
}

.dashboard-head .whats-new .feed-col {
	width: 100%;
}

.dashboard-head .whats-new .video-col {
	width: 50%;
	display: none;
}

.dashboard-head .whats-new .feed-col .text {
	color: #404F70;
}

.dashboard-head .whats-new .feed-col ul {
	list-style: none !important;
	padding: 0;
}

.dashboard-head .whats-new .feed-col ul li {
	margin-bottom: 20px;
	width: 50%;
	float: left;
	display: block;
	height: auto;
}

.dashboard-head .whats-new .feed-col .text {
	overflow: hidden
}

.dashboard-head .whats-new .feed-col .text .d_icon {
	color: #404F70;
	width: 26px;
	height: 26px;
	padding: 7px;
	-webkit-border-radius: 50px;
	-moz-border-radius: 50px;
	border-radius: 50px;
	background-color: #6CC8DD;
	float: left;
}

.dashboard-head .whats-new .feed-col .text .d_icon img {
	display: block;
	width: 25px;
	height: 25px;
}

.dashboard-head .whats-new .feed-col .text i {
	display: block;
	float: left;
	padding: 0px 40px 0px 20px;
	font-style: normal !important;
	width: -moz-calc(100% - 100px);
	width: -webkit-calc(100% - 100px);
	width: calc(100% - 100px);
}

.dashboard-head .user-details {
	background-color: #f0ac65;
	float: none;
	width: 28.5714%;
	height: auto !important;
}

.dashboard-head div {
	box-sizing: border-box;
}


/* ------- ressources box ------- */

.resource-wrap {
	margin-bottom: 30px;
	width: 33% !important;
}


.resource-wrap .image{
	overflow: hidden;
}

.resource-wrap .image img{
	object-fit: cover;
	object-position: center center;
}




@media all and (max-width: 1080px) {
	.resource-wrap {
		margin-bottom: 30px;
		width: 50% !important;
	}
}

/* ------- books on the top of the page ------- */

.dashboard-head .whats-new .book {
	position: relative;
	cursor: pointer;
}

.dashboard-head .whats-new .overlay_box:before {
	height: 100%;
	width: 100%;
	background: rgba(249, 181, 110, 0.8);
	visibility: hidden;
	opacity: 0;
	position: absolute;
	top: 0px;
	left: 0px;
	display: block;
	content: "";
	font-size: 18px;
	color: white;
	line-height: 100%;
	padding: 50% 20px 0px 20px;
	box-sizing: border-box;
	-webkit-transition: all 300ms ease;
	-moz-transition: all 300 m sease;
	-o-transition: all 300ms ease;
	transition: all 300ms ease;
	z-index: 9;
	-webkit-border-radius: 4px;
	-moz-border-radius: 4px;
	border-radius: 4px;
	content: "Choose this Book";
	text-align: center;
	line-height: 100%;
}

.dashboard-head .whats-new .book:hover:after, .dashboard-head .whats-new a:hover:before {
	visibility: visible;
	opacity: 1;

}

.dashboard-head .whats-new .books {
	padding: 40px;
}

.dashboard-head .whats-new .books .book-wrap {
	width: 25% !important;
	margin-bottom: 30px;
}

.dashboard-head .whats-new .books .book-wrap a {
	background: #D3C6C1;
	display: block;
	padding-bottom: 4px;
	-webkit-border-radius: 4px;
	-moz-border-radius: 4px;
	border-radius: 4px;
	position: relative;

}

.dashboard-head .whats-new .books .wrap .book-wrap .book span {
	width: 100%;
	height: 2px;
	background-color: #CCBEB6;
	display: block;
	margin-bottom: 10px;
	position: relative;
	top: -6px;
}

.dashboard-head .whats-new .books .wrap .book-wrap .book img {
	max-width: 80% !important;
	height: 100%;
}

.dashboard-head .whats-new .books .wrap .book-wrap .book_upcoming {
	border: 2px dashed #CCBEB6 !important;
	background: none !important;
	cursor: default !important;
}

.dashboard-head .whats-new .books .wrap .book-wrap .book_upcoming:before, .dashboard-head .whats-new .books .wrap .book-wrap .book_upcoming:after {
	visibility: hidden !important;
	opacity: 0 !important;
	display: none !important;
}

.dashboard-head .whats-new .books .wrap .book-wrap .book_upcoming span {
	opacity: 0;
}

/* ------- welcome text on the bottom of the page ------- */

.welcome_txt {
	width: 100%;
	display: block;
	overflow: hidden;
	background-color: white;
}

.welcome_wrapper {
	width: 100%;
	display: block;
	overflow: hidden;
	padding: 40px;
	box-sizing: border-box;
	background-color: white;
	max-width: 960px;
	margin: 0 auto;
}

.welcome_wrapper h2 {
	text-align: center;
}

.welcome_wrapper .feed-col ul {
	list-style: none !important;
	padding: 0;
}

.welcome_wrapper .feed-col ul li {
	margin-bottom: 20px;
	width: 50%;
	float: left;
	display: block;
	height: auto;
}

.welcome_wrapper .feed-col .text {
	overflow: hidden
}

.welcome_wrapper .feed-col .text .d_icon {
	color: #404F70;
	width: 26px;
	height: 26px;
	padding: 7px;
	-webkit-border-radius: 50px;
	-moz-border-radius: 50px;
	border-radius: 50px;
	background-color: #6CC8DD;
	float: left;
}

.welcome_wrapper .text .d_icon img {
	display: block;
	width: 25px;
	height: 25px;
}

.welcome_wrapper .feed-col .text i {
	display: block;
	float: left;
	padding: 0px 40px 0px 20px;
	font-style: normal !important;
	width: -moz-calc(100% - 100px);
	width: -webkit-calc(100% - 100px);
	width: calc(100% - 100px);
}

/* ------------------- Single Book ------------------- */

.books .wrap .book-wrap .book a img {
	height: 241px;
	width: 196px;
	max-width: 100%;
}

.books .book-wrap .title {
	color: #404F70;
}

.dashboard-head .whats-new .video-col .video {
	background-color: transparent !important;
	height: auto !important;
}

.dashboard-head .whats-new .video-col .video img {
	max-width: 100%;
}

.book .image {
	background-color: transparent !important;
	height: 400px;
	margin-bottom: 30px;
}

.hero.dashboard {
	background-color: #F9B56E !important;
	background-image: url(../image/bg_clouds.png);
	background-position: top;
	background-repeat: no-repeat;
}

.book .image img {
	width: 285px;
}

.book .btn.btn-orange {
	background-color: #f69025 !important;
	border-color: #dc760b !important;
}

.bottom-deco .deco-left, .bottom-deco .deco-right {
	background-color: #F9B56E !important;
}

.theme-filters .filters {
	border: none !important;
	width: 100% !important;
}

.theme-filters .filters ul {

}

.theme-filters .filters li {
	margin-right: 10px !important;
	margin-bottom: 10px !important;
	color: #fff !important;
	border: none !important;
	-webkit-border-radius: 3px;
	-moz-border-radius: 3px;
	border-radius: 3px;
}

.theme-filters .filters li a {
	color: #fff !important;
}

.theme-filters .filters .f_all {
	background-color: #E5DBD3 !important;
}

.theme-filters .filters .f_all a {
	color: #404F70 !important;
}

.theme-filters .filters .f_story {
	background-color: #956F9E !important;
}

.theme-filters .filters .f_slide {
	background-color: #404F70 !important;
}

.theme-filters .filters .f_illu {
	background-color: #6CC8DD !important;
}

.theme-filters .filters .f_video {
	background-color: #F9B56E !important;
}

.theme-filters .filters .f_link {
	background-color: #E96982 !important;
}

.theme-filters .filters .f_interact {
	background-color: #C14669 !important;
}

/* ------------------ the 6 ressources types ------------------- */

/* --------- stories --------- */

.resources .resource.story .info {
	background-color: #AC88B5 !important;
	background-image: url("../image/icons/icon_resource_story.png");
}

.resources .resource.story {
	background-color: #956F9E !important;
	border-color: #956F9E !important;
}

/* --------- slideshow --------- */

.resources .resource.slideshow .info {
	background-color: #404F70 !important;
	background-image: url("../image/icons/icon_resource_slideshow.png");
}

.resources .resource.slideshow {
	background-color: #233253 !important;
	border-color: #233253 !important;
}

/* --------- illustration --------- */

.resources .resource.illustration .info {
	background-color: #6CC8DD !important;
	background-image: url("../image/icons/icon_resource_illustration.png");
}

.resources .resource.illustration {
	background-color: #479DBF !important;
	border-color: #479DBF !important;
}

/* --------- video --------- */

.resources .resource.video .info {
	background-color: #F9B56E !important;
	background-image: url("../image/icons/icon_resource_video.png");
}

.resources .resource.video {
	background-color: #F69025 !important;
	border-color: #F69025 !important;
}

/* --------- weblink --------- */

.resources .resource.link .info {
	background-color: #E96982;
	background-image: url("../image/icons/icon_resource_link.png");
}

.resources .resource.link {
	background-color: #C14669 !important;
	border-color: #C14669 !important;
}

/* --------- interactive --------- */

.resources .resource.interactive .info {
	background-color: #C14669 !important;
	background-image: url("../image/icons/icon_resource_interactive.png");
}

.resources .resource.interactive {
	background-color: #94193c !important;
	border-color: #94193c !important;
}

/* --------- book prayer --------- */

.resources .resource.prayer .info {
	background-color: #8ab654 !important;
	background-image: url("../image/icons/icon_resource_prayer.png");
}

.resources .resource.prayer {
	background-color: #61803b !important;
	border-color: #61803b !important;
}

/* --------- book poem --------- */

.resources .resource.poem .info {
	background-color: #d9c700 !important;
	background-image: url("../image/icons/icon_resource_poem.png");
}

.resources .resource.poem {
	background-color: #968a00 !important;
	border-color: #968a00 !important;
}

/* ------------------ prayer page ------------------- */

.candle .prayer-intro .left {
	background: url(../image/Jesus_prays_02.png);
	display: block;
	height: 400px;
	background-size: contain;
	background-repeat: no-repeat;
	font-family: 'Courier New', Courier, monospace;
}

/* ------------------ ressources page ------------------- */

.resources .image {
	height: 200px
}

.resources .info {
	height: 50px
}

/* ------------------ other  ------------------- */

.landing-link {
	text-decoration: none;
	color: #fff;
}

.hide {
	display: none;
}

.blue-bg {
	background-color: #5E83BA !important;
}

/*
.wrap{
	width: 70%
}*/

.slider-modal .slider .slide {
	background-size: contain !important;
}

/* ------------------ Single blog ------------------- */

.post-wrapper {
	display: block;
}

.post-wrapper .post-title {
	/*    width: 100%;*/
	height: 200px;
	padding: 40px 40px 0px 40px;
	display: block;
	background: url(../image/bg_clouds.png) no-repeat center center #6CC8DD;
	position: relative;
}

.post-wrapper .post-title h1 {
	font-family: CambridgeRound-Semibold;
	font-size: 30px;
	line-height: 30px;
	color: #FFFFFF;
	width: -moz-calc(100% - 80px);
	width: -webkit-calc(100% - 80px);
	width: calc(100% - 80px);
	display: block;
	text-align: center;
	margin-top: 40px;
}

.post-wrapper .post-title .data-wrapper {
	position: absolute;
	bottom: 0px;
	left: 40px;
}

.post-wrapper .post-title .date {
	width: 90px;
	height: 43px;
	padding: 10px;
	background-color: #479DBF;
	-webkit-border-radius: 6px;
	-webkit-border-bottom-right-radius: 0px;
	-webkit-border-bottom-left-radius: 0px;
	-moz-border-radius: 6px;
	-moz-border-radius-bottomright: 0px;
	-moz-border-radius-bottomleft: 0px;
	border-radius: 6px;
	border-bottom-right-radius: 0px;
	border-bottom-left-radius: 0px;
	color: #fff;
	float: left;
}

.post-wrapper .post-title .date .date-day {
	font-size: 40px;
	float: left;
	display: block;
	width: 50%;
	line-height: 42px;
}

.post-wrapper .post-title .date .date-month {
	font-size: 16px;
	letter-spacing: 1px;
	float: left;
	font-family: CambridgeRound-Semibold;
	text-transform: uppercase;
	display: block;
	width: 50%;
}

.post-wrapper .post-title .date .date-year {
	font-size: 12px;
	float: left;
	display: block;
	width: 50%;
	letter-spacing: 1px;
}

.post-wrapper .post-title .navigation {
	height: 40px;
	width: 110px;
	float: left;
	padding: 10px;
}

.post-wrapper .post-title .navigation .nav-prev a, .post-wrapper .post-title .navigation .nav-next a {
	width: 40px;
	height: 40px;
	border: 2px solid rgba(255, 255, 255, 0.5);
	-webkit-border-radius: 6px;
	-moz-border-radius: 6px;
	border-radius: 6px;
	display: block;
	float: left;
	margin-right: 10px;

}

.post-wrapper .post-title .navigation .nav-prev a:hover, .post-wrapper .post-title .navigation .nav-next a:hover {
	background-color: rgba(255, 255, 255, 0.5);
	border-color: transparent;
}

.post-wrapper .post-title .navigation .nav-prev a {
	background: url(../image/icons/icon_previous_w.png) no-repeat center center
}

.post-wrapper .post-title .navigation .nav-next a {
	background: url(../image/icons/icon_next_w.png) no-repeat center center
}

.post-wrapper .post-body {
	display: block;
	overflow: hidden;
}

.post-wrapper .post-body .post-content {
	width: 71.4286%;
	background-color: #FFF;
	font-size: 16px;
	line-height: 24px;
	color: #404F70;
	padding: 40px;
	float: left;
	display: block;
	box-sizing: border-box;
}

.post-content-wrapper {
	max-width: 960px;
	margin: 0 auto;
	display: block;
}

.post-wrapper .post-body .post-content p {
	font-size: 16px;
	line-height: 24px;
	color: #404F70;
}

.post-wrapper .post-body .post-list {
	width: 28.5714%;
	background-color: #F4EEE9;
	padding: 40px;
	float: left;
	display: block;
	box-sizing: border-box;
}

.post-list .post-list-item {
	width: 100%;
	border-bottom: 2px solid #BFB2A9;
	padding: 10px 0px;
	display: block;
	float: left
}

.post-list .post-list-item:last-child {
	border-bottom: none;
}

.post-list .post-list-item .date {
	font-family: CambridgeRound-Semibold;
	text-transform: uppercase;
	font-size: 16px;
	color: #404F70;
	width: 100px;
	float: left;
}

.post-list .post-list-item .title {
	font-size: 14px;
	line-height: 20px;
	color: rgba(64, 79, 112, 1);
	width: 100px;
	float: left;
	width: -moz-calc(100% - 100px);
	width: -webkit-calc(100% - 100px);
	width: calc(100% - 100px);
}

.post-list .post-list-item .title:hover {
	color: rgba(64, 79, 112, 0.7);
}

/* ------- blog - last entries ------- */

.blogentries_wrapper {
	padding: 40px;
	width: 100%;
	display: block;
	overflow: hidden;
}

.blogentries_wrapper .post-list-item {
	width: 100%;
	border-bottom: 2px solid #E59751;
	padding: 10px 0px;
	display: block;
	float: left
}

.blogentries_wrapper .post-list-item .date {
	font-family: CambridgeRound-Semibold;
	text-transform: uppercase;
	font-size: 16px;
	color: #FFFFFF;
	width: 100px;
	float: left;
}

.blogentries_wrapper .post-list-item .title {
	font-size: 14px;
	line-height: 20px;
	color: #fff;
	width: 100px;
	float: left;
	width: -moz-calc(100% - 100px);
	width: -webkit-calc(100% - 100px);
	width: calc(100% - 100px);
}

.blogentries_wrapper .post-list-item .title:hover {
	color: rgba(255, 255, 255, 0.7);
}

/* ------------------ pro dev ------------------- */

.professional-development {
	background: url(../image/bg_shared_resources_page.png) no-repeat right center #737f88 !important
}

.professional-development h1 {
	padding: 0px 80px;
	text-align: left;
}

.shared-resources {

}

.shared-resources .shared-wrap {
	display: table;
	width: 100%;
	vertical-align: top;
}

.shared-resources .shared-wrap .left, .shared-resources .shared-wrap .right {
	float: none !important;
	display: table-cell;
	vertical-align: top;
}

.shared-resources .shared-wrap .left {
	background: url(../image/bg_shared_resources_page_round.png) no-repeat right center #00A9E7 !important;
	background-size: inherit !important;
}

.shared-resources .shared-wrap .left h1 {
	padding: 0px 80px;
	text-align: left;
	color: white;
}

/* --------------------- new nav --------------------- */

/* ------- Timeline ------- */

.states_nav {
	margin: 0 auto;
	padding: 80px 40px 40px 40px;
	box-sizing: border-box;
	position: relative;
	width: 100%;
	background-color: #62bed3;
	overflow: hidden;
}

.states_nav .steps {
	list-style: outside none none;
	margin: 0;
	padding: 0;
}

.states_nav .steps li {
	color: #fff;
	float: left;
	line-height: 40px;
	text-align: center;
	width: 25%;
	position: relative;
}

.states_nav .steps li:after {
	left: calc(50% + 30px);
	background-color: #fff;
	height: 2px;
	position: absolute;
	top: 18px;
	width: calc(100% - 60px);
	z-index: 1;
	content: "";
}

.states_nav .steps li:last-child:after {
	display: none;
}

.states_nav .steps li:hover .icon {
	border: 2px solid #fff !important;
	background-color: #fff !important;
	color: #404f70 !important;
}

.states_nav .steps li a:after, .states_nav .steps li a:before {
	display: none !important;
}

.states_nav .steps li:hover .text {
	color: #fff;
}

.states_nav .steps li .icon {
	border: 2px solid #fff;
	background-color: transparent;
	border-radius: 100%;
	color: #fff;
	height: 40px;
	margin: 0 auto;
	transition: all 0.2s ease 0s;
	width: 40px;
	line-height: 37px;
	font-family: CambridgeRound-Semibold;
}

.states_nav .steps li .active {
	background-color: #fff;
	color: #404f70;
	height: 50px;
	width: 50px;
	position: relative;
	top: -8px;
}

.states_nav .steps li .active  {
	background-color: #fff;
	color: #404f70;
	height: 50px;
	width: 50px;
	position: relative;
	top: -8px;
	line-height: 49px;
	font-size: 20px;
}


.states_nav .steps li .text {
	text-align: center;
	color: #fff;
	font-size: 16px;
}

/* ------- theme box ------- */

.books .theme-wrap {
	margin-bottom: 30px;
	width: 33% !important;
	float: left;
	box-sizing: border-box;
	min-height: 1px;
	padding-left: 15px;
	padding-right: 15px;
}

.books .theme-wrap .overlay_box::before {
	background: none repeat scroll 0 0 rgba(249, 181, 110, 0.8);
	border-radius: 4px;
	box-sizing: border-box;
	color: white;
	content: attr(title);
	display: block;
	font-size: 18px;
	height: 100%;
	left: 0;
	line-height: 100%;
	opacity: 0;
	padding: 65px 20px 0PX;
	position: absolute;
	text-align: center;
	top: 0;
	transition: all 300ms ease 0s;
	visibility: hidden;
	width: 100%;
	z-index: 9;
}
.books .theme-wrap .overlay_lesson::before {
	content: attr(title);
}

.books .theme-wrap a {
	background: none repeat scroll 0 0 #d3c6c1;
	border-radius: 4px;
	display: block;
	padding-bottom: 4px;
	position: relative;
}

.books .theme-wrap .theme {
	cursor: pointer;
	position: relative;
	background-color: #fff;
	border-radius: 4px;
	padding: 25px;
	text-align: center;
	box-sizing: border-box;
}

.books .theme-wrap .theme .icon {
	border: 2px solid #F8B36E;
	background-color: #F8B36E;
	border-radius: 100%;
	color: #D17F42;
	height: 40px;
	margin: 0 auto;
	transition: all 0.2s ease 0s;
	width: 40px;
	line-height: 37px;
	font-family: CambridgeRound-Semibold;
	text-transform: uppercase;
	font-size: 20px;
	margin-bottom: 20px;
}

.books .theme-wrap .lesson .icon {
	color: #F8B36E !important;
	border: 2px solid #F8B36E !important;
	background-color: transparent !important;
}

.books .theme-wrap .theme .title {
	font-size: 20px;
	color: #404F70;
}

.dashboard-head .sidebar_book {
	/*background-color: #f9b56e;*/
	float: none;
	height: auto !important;
	width: 28.5714%;
	display: table-cell;
	table-layout: fixed;
	vertical-align: top;
	box-sizing: border-box;
	padding: 40px;
	background-color: #AB87B4;
}

.dashboard-head.book_3 .sidebar_book {
	background-color: #367AA3;
}

.dashboard-head.book_3 .sidebar_book .book .btn {
	background-color: #79CDE7 !important;
	border-color: #313D5D !important;
}


.dashboard-head .sidebar_book .book {
	background-color: transparent;
	float: none;
	height: auto !important;
	width: 100%;
	display: block;
	table-layout: fixed;
	vertical-align: top;
	box-sizing: border-box;
}

.dashboard-head .sidebar_book .book img {
	box-sizing: border-box;
	padding: 40px 40px 0px 40px;
	max-width: 300px;
	height: auto;
	display: block;
	margin: 0 auto;
}

.dashboard-head .sidebar_book .book span {
	background-color: rgba(52, 48, 46, 0.2);
	display: block;
	height: 2px;
	margin-bottom: 40px;
	position: relative;
	top: px;
	width: 100%;
}

.dashboard-head .sidebar_book .book .btn {
	width: calc(100% - 80px);
	margin: 0px 40px;
	text-align: center;
}

.dashboard-head .sidebar_book .book .btn:hover {
	border-width: 3px;
}

.books .theme-filters .filters {
	margin: 0px !important;
	margin-bottom: 40px !important;
}



/* --------------------- forgot pages --------------------- */

.forgot_container{
	max-width: 440px;
	padding: 40px 20px;
	box-sizing: border-box;
	margin: 0 auto;

}


.forgot_wrapper .forgot_intro{

	margin-bottom: 40px
}



.forgot_wrapper .forgot_intro h1{
	font-size : 30px;
	color : #404F70;
	text-align: center;
	display: block;
	margin-bottom: 20px;
	font-family: CambridgeRound-Semibold;
}


.forgot_wrapper .forgot_intro h2{
	font-size : 20px;
	text-align: center;
	color : #404F70;
}


.forgot_wrapper .forgot_form{
	position: relative;
}

.forgot_wrapper .forgot_form span{
	opacity: 0;
	visibility: hidden;
	position: absolute;
	top: 0px;
	left: 0px;
	display: block;
	padding: 19px;
	-webkit-border-radius: 4px;
	-moz-border-radius: 4px;
	border-radius: 4px;
	background: #479DBF;
	color: #fff;
	height: 68px;
	-webkit-transition: background ease-in-out 0.4s;
	-moz-transition: background ease-out 0.4s;
	-o-transition: background ease-out 0.4s;
	transition: background ease-out 0.4s;
	text-align: center;
}

.forgot_wrapper .forgot_form_success span{
	opacity: 1;
	visibility: visible;
}



.forgot_wrapper .forgot_form input[type="text"], input[type="password"], input[type="email"]{
	-webkit-border-radius: 4px;
	-moz-border-radius: 4px;
	border-radius: 4px;
	padding: 10px;
	font-size : 16px;
	color : #404F70;
	background: transparent;
	border: 2px solid #404F70;
	width: 100%;
	box-sizing: border-box;
}

.forgot_wrapper .forgot_form .btn_sub{
	-webkit-border-radius: 4px;
	-moz-border-radius: 4px;
	border-radius: 4px;
	padding: 10px;
	font-size : 16px;
	color : #fff;
	background: #6CC8DD;
	border: none !important;
	width: 100%;
	margin-top: 20px;
	margin-bottom: 40px;
	-webkit-transition: background ease-in-out 0.4s;
	-moz-transition: background ease-out 0.4s;
	-o-transition: background ease-out 0.4s;
	transition: background ease-out 0.4s;
}

.forgot_wrapper .forgot_form .btn_sub:hover{
	background: #479DBF;
}


.forgot_wrapper .forgot_form a{
	color : #404F70;
	position: relative;
	text-align: center;
	display: block;
	width: 145px;
	margin: 0 auto;
}


.forgot_wrapper .forgot_form a:after{
	background : #404F70;
	position: absolute;
	width: 100%;
	height: 2px;
	display: block;
	content: "";
	top: 25px;
	-webkit-transition: all ease-in-out 0.4s;
	-moz-transition: all ease-out 0.4s;
	-o-transition: all ease-out 0.4s;
	transition: all ease-out 0.4s;


}


.forgot_wrapper .forgot_form a:hover:after{
	background : #F9B46E;
}


.field-wrap-email
{
	margin-bottom: 20px;
}

.view-book-btn-bottom-deco {
	-moz-box-sizing: border-box;
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
	float: left;
	height: 24px;
	margin-top: 25px;
	padding-left: 15px;
	padding-right: 15px;
	width: 100%;
}
.view-book-btn-bottom-deco .deco-left {
	-moz-box-sizing: border-box;
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
	-moz-border-radius: 0 24px 0 0;
	-webkit-border-radius: 0 24px 0 0;
	border-radius: 0 24px 0 0;
	border-right: 2px solid rgba(255, 255, 255, 0.5);
	border-top: 2px solid rgba(255, 255, 255, 0.5);
	float: left;
	height: 100%;
	width: calc(50% + 1px);
}
.view-book-btn-bottom-deco .deco-right {
	-moz-box-sizing: border-box;
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
	-moz-border-radius: 24px 0px 0 0;
	-webkit-border-radius: 24px 0px 0 0;
	border-radius: 24px 0px 0 0;
	border-left: 2px solid rgba(255, 255, 255, 0.5);
	border-top: 2px solid rgba(255, 255, 255, 0.5);
	float: left;
	height: 100%;
	width: calc(50% + 1px);
	margin-left: -2px;
}
.bottom-deco {
	float: left;
	height: 24px;
	margin-bottom: -24px;
	position: relative;
	width: 100%;
}
.bottom-deco .deco-left {
	-moz-box-sizing: border-box;
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
	-moz-border-radius: 0 0px 24px 0;
	-webkit-border-radius: 0 0px 24px 0;
	border-radius: 0 0px 24px 0;
	float: left;
	height: 100%;
	width: 50%;
	background-color: #404f70;
}
.bottom-deco .deco-right {
	-moz-box-sizing: border-box;
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
	-moz-border-radius: 0px 0px 0 24px;
	-webkit-border-radius: 0px 0px 0 24px;
	border-radius: 0px 0px 0 24px;
	float: left;
	height: 100%;
	width: 50%;
	background-color: #404f70;
}

.book-tab-holder a {
	display: inline-block;
	padding: 10px 20px;
	text-align: center;
}
.book-tab-holder a:not(.selected) {
	color: #fff!important;
	border-width: 1px;
	border-color: #404f70;
	border-left-style: solid;
}

.book-tab-holder a:last-child:not(.selected) {
	border-right-style: solid;
}

.book-tab-holder a:first-child{
	border-style: none;
}

.book-tab-holder a.selected {
	color: #404f70;
	background-color: #f3eeea;
	-moz-border-radius: 4px 4px 0 0;
	-webkit-border-radius: 4px 4px 0 0;
	border-radius: 4px 4px 0 0;
}

.book-tab-holder a.selected + a {
	border-left: none;
}

.book-tab-holder a .desc {
	font-size: 0.8em;
	display: inline-block;
}

.theme-search-holder .search-field {
	background-color: #e5dbd2;
	border-color: #404f70;
	border-image: none;
	border-style: none none solid;
	border-width: medium medium 2px;
	padding: 10px 15px;
	padding-right: 50px;
	-moz-border-radius: 0px 4px 4px 0px;
	-webkit-border-radius: 0px 4px 4px 0px;
	border-radius: 0px 4px 4px 0px;
	width: 260px;
}
.theme-search-holder .search-button {
	background-image: url("../image/icons/icon_button_search.png");
	background-color: #404f70;
	background-position: center;
	background-repeat: no-repeat;
	-moz-border-radius: 0px 4px 4px 0px;
	-webkit-border-radius: 0px 4px 4px 0px;
	border-radius: 0px 4px 4px 0px;
	width: 40px;
	padding-bottom: 10px;
	padding-top: 10px;
	display: block;
	float: right;
	position: relative;
	margin-left:-40px;
}
.theme-search-holder {
	float: right;
	margin-right: 15px;
}

.theme-filters {
	float: left;
	width: 100%;
}

.choice-select-lessons {
	border-bottom: 2px solid #ac88b5;
	margin-top: 20px;
	margin-bottom: 20px;
}

a.choice-select-lesson {
	display: inline-block;
	margin-left: 30px;
	background-color: #f3eeea;
	width: 200px;
	text-align: center;
	padding-top: 12px;
	padding-bottom: 10px;
	color: #ac88b5;
}
a.choice-select-lesson.selected {
	border: 2px solid;
	border-bottom: none;
	padding-top: 12px;
	padding-bottom: 12px;
	border-radius: 4px 4px 0px 0px;
	margin-bottom: -2px;
	border-color: #ac88b5;
}
div[data-theme='3'] a.choice-select-lesson.selected {
	border-color: rgb(120, 163, 227)
}

.theme-filters .filters {
	border: 3px solid #956f9e;
	border-radius: 4px;
	box-sizing: border-box;
	float: left;
	list-style: outside none none;
	margin: 0 0 0 30px;
	padding: 0;
	width: 415px;
	-moz-box-sizing: border-box;
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
	text-align: center;
}
.theme-filters .filters li {
	border-right: 3px solid #956f9e;
	box-sizing: border-box;
	color: #fff;
	float: none;
	-moz-box-sizing: border-box;
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
	display: inline-block;
}
.theme-filters .filters li:last-child {
	border: none;
}
.theme-filters .filters {
	list-style: outside none none;
}
.theme-filters .filters a {
	box-sizing: border-box;
	color: #956f9e;
	display: block;
	padding: 10px;
	text-align: center;
	text-decoration: none;
	width: 100px;
	-moz-box-sizing: border-box;
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
}

.theme-filters .filters {
	width: 804px;
}

.candles-page .candle-holder {
	width: 200px;
	margin-left: -38px;
}

.theme-search-holder .search-field {
	height: 42px;
	-moz-box-sizing: border-box;
	-webkit-box-sizing: border-box;
	box-sizing: border-box;
}

.prayer-setup .display .candle-icon { width: 200px; }

.animated-glow {
	position: absolute;
	background-image: url(../image/icons/candle_sprite_01.png);
	width: 100%;
	height: 200px;
	background-position: 0px;
	top: 27px;
}

.day-1 .animated-glow {
	top: 50px;
}
.day-2 .animated-glow {
	top: 72px;
}
.day-3 .animated-glow {
	top: 91px;
}
.day-4 .animated-glow {
	top: 112px;
}
.day-5 .animated-glow {
	top: 131px;
}
.day-6 .animated-glow {
	top: 147px;
}
.candles-page .day-1 .prayer {
	top: 80px;
}
.candles-page .day-2 .prayer {
	top: 108px;
}
.candles-page .day-3 .prayer {
	top: 125px;
}
.candles-page .day-4 .prayer {
	top: 140px;
}
.candles-page .day-5 .prayer {
	top: 165px;
}
.candles-page .day-6 .prayer {
	top: 183px;
}

.hover-element {
	position: absolute;
	width: 100%;
	height: 100px;
	z-index: 500;
}
.day-0 .hover-element {
	top: 65px;
}
.day-1 .hover-element {
	top: 95px;
}
.day-2 .hover-element {
	top: 120px;
}
.day-3 .hover-element {
	top: 140px;
}
.day-4 .hover-element {
	top: 155px;
}
.day-5 .hover-element {
	top: 180px;
}
.day-6 .hover-element {
	top: 195px;
}

.resources .resource.story,
.resources .resource.slideshow{
	background-color: #f9b56e;
	border-color: #f79c3d;
}

.resources .resource.story .ov,
.resources .resource.slideshow .ov{
	background-color: #f9b56e;
	opacity: 0.5;
}

.alert {
	padding: 15px;
	margin-bottom: 20px;
	border: 1px solid transparent;
	border-radius: 4px;
}
.alert h4 {
	margin-top: 0;
	color: inherit;
}
.alert .alert-link {
	font-weight: bold;
}
.alert > p,
.alert > ul {
	margin-bottom: 0;
}
.alert > p + p {
	margin-top: 5px;
}
.alert-dismissable,
.alert-dismissible {
	padding-right: 35px;
}
.alert-dismissable .close,
.alert-dismissible .close {
	position: relative;
	top: -2px;
	right: -21px;
	color: inherit;
}
.alert-success {
	color: #3c763d;
	background-color: #dff0d8;
	border-color: #d6e9c6;
}
.alert-success hr {
	border-top-color: #c9e2b3;
}
.alert-success .alert-link {
	color: #2b542c;
}
.alert-info {
	color: #31708f;
	background-color: #d9edf7;
	border-color: #bce8f1;
}
.alert-info hr {
	border-top-color: #a6e1ec;
}
.alert-info .alert-link {
	color: #245269;
}
.alert-warning {
	color: #8a6d3b;
	background-color: #fcf8e3;
	border-color: #faebcc;
}
.alert-warning hr {
	border-top-color: #f7e1b5;
}
.alert-warning .alert-link {
	color: #66512c;
}
.alert-danger {
	color: #a94442;
	background-color: #f2dede;
	border-color: #ebccd1;
}
.alert-danger hr {
	border-top-color: #e4b9c0;
}
.alert-danger .alert-link {
	color: #843534;
}

/* langiage-switcher */

.language-switcher {
	float: right;
	height: 111px;
	width: 111px;
	background: #404f70;
}

.language-switcher-logged {
	float: right;
	margin:0 auto;
	width: 60px;
	height: 60px;
	margin-top: 5px;
	background: #404f70;
	border-radius: 60px;
	font-size: 0.75em;

	a {
		padding: 0;
		line-height: 60px;
	}
}

.language-switcher a {
	line-height: 111px;
}

.language-switcher a,
.language-switcher-logged a {
	display: block;
	text-align: center;
	color: white;
	text-transform: uppercase;
	font-weight: 900;
}

header .login .login_form {
	width: 50%;
	float: right;
}

header .login .login_form form {
	width: calc(100% - 111px);
}

header nav ul.authed li.user .user-nav-container {
	float: left;
	width: calc(100% - 80px);
}

#container.gil-viewer{
	position: relative;
	min-height: 800px;
}