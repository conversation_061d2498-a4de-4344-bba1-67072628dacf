body {
	background: #165a7f;
}

.content,
.content:before,
.content:after {
	background: #367aa4;
}

.content:before {
	background: #266a91;
}

.content h1 {
	font-weight: 100;
	font-size: 2.6em;
	margin: 0;
}

.content h1,
.content p {
	color: white;
}

.cancelBtnHolder .big-btn {
	background-image: url('../img/close_light.png');
}

.big-btn {
	background-color: #f9b46e;
	border-bottom: solid 3px #e59751;
}

.content-holder {
	padding: 0;
	text-align: center;
	width: 100%;
	margin: 0 auto;
}
.content-holder:after {
	content:"";
	display:table;
	clear:both;
}

.image-holder {
	position: absolute;
	top: 60px;
	left: 0;
	right: 0;
	margin: 0 auto;
	padding: 0 30px;
	z-index: 2;
}

.image-holder-wrapper {
	position: relative;
}

.pic {
	text-align: center;
	position: relative;
	height: 486px;
	background: url("../img/stories/pic.png") no-repeat;
}

.status-holder {
	position: absolute;
	bottom: 0px;
	left: 0;
	right: 0;
	margin: 0 auto;
	background: #266a91;
	padding-top: 100px;
	z-index: 1;
}

.status-holder .items {
	display: inline-block;
}

.status-holder .items .status {
	width: 41px;
	height: 41px;
	margin: 0 auto;
	margin-top: 10px;
	background: url("../img/stories/sprite.png") no-repeat;
	background-position: 0 0;
}

.status-holder .items.active .status {
	background-position: -41px 0;
}

.status-holder img {
	width: 125px;
	vertical-align: bottom;
}

/****************************/
/* Win, loose, start screen */
/****************************/

.question-screen,
.win-screen,
.loose-screen,
.start-screen {
	display: none;
	z-index: 5;
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	overflow: hidden;
	background: rgba(0,0,0,0.5);
}

.start-screen {
	display: block;
}

.question-screen .container,
.win-screen .container,
.loose-screen .container,
.start-screen .container {
	position: absolute;
	background: white;
	border-radius: 10px;
	width: 550px;
	height: 600px;
	top: 50%;
	margin-top: -320px;
	left: 50%;
	margin-left: -275px;
	padding: 30px 60px;
}

.question-screen .container {
	height: 250px;
}

.win-screen .container,
.loose-screen .container {
	padding-top: 60px;
}

.start-screen .container {
	height: 550px;
	margin-top: -325px;
}

.start-screen .container h3 {
	margin-bottom: 30px;
}

.start-screen .container .col1,
.start-screen .container .col2 {
	width: 50%;
	margin: 0 auto;
	float: left;
}

.start-screen .container .row1 .col1 img {
	margin-top: 20px;
	/*	border-radius: 50%;*/
}

.start-screen .container .row2 img {
	height: 120px;
	/*	border-radius: 50%;*/
}

.start-screen .container .row2 .col1 {

	border-right: 2px solid #d5dae0;

}

.start-screen .container .col2 .up,
.start-screen .container .col2 .left,
.start-screen .container .col2 .down,
.start-screen .container .col2 .right {
	position: relative;
	width: 55px;
	height: 55px;
	background: #e0d5d1;
	margin: 0 auto;
	padding: 0;
	border-radius: 5px;
	padding-top: 15px;
}

.start-screen .container .col2 .up {
	margin-top: 10px;
}

.start-screen .container .col2 .down,
.start-screen .container .col2 .left,
.start-screen .container .col2 .right {
	float: left;
	margin-top: 6px;
	margin-right: 6px;
}

.start-screen .container .col2 .left {
	margin-left: 19px;
}

.win-screen .container h2,
.loose-screen .container h2 {
	padding: 50px 0 40px 0;
	font-size: 42px;
}

.win-screen .container a,
.loose-screen .container a,
.start-screen .container a {
	width: 100%;
}

.container .win,
.container .loose {
	margin: 0 auto;
	border-radius: 50%;
	width: 180px;
	height: 180px;
	margin-top: 30px;
	margin-bottom: 30px;
}

.container .win {
	background: #79cee8;
}

.container .loose {
	background: #ce4e6d;
}

.start-screen .container .win,
.start-screen .container .loose {
	width: 120px;
	height: 120px;
}

.start-screen .container .win {
	background: #f9b46e;
}

.start-screen .container .divider {
	height: 2px;
	background: #d5dae0;
	margin: 40px 0;
}

.start-screen .container .win img {
	margin-top: 25px;
}

.start-screen .container .loose img {
	margin-top: 35px;
}

.win-screen .container .win img {
	margin-top: 50px;
}

.loose-screen .container .loose img {
	margin-top: 65px;	
}
