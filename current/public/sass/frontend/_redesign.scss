$white: #fff;
$beige: #f2ede8;
$blue: #44c8f5;
$dark-blue: #007db6;
$grey: #6d6e71;
$light-grey: #e6e6e6;
$purple: #8d73a6;
$light-purple: #ac88b5;
$orange: #f15a22;
$pink: #f173ac;
$green: #72bf44;
$gold: #ffa84a;

$border-radius: 4px;

body{
	-webkit-font-smoothing: antialiased;
	text-rendering: optimizeLegibility;
	min-width: 0px;
}

/*
|--------------------------------------------------------------------------
| Footer
|--------------------------------------------------------------------------
*/

footer {
	color: #fff;

	.container{
		background-color: $dark-blue;
		width: 100%;
		text-align: center;
	}

	.footer-top{
		height: 100px;
		border-bottom: 1px solid $light-grey;

		.useful-links {
			font-family: 'CambridgeRound-Regular', "Helvetica Neue", Helvetica, Arial, sans-serif;
			font-size: 16px;
			line-height: 22px;
			letter-spacing: 0.020em;
			margin: 0;
			padding: 0;
			list-style: none;
			position: relative;
			top: 30px;

			li {
				display: inline-block;
				margin: 10px;
				border-bottom: solid 2px transparent;
				cursor: pointer;
			}
			li::after {
				content: "";
				border: 2px solid;
				border-width: 0 0 0 2px;
				position: relative;
				left: 12px;
			}
			li:last-child::after {
				content: none;
			}
			li:hover{
				border-bottom: solid 2px $white;
				padding-bottom: 3px;
			}
			a  {
				color: #fff;
				text-decoration: none;
			}
		}
	}

	.footer-bottom{
		padding-top: 12px;
		height: 37px;

		.small-print{
			font-family: 'CambridgeRound-Light', "Helvetica Neue", Helvetica, Arial, sans-serif;
			font-size: 14px;
			line-height: 20px;
			letter-spacing: 0.020em;
			a {
				color: $white;
				text-decoration: none;
			}
		}

	}
}

@media( min-width: 980px ) {
	footer.copyright {
	}
	footer.copyright, .page-wrap:after {
		height: 100px;
	}
	.page-wrap {
		min-height: 100%;
		/* equal to footer height */
		margin-bottom: -100px;
	}

	.page-wrap:after {
		content: "";
		display: block;
	}
}

/*
|--------------------------------------------------------------------------
| Header
|--------------------------------------------------------------------------
*/

.login-wrap {
	@include clearfix();
	position: relative;
	background-color: $blue;
	font-size: 16px;
	height: 79px;
	color: $white;
	z-index: 3;
	.follow-us {
		@include clearfix();
		width: 25%;
		float: left;
		padding: 0 0 0 25px;
		line-height: 77px;
		font-weight: 900;
		letter-spacing: 1.3px;
		i.fa{
			position: relative;
			left: 3px;

		}
		a {
			color: $white !important;
			text-decoration: none;
			&:hover{
				opacity: 0.85;
			}
		}
	}

	.login {
		@include clearfix();
		width: 50%;
		float: right;
		padding: 0 0 0 5px;
		height: auto;
		background-color: transparent;
		button {
			margin-right: 25px;
			margin-top: 17px;
			float: right;
			width: 160px !important;
		}
		form {
			float: right;
			width: calc(100% - 97px);
		}
		.btn{
			padding: 0;
		}
	}

	.language-switcher {
		margin-left: 10px;
		float: right;
		height: 79px;
		width: 97px;
		background: $dark-blue;
		a{
			text-transform: none;
			line-height: 79px;
			font-weight: 900;
		}
	}

	.field-wrap-login {
		width: 33.3333%;
		float: left;
		padding: 15px 10px;
		margin: 0;
		color: #ffffff;
	}

	.field {
		background-repeat: no-repeat;
		background-position: 0 center;
	}

	.username {
		background-image: url('../image/icons/icon_input_login_blue.png');
	}

	.password {
		background-image: url('../image/icons/icon_input_password_blue.png');
	}

	.member-notice {
		position: absolute;
		top: 50%;
		line-height: 30px;
		margin-top: -15px;
		color: #fff;
		padding-right: 30px;
		padding-left: 10px;
	}

}

nav {
	background-color: $white !important;
	color: $light-grey;
	font-size: 16px;
	box-shadow: 0 3px 3px 0;
	height: 100px;
	position: relative;
	margin-bottom: 4px;
	z-index: 2;

	a{
		color: $grey;
		padding: 0;
	}

	select::-ms-expand {
		display: none;
	}

	ul {
		@include clearfix();
		padding: 0;
		margin: 0;
		list-style: none;
	}

	li {
		width: auto !important;
		position: relative;
		height: 100%;
		float: left;
		text-align: center;
		border-bottom: solid 4px $white !important;
		border-right: none !important;
		&:hover, &.current-menu-item{
			border-bottom: 4px solid $orange !important;
		}
		a{
			padding: 0 5px 0 5px;
		}
	}

	.logo-wrap {
		background-color: #fff;
		padding-top: 3px;
		a{
			float: left;
			position: relative;
			left: 20px;
			top: 1px;
		}
	}

	.menu-wrap{
		float: right;
		height: 100%;
		color: $grey;
		font-weight: normal;
		margin-right: 105px;
		ul{
			height: 100%;
			li {
				line-height: 100px;
				vertical-align: middle;
				margin-right: 15px;
				margin-left: 15px;
				cursor: pointer;
				a{
					padding: 0 5px;
					font-size: 16px;
					font-weight: 900;
				}
			}
		}
	}

	a {
		text-decoration: none;
		background-repeat: no-repeat;
		background-position: center 15px;
	}

	.logo-wrap a { padding: 15px }

	.arrow {
		position: absolute;
		left: 0;
		bottom: -11px;
		z-index: 10;
		width: 100%;
		height: 8px;
		background-repeat: no-repeat;
		background-position: center center;
		display: none;
	}
}


#user-menu{
	z-index: 100;
	display: inline-block;
	float: right;
	width: 25%;
	margin-top: 11px;
	padding: 0px;
	font-size: 17px;
	list-style-type: none;
	margin-right: 20px;
	a{
		user-select: none !important;
	}
	strong{
		display: block;
	}
	.user-nav-container{
		cursor: pointer;
		background-color: $white;
		-webkit-border-radius: 30px;
		-moz-border-radius: 30px;
		border-radius: 30px;
		-webkit-transition: all 300ms ease;
		-moz-transition: all 300ms ease;
		-o-transition: all 300ms ease;
		transition: all 300ms ease;
		i{
			float: right;
			position: relative;
			right: 30px;
			font-size: 40px;
			color: #007db6;
			top: 10px;
		}
		img{
			float: left;
			height: 58px;
			position: relative;
			right: 7px;
			-khtml-user-select: none;
			-o-user-select: none;
			-moz-user-select: none;
			-webkit-user-select: none;
			user-select: none;
			-webkit-border-radius: 30px;
			-moz-border-radius: 30px;
			border-radius: 30px;
			-webkit-transition: all 300ms ease;
			-moz-transition: all 300ms ease;
			-o-transition: all 300ms ease;
			transition: all 300ms ease;
		}
		&.active, &.active img{
			-webkit-transition: all 300ms ease;
			-moz-transition: all 300ms ease;
			-o-transition: all 300ms ease;
			transition: all 300ms ease;
			-webkit-border-radius: 30px;
			-webkit-border-bottom-right-radius: 0px;
			-webkit-border-bottom-left-radius: 0px;
			-moz-border-radius: 30px;
			-moz-border-radius-bottomright: 0px;
			-moz-border-radius-bottomleft: 0px;
			border-radius: 30px;
			border-bottom-right-radius: 0px;
			border-bottom-left-radius: 0px;
		}
		&.active img{
			-webkit-border-top-right-radius: 0px;
			-moz-border-radius-topright: 0px;
			border-top-right-radius: 0px;
		}
		height: 58px;
		a{
			user-select: none !important;
			color: $dark-blue;
			text-decoration: none;
			&.user-dropdown{
				position: relative;
				left: 20px;
				top: 6px;
			}
		}
		ul{
			margin-left: 0;
			position: relative;
			bottom: 8px;
			padding: 0;
			list-style-type: none;
			width: calc(100% + 8px);
			right: 8px;
			li{
				background-color: $dark-blue;
				cursor: pointer;
				text-indent: 25px;
				padding: 0;
				height: 60px;
				line-height: 59px;
				&:hover{
					background-color: $blue;
				}
				a{
					color: $white;
					display: block;
					height: 100%;
				}
				&:last-child{
					-webkit-border-bottom-right-radius: 30px;
					-webkit-border-bottom-left-radius: 30px;
					-moz-border-radius-bottomright: 30px;
					-moz-border-radius-bottomleft: 30px;
					border-bottom-right-radius: 30px;
					border-bottom-left-radius: 30px;
				}
			}
		}
	}
}


/*
|--------------------------------------------------------------------------
| Common
|--------------------------------------------------------------------------
*/

body{
	background-color: $white;
}


/*
|--------------------------------------------------------------------------
| Registration
|--------------------------------------------------------------------------
*/

/*
.register-heading, .form-footer{
	background-color: $white;

	h1{
		color: $dark-blue;
	}
}
*/

/*
|--------------------------------------------------------------------------
| Headings
|--------------------------------------------------------------------------
*/

h1 {
	font-family: 'CambridgeRound-Semibold', "Helvetica Neue", Helvetica, Arial, sans-serif !important;
	font-size: 40px !important;
	line-height: 50px !important;
	letter-spacing: 0.030em !important;
}

h2 {
	font-family: 'CambridgeRound-Semibold', "Helvetica Neue", Helvetica, Arial, sans-serif !important;
	font-size: 32px !important;
	line-height: 42px !important;
	letter-spacing: 0.050em !important;
}

h3 {
	font-family: 'CambridgeRound-Regular', "Helvetica Neue", Helvetica, Arial, sans-serif !important;
	font-size: 18px !important;
	line-height: 26px !important;
	letter-spacing: 0.020em !important;
}

h4 {
	font-family: 'CambridgeRound-Regular', "Helvetica Neue", Helvetica, Arial, sans-serif !important;
	font-size: 16px !important;
	line-height: 22px !important;
	letter-spacing: 0.020em !important;
}

/*
|--------------------------------------------------------------------------
| Links
|--------------------------------------------------------------------------
*/


a {
	color: $grey;
}

.row {
	@include make-row();
}


/*
|--------------------------------------------------------------------------
| Container
|--------------------------------------------------------------------------
*/

.container {
	@include clearfix();
}

/*
|--------------------------------------------------------------------------
| Forms
|--------------------------------------------------------------------------
*/


.field-wrap {
	@include clearfix();
	margin-bottom: 20px;
	position: relative;
	overflow: hidden;
	p {
		font-size: 12px;
		line-height: 20px;
		color: $dark-blue;
	}
}

.field-wrap label {
	width: auto;
	color: #404f70;
	position: absolute;
	left: 0;
	top: -1px;
	padding: 10px 11px;
	border-right: solid 2px #404f70;
	opacity: 0;
	@include transition(all 0.2s ease);
	&.focused {
		opacity: 1;
		border-right-color: $blue;
	}
}

.ninja-forms-all-fields-wrap label {
	width: auto;
}

.ie-lt10 .field-wrap label {
	position: static;
	opacity: 1;
	display: block;
	padding: 10px 0;
	border: 0;
}

.field {
	width: 100%;
	padding: 10px;
	background-color: transparent;
	border: solid 2px #404f70;
	border-radius: $border-radius;
	color: #404f70;
	@include placeholder($color: #404f70);
	@include transition(all 0.2s ease);

	&:focus {
		outline: none;
	}

	&.field-login {
		border-color: $blue;
		@include placeholder($color: $blue);
		padding-left: 50px;
	}

}

.textarea {
	width: 100%;
	height: 250px;
	padding: 10px;
	background-color: transparent;
	border: solid 2px #404f70;
	border-radius: $border-radius;
	color: #404f70;
}


.form-section {
	padding: 50px 0;
	border-bottom: solid 1px #ddd;
	&.last {
		border-bottom: 0;
	}
}

.form-heading {
	font-family: 'CambridgeRound-semibold', "Helvetica Neue", Helvetica, Arial, sans-serif;
	@include clearfix();
	margin-bottom: 20px;
	.title {
		font-size: 24px;
		line-height: 50px;
		color: $blue;
	}
	.num {
		width: 50px;
		height: 50px;
		font-size: 36px;
		line-height: 50px;
		color: #fff;
		float: left;
		border-radius: 100%;
		text-align: center;
		background-color: $blue;
		margin-right: 20px;
	}
}

.check {
	@include clearfix();
	line-height: 20px;
	margin-bottom: 20px;
	.box {
		cursor: pointer;
		position: relative;
		float: left;
		width: 20px;
		height: 20px;
		border: solid 2px $dark-blue;
		margin-right: 20px;
		border-radius: $border-radius;
		img { display: none; }
		&.checked img {
			display: block;
			position: absolute;
			left: 3px;
			top: -3px;
			z-index: 999;
		}
	}
	input {
		display: none;
	}
}

.submit-wrap {
	text-align: center;
	margin-top: 40px;
}


/*
|--------------------------------------------------------------------------
| Buttons
|--------------------------------------------------------------------------
*/


.btn {
	display: inline-block;
	border: 1px solid;
	background-color: transparent;
	border-radius: $border-radius;
	padding: 12px;
	font-size: 16px;
	text-decoration: none;
	color: $white;
	height: 47px;
	font-weight: normal;

	&:hover{
		border-width: 1px;
	}

	&.btn-block {
		width: 100%;
	}

	&.btn-wide {
		width: 200px;
	}

	&.btn-large {
		padding: 15px 40px;
		font-size: 22px;
	}

	&.btn-gold {
		background-color: $gold;
		border-color: $gold;
	}
	&.btn-gold:hover {
		color: $gold;
		background-color: transparent;
	}
	&.btn-gold.btn-header:hover {
		color: $white;
		background-color: $gold;
	}

	&.btn-white {
		border-color: $white;
		background-color: $white;
		color: $grey;
	}
	&.btn-white:hover {
		color: $grey;
	}

	&.btn-white-inv, &.btn-purple-inv{
		border-color: $white;
		background-color: transparent;
		color: $white;
	}
	&.btn-white-inv:hover {
		background-color: $white;
		color: $dark-blue;
	}
	&.btn-purple-inv:hover {
		background-color: $white;
		color: $purple;
	}

	&.btn-blue {
		background-color: transparent;
		color: $dark-blue;
	}
	&.btn-blue:hover {
		background-color: $dark-blue;
		color: $white;
	}

	&.btn-header:hover {
		opacity: 0.85 !important;
	}
}

/*
|--------------------------------------------------------------------------
| Horizontal Rule
|--------------------------------------------------------------------------
*/

hr {
	border: 0;
	height: 3px;
	&.short {
		width: 165px;
	}
	&.small {
		margin: 15px auto;
	}
	&.gold {
		background-color: $gold;
		margin: 5px auto;
	}
}

@media( min-width: 980px ) {
	hr {
		margin: 30px 0;
	}
}


/*
|--------------------------------------------------------------------------
| Common Elements
|--------------------------------------------------------------------------
*/

blockquote {
	padding-left: 20px;
	font-style: italic;
	border-left: solid 2px $purple;
	p:before {
		content: open-quote;
		color: $purple;
	}
	p:after {
		content: close-quote;
		color: $purple;
	}
}

.text-spacer{
	margin: 0 auto 52px;
	height: 2px;
	width: 80px;
	background-color: $white;
}

.form-header{
	margin: 100px auto 0 auto;
	color: $dark-blue;
	h1{
		margin: 0 0 20px 0;
		text-align: center;
	}
	.text-spacer{
		margin-bottom: 40px;
		background-color: $dark-blue;
	}
}
.form-wrap{
	display: table;
	margin: 0 auto;
	color: $grey;
	width: 400px;
	font-size: 18px;
	font-weight: 900;
	.form-prompt{
		text-align: center;
		margin-bottom: 60px;
		max-width: 480px;
	}
	.form-input{
		width: 400px;
		margin: 0 auto;
		input{
			width: calc(100% - 26px);
			margin: 7px 0 13px 0;
			padding: 13px;
			border-radius: 8px;
			border: 1px solid #007db6;
			font-size: 17px;
			box-sizing: inherit;
			font-weight: normal;
		}
	}
	.btn{
		margin: 27px 0 20px 0;
	}
	.form-sect{
		padding: 0;
	}
	.form-link{
		text-align: center;
		margin: 5px 0 5px 0;
		a:hover{
			color: $dark-blue;
		}
	}

	.form-check {
		line-height: 20px;
		margin-bottom: 70px
	}

	.form-check .box {
		cursor: pointer;
		position: relative;
		float: left;
		width: 20px;
		height: 20px;
		border: 2px solid #479dc0;
		margin-right: 20px;
		border-radius: 4px
	}

	.form-check .box img {
		display: none
	}

	.form-check .box.checked img {
		display: block;
		position: absolute;
		left: 3px;
		top: -3px;
		z-index: 999
	}

	.form-check input {
		display: none
	}
}
.form-foot{
	margin-bottom: 130px;
}

.hero {
	width: 100%;
}

.prayer-bar{
	.right{
		height: 81px;
	}
}

.task-bar{
	.right{
		height: 80px;
	}
}