/* v 3.17
author http://codecanyon.net/user/creativeinteractivemedia/portfolio?ref=creativeinteractivemedia
*/

var FLIPBOOK=FLIPBOOK||{};FLIPBOOK.Book3=function(t,e,i){this.options=i,this.model=e,this.singlePage=i.singlePageMode,this.pageWidth=this.options.pageWidth,this.pageHeight=this.options.pageHeight,this.wrapper=t,this.$wrapper=jQuery(this.wrapper).addClass("flipbook-book3"),this.$bookLayer=this.$wrapper.parent(),this.bookLayer=this.$bookLayer[0],this.flipEasing="easeInOutCubic",this.flipEasing="easeOutSine",this.flipEasing="easeOutQuad",this.scroll=new FLIPBOOK.IScroll(this.bookLayer,{zoom:!0,scrollX:!0,scrollY:!0,scrollbars:!0,keepInCenterV:!0,keepInCenterH:!0,freeScroll:!0,preventDefault:!1});var o=this;e.on("toolMove",function(){o.updateTool()}),e.on("toolSelect",function(){o.updateTool()}),this.scroll.on("zoomEnd",function(){var t;isNaN(o.scroll.scale)?o.zoomTo(i.zoomMin):(i.main.onZoom(o.scroll.scale/o.ratio),t=i.main.zoom,o.scroll.options.eventPassthrough=1<t?"":"vertical",o.scroll.options.freeScroll=1<t,o.scroll.refresh())}),this.scroll.on("zoomStart",function(){});var s=this.wrapper.style;s.width=String(2*this.pageWidth)+"px",s.height=String(this.pageHeight)+"px";var n,a=3*this.pageHeight+"px";this.$centerContainer=jQuery("<div>").appendTo(this.$wrapper).addClass("flipbook-center-container3"),this.$centerContainer.css({"-webkit-perspective":a,perspective:a}),this.centerContainerStyle=this.$centerContainer[0].style,this.centerContainerStyle.width=2*this.pageWidth+"px",this.centerContainerStyle.height=this.pageHeight+"px",this.pagesArr=[],this.animating=!1,this.setRightIndex(i.rightToLeft?i.pages.length:0),o.updateFlipped(),this.numPages=i.pages.length;for(var h=0;h<i.numPages;h++)(n=new FLIPBOOK.Page3(this,h,i.pages[h].src,i.pages[h].htmlContent)).hideHtml(),this.pagesArr.push(n),this.$centerContainer.append(n.$wrapper),this.flippedright++;this.pagesArr[0].show(),this.nextEnabled=!0,this.prevEnabled=!0},FLIPBOOK.Book3.prototype=Object.create(FLIPBOOK.Book.prototype),FLIPBOOK.Book3.prototype.constructor=FLIPBOOK.Book3,FLIPBOOK.Book3.prototype.enableMouseWheelZoom=function(){this.scroll.options.eventPassthrough="vertical",this.scroll.refresh()},FLIPBOOK.Book3.prototype.disableMouseWheelZoom=function(){this.scroll.options.eventPassthrough="",this.scroll.refresh()},FLIPBOOK.Book3.prototype.enablePrev=function(t){this.prevEnabled=t},FLIPBOOK.Book3.prototype.enablePan=function(){this.scroll.enable()},FLIPBOOK.Book3.prototype.disablePan=function(){this.scroll.disable()},FLIPBOOK.Book3.prototype.setRightIndex=function(t){this.rightIndex=t},FLIPBOOK.Book3.prototype.updateTool=function(){switch(this.options.main.tool){case"toolSelect":this.disablePan(),jQuery(".flipbook-textLayer").css("pointer-events","auto").removeClass(".flipbook-noselect");break;case"toolMove":this.enablePan(),jQuery(".flipbook-textLayer").css("pointer-events","none").addClass(".flipbook-noselect")}},FLIPBOOK.Book3.prototype.enableNext=function(t){this.nextEnabled=t},FLIPBOOK.Book3.prototype.isZoomed=function(){return this.options.main.zoom>this.options.zoomMin&&1<this.options.main.zoom},FLIPBOOK.Book3.prototype.onZoom=function(){},FLIPBOOK.Book3.prototype.getNumPages=function(){},FLIPBOOK.Book3.prototype.move=function(t){if(!(this.zoom<=1)){var e=this.scroll;if(e){var i=e.x,o=e.y,s=20*this.zoom;switch(t){case"left":i+=s;break;case"right":i-=s;break;case"up":o+=s;break;case"down":o-=s}0<i&&(i=0),i<e.maxScrollX&&(i=+e.maxScrollX),0<o&&(o=0),o<e.maxScrollY&&(o=+e.maxScrollY),e.scrollTo(i,o,0)}}},FLIPBOOK.Book3.prototype.zoomTo=function(t,e,i,o){var s;this.enabled&&(i=i||0,o=o||0,e=e||0,this.zoom=t,e=0,(s=this.scroll)&&s.zoom(t*this.ratio,i,o,e))},FLIPBOOK.Book3.prototype.setWrapperW=function(t){this.$wrapperW!=t&&(this.$wrapper.width(t),this.$wrapperW=t)},FLIPBOOK.Book3.prototype.updateBookPosition=function(){if(this.options.singlePageMode)return this.setWrapperW(this.pageWidth),this.$wrapper.width(this.pageWidth),this.scroll&&this.scroll.refresh(),this.view=1,void this.focusRight();1==this.view?this.setWrapperW(this.pageWidth):this.setWrapperW(2*this.pageWidth),this.scroll&&this.scroll.refresh(),2==this.view?this.isCover()?this.focusRight():this.isBackCover()?this.focusLeft():this.focusBoth():1==this.view&&(this.isCover()?this.focusRight():this.isBackCover()&&this.focusLeft())},FLIPBOOK.Book3.prototype.focusLeft=function(t,e){var i=1==this.view||this.options.singlePageMode?0:this.pageWidth/2;this.setBookPosition(i,t,e)},FLIPBOOK.Book3.prototype.focusRight=function(t,e){var i=1==this.view||this.options.singlePageMode?-this.pageWidth:-this.pageWidth/2;this.setBookPosition(i,t,e)},FLIPBOOK.Book3.prototype.focusBoth=function(t,e){var i=1==this.view?-this.pageWidth/2:0;this.setBookPosition(i,t,e)},FLIPBOOK.Book3.prototype.setBookPosition=function(t,e,i){var o,s,n;this.centerContainerPosition!=t&&(o=this.centerContainerPosition,e?(s=this,i=i||0,jQuery({someValue:o}).delay(i).animate({someValue:t},{duration:e,easing:this.flipEasing,step:function(t){s._setStyle(s.centerContainerStyle,FLIPBOOK.IScroll.utils.style.transform,"translateX("+t+"px) translateZ(0)")},complete:function(){s.centerContainerPosition=t,s.updateFlipped(),s.options.main.turnPageComplete()}})):(n="translateX("+t+"px)",this.centerContainerPosition=t,this._setStyle(this.centerContainerStyle,FLIPBOOK.IScroll.utils.style.transform,n),this.updateFlipped(),this.options.main.turnPageComplete()))},FLIPBOOK.Book3.prototype.updateVisiblePages=function(t){void 0===t&&(t=!0);var e=this,i=FLIPBOOK.IScroll.utils.style.transform,o=this.rightIndex;this.options.oddPages&&this.options.rightToLeft&&o--;for(var s=0;s<this.pagesArr.length;s++)s==o||s==o-1&&!this.options.singlePageMode?this.pagesArr[s].show():this.pagesArr[s].hide(),this.pagesArr[s]._setZIndex(0),this.pagesArr[s]._setStyle(this.pagesArr[s].wrapper.style,i,"");this.updateBookPosition();var n=this.pagesArr[o],a=this.pagesArr[o-1],h=this.pagesArr[o+1],r=this.pagesArr[o+2],p=this.pagesArr[o-2],l=this.pagesArr[o-3];a?(e.options.main.setLoadingProgress(.1),a.load(function(){e.options.main.setLoadingProgress(1),a.showHtml(),n?n.load(function(){e.options.main.setLoadingProgress(1),p&&p.load(),l&&l.load(),h&&h.load(),r&&r.load(),n.showHtml()}):(p&&p.load(),l&&l.load())})):(e.options.main.setLoadingProgress(.1),n.load(function(){e.options.main.setLoadingProgress(1),h&&h.load(),r&&r.load(),n.showHtml()}));var g=this.options.rightToLeft?-this.model.bookW:0;this.scroll.scrollTo(g,0,300)},FLIPBOOK.Book3.prototype.enable=function(){this.onResize(),this.enabled=!0},FLIPBOOK.Book3.prototype.disable=function(){this.onResize(),this.enabled=!1},FLIPBOOK.Book3.prototype.getLeftPage=function(){return this.pagesArr[this.flippedleft-1]},FLIPBOOK.Book3.prototype.getRightPage=function(){return this.pagesArr[this.flippedleft]},FLIPBOOK.Book3.prototype.getLeftBackPage=function(){return this.pagesArr[this.flippedleft-2]},FLIPBOOK.Book3.prototype.getRightBackPage=function(){return this.pagesArr[this.flippedleft+1]},FLIPBOOK.Book3.prototype.getNextPage=function(){return this.pagesArr[this.flippedleft+2]},FLIPBOOK.Book3.prototype.getPrevPage=function(){return this.pagesArr[this.flippedleft-3]},FLIPBOOK.Book3.prototype.nextPage=function(){var t;this.nextEnabled&&(1!=this.view||!this.isFocusedLeft()||this.options.singlePageMode?this.goToPage(this.rightIndex+2):(t=700*this.options.pageFlipDuration/2,this.focusRight(t)))},FLIPBOOK.Book3.prototype.prevPage=function(){var t,e;this.prevEnabled&&(1!=this.view||!this.isFocusedRight()||this.options.singlePageMode?(t=this.options.singlePageMode?this.rightIndex:this.rightIndex-2,this.goToPage(t)):(e=700*this.options.pageFlipDuration/2,this.focusLeft(e)))},FLIPBOOK.Book3.prototype.goToPage=function(t,e){if(this.enabled&&!this.flipping&&(!this.options.singlePageMode&&t%2==0||t--,t<0&&(t=0),t>this.options.pages.length&&(t=this.options.pages.length),t!=this.rightIndex)){if(e||this.options.instantFlip)return this.setRightIndex(t),this.updateFlipped(),this.updateVisiblePages(),void this.options.main.turnPageComplete();this.flipping=!0;var i=this.flipEasing;void 0===jQuery.easing[i]&&this.options.main.initEasing();var o=700*this.options.pageFlipDuration;this.options.singlePageMode||(1==this.view?t<this.rightIndex?this.focusRight(o):this.focusLeft(o):0==t?this.focusRight(o):t==this.pagesArr.length?this.focusLeft(o):this.focusBoth(o));var s=this;(this.goingToPage=t)>this.rightIndex?(end=180,(s.angle<=0||180<=s.angle||!s.angle)&&(s.angle=1)):t<this.rightIndex&&(end=-180,(0<=s.angle||s.angle<=-180||!s.angle)&&(s.angle=-1)),this.options.singlePageMode&&(o/=2),jQuery({someValue:s.angle}).animate({someValue:end},{duration:o,easing:i,step:function(t){s._setPageAngle(t)},complete:function(){s.setRightIndex(s.goingToPage),s.angle=0,s.flipping=!1,s.updateFlipped(),s.updateVisiblePages(),s.options.main.turnPageComplete()}}),s.options.main.playFlipSound()}},FLIPBOOK.Book3.prototype.updateFlipped=function(){this.flippedleft=(this.rightIndex+this.rightIndex%2)/2,this.flippedright=this.options.pages.length/2-this.flippedleft},FLIPBOOK.Book3.prototype.onSwipe=function(t,e,i,o,s,n,a){if(!this.isZoomed()&&"flipbook-page-link"!=t.target.className&&"start"!=e&&"up"!=i&&"down"!=i&&!this.flipping){"right"==i&&(o*=-1);var h=180*o/this.model.wrapperW;if("cancel"==e&&n<=1&&(0<h?this.nextPage():h<0&&this.prevPage()),"end"==e&&n<=1&&(0<h?this.nextPage():h<0&&this.prevPage()),"move"==e&&n<=1){if(0<h){if(!this.nextEnabled)return;if(this.options.singlePageMode&&this.rightIndex==this.pagesArr.length-1)return;this.goingToPage=this.options.singlePageMode?this.rightIndex+1:this.rightIndex+2}else if(h<0){if(!this.prevEnabled)return;this.goingToPage=this.options.singlePageMode?this.rightIndex-1:this.rightIndex-2}this.goingToPage!=this.rightIndex&&0<=this.goingToPage&&this.goingToPage<=this.pagesArr.length&&(this.options.instantFlip||this._setPageAngle(h))}}},FLIPBOOK.Book3.prototype.hideHtml=function(t,e,o){for(i=0;i<this.pagesArr.length;i++)this.pagesArr[i].hideHtml()},FLIPBOOK.Book3.prototype._setStyle=function(t,e,i){t&&(t[e]=i)},FLIPBOOK.Book3.prototype._setPageAngle=function(t){var e,i,o,s;if(this.angle=t,this.hideHtml(),this.options.singlePageMode)0<t?((o=this.pagesArr[this.rightIndex])._setAngle(t/2),(i=this.pagesArr[this.goingToPage])&&i.show()):((s=this.pagesArr[this.goingToPage]).show(),s._setAngle(t/2+90),o=this.pagesArr[this.rightIndex]);else{var n=this.rightIndex,a=this.goingToPage;if(this.options.rightToLeft&&this.options.oddPages&&(n--,a--),0<t){if(1==this.view&&this.isFocusedLeft())return;o=this.pagesArr[n],s=this.pagesArr[a-1],this.applyAngles(o,s,t,90,0),i=this.pagesArr[a],o.setShadowOpacity((t-10)/600),s.setShadowOpacity((180-t-10)/600),i&&i.show()}else{if(1==this.view&&this.isFocusedRight())return;s=this.pagesArr[n-1],o=this.pagesArr[a],this.applyAngles(o,s,t,-90,180),o.setShadowOpacity((180+t-10)/600),s.setShadowOpacity((-t-10)/600),(e=this.pagesArr[a-1])&&e.show()}}},FLIPBOOK.Book3.prototype.isCover=function(){return 0==this.rightIndex},FLIPBOOK.Book3.prototype.isBackCover=function(){return this.rightIndex==this.options.pages.length},FLIPBOOK.Book3.prototype.applyAngles=function(t,e,i,o,s){i<o?(t&&(t.show(),t._setAngle(i+s)),e&&(e.hide(),e._setAngle(45))):(e&&(e.show(),e._setAngle(i+s)),t&&(t.hide(),t._setAngle(45)))},FLIPBOOK.Book3.prototype.onPageUnloaded=function(t,e){var i=t;this.options.rightToLeft&&(i=this.options.pages.length-t-1),this.pagesArr[i]&&this.pagesArr[i].unload()},FLIPBOOK.Book3.prototype.onResize=function(){var t=this.model,e=t.wrapperW,i=t.wrapperH,o=t.bookW,s=t.bookH,n=t.pageW,a=t.pageH,h=e/i,r=n/a,p=(this.options.main,this.options),l=this;function g(){l.ratio=i/s,c()}function d(){l.ratio=1==l.view?e/n:e/o,c()}function c(){l.scroll&&(l.scroll.options.zoomMin=l.ratio*p.zoomMin),l.scroll&&(l.scroll.options.zoomMax=l.ratio*p.zoomMax),l.updateBookPosition(),l.scroll&&l.scroll.zoom(l.ratio*p.main.zoom,e/2,i/2,0)}Math.min(this.zoom,1),Number(this.options.zoomMin);!this.options.singlePageMode&&this.options.responsiveView&&e<=this.options.responsiveViewTreshold&&h<2*r?(this.view=1,(n/a<e/i?g:d)()):(this.view=2,(o/s<=e/i?g:d)()),this.updateBookPosition(),this.updateFlipped(),this.options.main.turnPageComplete()},FLIPBOOK.Book3.prototype.isFocusedRight=function(){var t=1==this.view?-this.pageWidth/2:0;return this.options.singlePageMode?this.rightIndex%2==0:this.centerContainerPosition<t},FLIPBOOK.Book3.prototype.isFocusedLeft=function(){var t=1==this.view?-this.pageWidth/2:0;return this.options.singlePageMode?this.rightIndex%2==1:this.centerContainerPosition>t},FLIPBOOK.Page3=function(t,e,i,o){this.book=t,this.options=t.options,this.texture=i,this.html=o,this.index=e,this.$wrapper=jQuery("<div>").addClass("flipbook-page3").width(t.options.pageWidth).height(t.options.pageHeight),this.wrapper=this.$wrapper[0],this.htmlContentScale=this.book.$wrapper.height()/1e3;var s=t.options;this.$inner=jQuery("<div>").appendTo(this.$wrapper).addClass("flipbook-page3-inner"),this.$bg=jQuery("<div>").appendTo(this.$inner).addClass("flipbook-page3-bg"),this.$html=jQuery("<div>").appendTo(this.$inner).addClass("flipbook-page3-html").addClass("page_"+e),this.$shadow=jQuery("<div>").appendTo(this.$wrapper).addClass("flipbook-page3-shadow"),s.pagePreloader?this.$preloader=jQuery('<img src="'+s.pagePreloader+'" class="flipbook-page-preloader-image">').appendTo(this.$wrapper):this.$preloader=jQuery('<img src="'+s.assets.spinner+'" class="flipbook-page-preloader">').appendTo(this.$wrapper),this.shadow=this.$shadow[0],this.hidden=!1,this.hide(),this.zIndex=0,this.options.rightToLeft&&this.options.oddPages&&e++,this.options.singlePageMode||e%2==0?(this.$wrapper.css("left",String(this.book.options.pageWidth-1)+"px").addClass("flipbook-page3-front"),this.type="front"):(this.$wrapper.addClass("flipbook-page3-back"),this.type="back")},FLIPBOOK.Page3.prototype={load:function(o){var s,t;1!=this.loaded?(this.loaded=!0,t=(s=this).options.rightToLeft?this.book.pagesArr.length-this.index-1:this.index,this.options.main.loadPageHTML(t,function(t){t&&(s.$html.empty().append(jQuery(t)),s.$html[0].style.transform="scale("+s.htmlContentScale+") translateZ(0)"),s.options.doublePage&&s.index%2==0&&0<s.index&&(s.$html[0].style.left="-100%"),s.hidden&&s.hideHtml()}),this.options.main.loadPage(t,this.options.pageTextureSize,function(t){var e,i;t.image&&(e=t.image,t.image.naturalHeight,i=t.image.naturalHeight,s.options.pageWidth,s.options.pageHeight,"left"!=s.options.pages[t.index].side&&"right"!=s.options.pages[t.index].side||s.index%2==0&&jQuery(e).css(FLIPBOOK.IScroll.utils.style.transform,"translateX(-50%) translateZ(0)"),s.$bg.empty().append(jQuery(e)),s.$preloader.hide()),t.canvas&&s.pageSize!=t.size&&(s.pageSize=t.size,s.$bg.empty(),s.options.doublePage&&s.index%2==0&&0!=s.index?jQuery(t.rCanvas).appendTo(s.$bg):s.options.doublePage&&s.index%2==1&&s.index!=s.options.pages.length-1?jQuery(t.lCanvas).appendTo(s.$bg):jQuery(t.canvas).appendTo(s.$bg),s.$preloader.hide()),o&&o.call(s)})):o&&o.call(this)},unload:function(){this.loaded=!1,this.mat=!1,this.pageSize=null},setMat:function(t,e,i){},hideHtml:function(){this.$html&&!this.htmlHidden&&this.$html.hide(),this.htmlHidden=!0},showHtml:function(){this.$html&&this.htmlHidden&&this.$html.show(),this.htmlHidden=!1},setDataUrl:function(t,e){var i=this;this.$img.attr("src",t),this.$img[0].onload=function(){i.$wrapper.css({background:"none"})},i.html&&jQuery(i.html).appendTo(i.$inner)},show:function(){this.hidden&&(this.$wrapper[0].style.display="block",this.setShadowOpacity(0)),this.hidden=!1},setShadowOpacity:function(t){this.shadow.style.opacity=t},hide:function(){this.hidden||(this.$wrapper[0].style.display="none",this.setShadowOpacity(0)),this.hidden=!0},_setScale:function(t){var e,i;this.scale!=t&&(e=FLIPBOOK.IScroll.utils.style.transform,i="scaleX("+String(t)+") translateZ(0)",this.scale=t,this._setStyle(e,i))},_setAngle:function(t){if(this.angle!=t){this.angle=t;var e,i=FLIPBOOK.IScroll.utils.style.transform,o="translateZ(0)";"3d"==this.book.options.viewMode?e="front"==this.type?"rotateY("+String(-t)+"deg) "+o:"rotateY("+String(180-t)+"deg) "+o:"2d"==this.book.options.viewMode&&(e="front"==this.type?(90<t&&(t=90),"scaleX("+String((180-2*t)/180)+") "+o):(t<90&&(t=90),"scaleX("+String(2*t/180-1)+") "+o)),this._setStyle(this.wrapper.style,i,e);for(var s=0,n=0;n<this.book.pagesArr.length;n++)n!=this.index&&this.book.pagesArr[n].zIndex>s&&(s=this.book.pagesArr[n].zIndex);this._setZIndex(s+1)}},_setZIndex:function(t){this.zIndex!=t&&(this.wrapper.style["z-index"]=t),this.zIndex=t},_setStyle:function(t,e,i){t&&(t[e]=i)}};