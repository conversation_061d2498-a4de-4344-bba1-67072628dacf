/* v 3.17
author http://codecanyon.net/user/creativeinteractivemedia/portfolio?ref=creativeinteractivemedia
*/

var FLIPBOOK=FLIPBOOK||{};FLIPBOOK.PageWebGL=function(t,e,i,o,s,n){THREE.Object3D.call(this),this.book=t,this.index=e,this.pW=o.pageWidth,this.pH=o.pageHeight,this.nfacesw=o.pageSegmentsW,this.nfacesh=o.pageSegmentsH,this.mats=[],this.pageHardness=i,this.pageThickness=i,this.duration=o.pageFlipDuration,this.angle=.25*Math.PI*this.pW/this.pH,this.force=10,this.offset=0,this.to=null,this.mod=null,this.bend=null,this.pivot=null,this.isFlippedLeft=!1,this.isFlippedRight=!0,this.flippingLeft=!1,this.flippingRight=!1;var r=(this.options=o).rightToLeft?o.pages.length/2-this.index-1:this.index;this.indexF=o.rightToLeft?2*r+1:2*r,this.indexB=o.rightToLeft?2*r:2*r+1,this.showing=!1,this.preloaderMatF=s,this.preloaderMatB=n;var h=this;0==e&&this.options.cornerCurl&&(this.nfacesw=20,this.nfacesh=20,this.cornerCurlTween=new FLIPBOOK.TWEEN.Tween(0).to(1,1e3).easing(FLIPBOOK.TWEEN.Easing.Sinusoidal.Out).onUpdate(function(t){h.cornerCurl&&(h.b2.force=-1.8*t,h.modF.apply())}).repeat(11e3).start()),this.gF=new THREE.BoxGeometry(this.pW,this.pH,.01,this.nfacesw,this.nfacesh,0);var a=new THREE.MeshBasicMaterial({color:15592941}),p=[a,a,a,a,s,n],c=[a,a,a,a,a,a];this.options.pagePreloader&&(c=[a,a,a,a,s,n]),this.cube=new THREE.Mesh(this.gF,p),this.cube.position.x=.5*this.pW,this.options.shadows&&(this.cube.castShadow=!0,this.cube.receiveShadow=!0),this.gF.faceVertexUvs[1]=this.gF.faceVertexUvs[0],this.showMat(),this.cubeEmpty=new THREE.Mesh(new THREE.BoxGeometry(this.pW,this.pH,.01,1,1,0),c),this.cubeEmpty.position.x=.5*this.pW,this.pageFlippedAngle=Math.PI*this.options.pageFlippedAngle/180,this.bendF=new MOD3.Bend(0,0,0),this.bendF.constraint=MOD3.ModConstant.LEFT,this.pH>this.pW&&(this.bendF.switchAxes=!0),this.b2=new MOD3.Bend(0,0,0),this.b2.constraint=MOD3.ModConstant.LEFT,this.pH>this.pW&&(this.b2.switchAxes=!0),this.b2.offset=.98,this.b2.setAngle(1),this.modF=new MOD3.ModifierStack(new MOD3.LibraryThree,this.cube),this.modF.addModifier(this.bendF),0==e&&this.options.cornerCurl&&this.modF.addModifier(this.b2),this.modF.apply()},FLIPBOOK.PageWebGL.prototype=new THREE.Object3D,FLIPBOOK.PageWebGL.prototype.constructor=FLIPBOOK.PageWebGL,FLIPBOOK.PageWebGL.prototype.startCornerCurl=function(){this.cornerCurl=!0},FLIPBOOK.PageWebGL.prototype.stopCornerCurl=function(){this.cornerCurl=!1,this.b2.force=0,this.modF.apply()},FLIPBOOK.PageWebGL.prototype.onPageCanvasLoaded=function(t,e,i){var o,s,n;"front"==e&&this.sizeFront!=t.size?(this.sizeFront=t.size,s=t.canvas,(o=new THREE.CanvasTexture(s)).minFilter=THREE.LinearFilter,o.needsUpdate=!0,o.repeat.x=s.scaleX,o.repeat.y=s.scaleY,o.offset.y=1-s.scaleY,s.double&&(o.offset.x=.5),this.frontMaterial=this.createMaterial(o,"front"),this.frontMaterial.needsUpdate=!0,this.setFrontMat(this.frontMaterial)):"back"==e&&this.sizeBack!=t.size&&(this.sizeBack=t.size,s=t.canvas,(n=new THREE.CanvasTexture(s)).minFilter=THREE.LinearFilter,n.needsUpdate=!0,n.repeat.x=s.scaleX,n.repeat.y=s.scaleY,n.offset.y=1-s.scaleY,this.backMaterial=this.createMaterial(n,"back"),this.backMaterial.needsUpdate=!0,this.setBackMat(this.backMaterial)),i.call(this)},FLIPBOOK.PageWebGL.prototype.loadHTML=function(t,e){var i="front"==t?this.indexF:this.indexB,o=this;this.htmlLoaded?e.call(this):this.options.main.loadPageHTML(i,function(t){e.call(o)})},FLIPBOOK.PageWebGL.prototype.load=function(t,i){var e=this.book.model;if(e.wrapperH&&e.zoom){for(var o=this.book.pages,s=this,n=this.book.options,r=0;r<o.length;r++){var h=o[r];if(h.flippingLeft||h.flippingRight)return}this.disposed=!1;var a=e.wrapperH*e.zoom<.8*n.pageTextureSizeSmall?n.pageTextureSizeSmall:n.pageTextureSize;"front"==t?this.sizeFront==a?i&&i.call(this):this.options.main.loadPage(this.indexF,a,function(t){if(t){if(t.canvas)s.onPageCanvasLoaded(t,"front",i);else if(t.image){if(s.loadedFront)return void i.call(s);s.loadedFront=!0;var e=new THREE.Texture;e.image=t.image,e.minFilter=THREE.LinearFilter,e.needsUpdate=!0,"left"==s.options.pages[2*s.index].side?e.repeat.x=.5:"right"==s.options.pages[2*s.index].side&&(e.repeat.x=.5,e.offset.x=.5),s.frontMaterial=s.createMaterial(e),s.setFrontMat(s.frontMaterial),i.call(s)}}else i&&i.call(s)}):"back"==t&&(this.sizeBack==a?i&&i.call(this):this.options.main.loadPage(this.indexB,a,function(t){if(t){if(t.canvas)s.onPageCanvasLoaded(t,"back",i);else if(t.image){if(s.loadedBack)return void i.call(s);s.loadedBack=!0;var e=new THREE.Texture;e.image=t.image,e.minFilter=THREE.LinearFilter,e.needsUpdate=!0,"left"==s.options.pages[2*s.index+1].side?e.repeat.x=.5:"right"==s.options.pages[2*s.index+1].side&&(e.repeat.x=.5,e.offset.x=.5),s.backMaterial=s.createMaterial(e,"back"),s.setBackMat(s.backMaterial),i.call(s)}}else i&&i.call(s)}))}},FLIPBOOK.PageWebGL.prototype.unload=function(t){var e,i;"front"==t&&this.sizeFront?(i=(e=this.cube.material[4]).map,e.dispose(),i&&i.dispose(),this.loadedFront=!1,this.sizeFront=0,this.setFrontMat(this.preloaderMatF)):"back"==t&&this.sizeBack&&(i=(e=this.cube.material[5]).map,e.dispose(),i&&i.dispose(),this.loadedBack=!1,this.sizeBack=0,this.setBackMat(this.preloaderMatB))},FLIPBOOK.PageWebGL.prototype.disposeMat=function(){var t,e,i,o;this.loaded&&(t=this.cube.material[4],e=this.cube.material[5],i=t.map,o=e.map,t.dispose(),e.dispose(),i&&i.dispose(),o&&o.dispose(),this.disposed=!0,this.loaded=!1)},FLIPBOOK.PageWebGL.prototype.createMaterial=function(t,e){var i,o;return this.options.lights?(i="back"==e?this.book.specularB:this.book.specularF,o=this.options,new THREE.MeshStandardMaterial({map:t,roughness:o.pageRoughness,metalness:o.pageMetalness,emissive:0,color:16777215,lightMap:i})):new THREE.MeshBasicMaterial({map:t})},FLIPBOOK.PageWebGL.prototype._setAngle=function(t,e){if(t<=180&&-180<=t){if(t=t/180*Math.PI,this.singlePage&&(90<=t&&(t=90),t<0&&(t-=90),t<-180&&(t=-180),this.index==this.book.pages.length-1))return;t<0&&(t+=Math.PI),this.angle=t,this.positionZ(200),this.dragging=!0,this.rotation.y=-t,this.isFlippedLeft?this.bendF.force=1.35*Math.pow(-Math.abs(Math.cos(-t/2)),1)/Math.pow(this.pageHardness,1.5):this.bendF.force=1.35*Math.pow(Math.abs(Math.sin(-t/2)),1)/Math.pow(this.pageHardness,1.5),this.updateBend(),this.book.htmlLayerVisible&&(this.book.$pageL.hide(),this.book.$pageR.hide(),this.book.$pageC.hide(),this.book.htmlLayerVisible=!1),this.book.needsUpdate=!0}},FLIPBOOK.PageWebGL.prototype.updateBend=function(){this.stopCornerCurl(),this.modF.apply(),this.gF.computeFaceNormals(),this.gF.computeVertexNormals(!0),this.book.needsUpdate=!0},FLIPBOOK.PageWebGL.prototype.flipLeft=function(t){var e,i;this.onComplete=t,this.dragging=!1,this.isFlippedLeft||this.flippingLeft||this.flippingRight||this.index!=this.book.flippedleft||(0<this.duration?(this.flippingLeft=!0,this.flipping=!0,this.force=0,e=(1+.5*Math.random())/this.pageHardness,i=.1+.2*Math.random(),this.to={angle:this.rotation.y,t:-1,xx:0,thiss:this,force:this.force,offset:this.offset},this.bendIn(-Math.PI,e,i)):(this.rotation.y=-Math.PI,this.flippingLeft=!1,this.isFlippedLeft=!0,this.flippingRight=!1,this.isFlippedRight=!1),this.correctZOrder())},FLIPBOOK.PageWebGL.prototype.correctZOrder=function(){var t;for(this.position.z=3,t=this.index-1;0<=t;t--)this.book.pages[t].position.z=this.book.pages[t+1].position.z-2-2-1;for(t=this.index+1;t<this.book.pages.length;t++)this.book.pages[t].position.z=this.book.pages[t-1].position.z-2-2-1},FLIPBOOK.PageWebGL.prototype.flipLeftInstant=function(t){if(this.onComplete=t,this.dragging=!1,!this.isFlippedLeft&&!this.flippingLeft&&!this.flippingRight&&this.index==this.book.flippedleft){(this.thiss=this).xx=0,this.angle=-Math.PI,this.flippingLeft=!0,this.isFlippedLeft=!1,this.renderFlip(),this.flippingLeft=!1,this.isFlippedLeft=!0,this.flippingRight=!1,this.isFlippedRight=!1;this.position.z=3;for(var e=this.index+1;e<this.book.pages.length;e++)this.book.pages[e].position.z=this.book.pages[e-1].position.z-2-2-1;if(this.index<this.book.pages.length-1){this.position.z=this.book.pages[this.index+1].position.z;for(e=this.index-1;0<=e;e--)this.book.pages[e].position.z=this.book.pages[e+1].position.z-2-2-1}else 0<this.index&&(this.book.pages[this.index].position.z=this.book.pages[this.index-1].position.z+2+2-1);this.flipFinished()}},FLIPBOOK.PageWebGL.prototype.hideMat=function(){this.showing&&(this.remove(this.cube),this.add(this.cubeEmpty),this.showing=!1)},FLIPBOOK.PageWebGL.prototype.showMat=function(){this.showing||(this.add(this.cube),this.remove(this.cubeEmpty),this.showing=!0,this.book.needsUpdate=!0)},FLIPBOOK.PageWebGL.prototype.setFrontMat=function(t){this.cube.material[4]!==t&&(this.cube.material[4]=t,this.book.needsUpdate=!0)},FLIPBOOK.PageWebGL.prototype.setBackMat=function(t){this.cube.material[5]!==t&&(this.cube.material[5]=t,this.book.needsUpdate=!0)},FLIPBOOK.PageWebGL.prototype.flipRightInstant=function(t){if(this.onComplete=t,this.dragging=!1,!this.isFlippedRight&&!this.flippingRight&&!this.flippingLeft&&this.index==this.book.getNumPages()-this.book.flippedright-1){(this.thiss=this).xx=0,this.angle=0,this.flippingRight=!0,this.isFlippedRight=!1,this.renderFlip(),this.flippingLeft=!1,this.isFlippedLeft=!1,this.flippingRight=!1,this.isFlippedRight=!0;this.position.z=3;for(var e=this.index-1;0<=e;e--)this.book.pages[e].position.z=this.book.pages[e+1].position.z-2-2-1;if(0<this.index){this.position.z=this.book.pages[this.index-1].position.z;for(e=this.index+1;e<this.book.pages.length;e++)this.book.pages[e].position.z=this.book.pages[e-1].position.z-2-2-1}else 1<this.book.pages.length&&(this.position.z=this.book.pages[this.index+1].position.z+2+2-1);this.flipFinished()}},FLIPBOOK.PageWebGL.prototype.flipRight=function(t){var e,i;this.onComplete=t,this.dragging=!1,this.isFlippedRight||this.flippingRight||this.flippingLeft||this.index!=this.book.getNumPages()-this.book.flippedright-1||(0<this.duration?(this.flippingRight=!0,this.flipping=!0,this.force=0,this.to={angle:this.rotation.y,t:-1,xx:0,thiss:this,force:this.force,offset:this.offset},e=(-1-.5*Math.random())/this.pageHardness,i=.1+.2*Math.random(),this.bendIn(0,e,i)):(this.rotation.y=0,this.flippingLeft=!1,this.isFlippedLeft=!1,this.flippingRight=!1,this.isFlippedRight=!0),this.correctZOrder())},FLIPBOOK.PageWebGL.prototype.bendIn=function(t,e,i){this.bendF.force=0,this.bendF.offset=0,this.updateBend();var o=2*this.duration*240*Math.pow(Math.abs(this.rotation.y-t)/Math.PI,.5)*Math.pow(this.pageHardness,.25);new FLIPBOOK.TWEEN.Tween(this.to).to({angle:t,xx:1,t:1},o).easing(FLIPBOOK.TWEEN.Easing.Sinusoidal.In).onUpdate(this.renderFlip).onComplete(this.bendOut).start(),this.options.main.playFlipSound()},FLIPBOOK.PageWebGL.prototype.bendOut=function(){var t=this.thiss,e=t.duration*Math.pow(Math.abs(t.bendF.force),.5)*1e3;new FLIPBOOK.TWEEN.Tween(t.bendF).to({force:0,offset:1},e).easing(FLIPBOOK.TWEEN.Easing.Sinusoidal.Out).onUpdate(function(){t.updateBend()}).onComplete(function(){t.flipFinished(t)}).start();if(t.flippingLeft)if(t.index<t.book.pages.length-1){t.position.z=t.book.pages[t.index+1].position.z;for(var i=t.index-1;0<=i;i--)t.book.pages[i].position.z=t.book.pages[i+1].position.z-2-2-1}else 1<t.book.pages.length&&(t.book.pages[t.index].position.z=t.book.pages[t.index-1].position.z+2+2-1);if(t.flippingRight)if(0<t.index){t.position.z=t.book.pages[t.index-1].position.z;for(i=t.index+1;i<t.book.pages.length;i++)t.book.pages[i].position.z=t.book.pages[i-1].position.z-2-2-1}else 1<t.book.pages.length&&(t.position.z=t.book.pages[t.index+1].position.z+2+2-1)},FLIPBOOK.PageWebGL.prototype.modApply=function(){this.thiss.bendF.force=this.thiss.bendB.force=this.force,this.thiss.bendF.offset=this.thiss.bendB.offset=this.offset,this.thiss.updateBend()},FLIPBOOK.PageWebGL.prototype.renderFlip=function(){this.thiss._setAngle(180*-this.angle/Math.PI)},FLIPBOOK.PageWebGL.prototype.flipFinished=function(){var t=this;t.flippingLeft?(t.flippingLeft=!1,t.isFlippedLeft=!0,t.flippingRight=!1,t.isFlippedRight=!1):t.flippingRight&&(t.flippingLeft=!1,t.isFlippedRight=!0,t.flippingRight=!1,t.isFlippedLeft=!1),t.bendF.force=0,t.bendF.offset=0,t.updateBend(),t.flipping=!1,t.dragging=!1,void 0!==t.onComplete&&t.onComplete(t),t.book.flipFinnished()},FLIPBOOK.PageWebGL.prototype.isFlippedLeft=function(){return this.thiss.isFlippedLeft},FLIPBOOK.PageWebGL.prototype.isFlippedRight=function(){return this.thiss.isFlippedRight},FLIPBOOK.PageWebGL.prototype.positionZ=function(){},FLIPBOOK.BookWebGL=function(t,e,i){this.wrapper=t,this.options=i,this.model=e,this.options.cameraDistance=2800,this.pageW=i.pageWidth,this.pageH=i.pageHeight,this.pageW=1e3*i.pageWidth/i.pageHeight,this.pageH=1e3,i.pageWidth=this.pageW,i.pageHeight=this.pageH,this.scroll=i.scroll,this.pagesArr=i.pages,this.pages=[],this.animating=!1,this.sc=1;var o=this.wrapper.style;o.width="100%",o.height="100%",o.position="absolute",o.overflow="hidden",this.options.cameraDistance=this.options.cameraDistance/1.5},FLIPBOOK.BookWebGL.prototype=Object.create(FLIPBOOK.Book.prototype),FLIPBOOK.BookWebGL.prototype.constructor=FLIPBOOK.BookWebGL,FLIPBOOK.BookWebGL.prototype.init3d=function(){var e=this,t=jQuery(e.wrapper).width()/jQuery(e.wrapper).height(),i=this.options;this.Scene=new THREE.Scene,this.centerContainer=new THREE.Object3D,this.Scene.add(this.centerContainer),this.Camera=new THREE.PerspectiveCamera(30,t,1,1e4),this.Scene.add(this.Camera),this.zoom=i.zoom,this.pan=i.pan,this.tilt=i.tilt,this.updateCameraPosition();var o=this.wrapper,s=(document.createElement("canvas").getContext("webgl"),new THREE.WebGLRenderer({antialias:this.options.antialias,alpha:!0}));(window.renderer=s).gammaInput=!0,s.gammaOutput=!0,this.options.shadows&&(s.shadowMap.enabled=!0,s.shadowMap.type=THREE.PCFShadowMap),window.webglrenderer=this.renderer=s,this.renderer.setSize(o.clientWidth,o.clientHeight);var n=window.devicePixelRatio<i.minPixelRatio?i.minPixelRatio:window.devicePixelRatio;this.renderer.setPixelRatio(n),o.appendChild(this.renderer.domElement);for(var r,h,a,p,c,l=!1,d=this.options.pages,u=0;u<d.length;u++)d[u].htmlContent&&(l=!0);this.options.hasHtmlContent=l||i.pdfMode,this.options.hasHtmlContent&&this.initHtmlContent(),this.canvas=this.renderer.domElement,this._bind("mousedown",this.canvas.parentNode.parentNode),this._bind("mousemove",this.canvas.parentNode.parentNode),this._bind("mouseout",this.canvas.parentNode.parentNode),this._bind("mouseup",this.canvas.parentNode.parentNode),this._bind("touchstart",this.canvas.parentNode.parentNode),this._bind("touchmove",this.canvas.parentNode.parentNode),this._bind("touchend",this.canvas.parentNode.parentNode),this._bind("touchcancel",this.canvas.parentNode.parentNode),this.options.lights&&(r=i.lightColor,(h=new THREE.SpotLight(r)).intensity=i.lightIntensity,h.position.set(i.lightPositionX,i.lightPositionY,i.lightPositionZ),h.distance=4e3,this.options.shadows&&(h.castShadow=!0,h.shadow.bias=-2e-6,h.shadow.mapSize.x=this.options.shadowMapSize,h.shadow.mapSize.y=this.options.shadowMapSize,(a=new THREE.ShadowMaterial).opacity=this.options.shadowOpacity,(p=new THREE.Mesh(new THREE.BoxGeometry(1e4,1e4,1,1,1,1),a)).position.set(0,0,-i.shadowDistance),this.Scene.add(p),p.receiveShadow=!0),this.Scene.add(h),c=i.ambientLightColor||3355443,new THREE.AmbientLight(c)),this.centerContainer.position.set(0,0,0),this.onResize(),this.centerContainer.position.x=.5*-this.pageW*this.centerContainer.scale.x,this.updateHtmlLayerPosition(),this.flippedleft=0,this.flippedright=0,this.cameraZMin=300,this.cameraZMax=5e3,function t(){if(FLIPBOOK.TWEEN.update(),e.rendering){if(!e.enabled)return;e.needsUpdate&&(e.renderer.render(e.Scene,e.Camera),e.needsUpdate=!1,e.htmlLayer&&e.cssRenderer.render(e.Scene,e.Camera))}requestAnimationFrame(t)}()},FLIPBOOK.BookWebGL.prototype.onPageUnloaded=function(t,e){var i=Math.floor(t/2),o=this.options.rightToLeft?(i=this.pages.length-i-1,t%2==0?"back":"front"):t%2==0?"front":"back";this.pages[i].unload(o)},FLIPBOOK.BookWebGL.prototype.initHtmlContent=function(){var t=document.createElement("div");t.className="htmlLayer "+Math.random();var e=jQuery(t);this.$htmlLayer=e,this.$pageR=jQuery(document.createElement("div")).addClass("R").css({width:1e3*this.options.pageWidth/this.options.pageHeight+"px",height:"1000px",position:"absolute",top:"-500px","pointer-events":"none"}).appendTo(e),this.$pageRInner=jQuery("<div>").css("pointer-events","all").appendTo(this.$pageR),this.$pageL=jQuery(document.createElement("div")).addClass("L").css({width:1e3*this.options.pageWidth/this.options.pageHeight+"px",height:"1000px",position:"absolute",top:"-500px",left:-1e3*this.options.pageWidth/this.options.pageHeight+"px","pointer-events":"none"}).appendTo(e),this.$pageLInner=jQuery(document.createElement("div")).css("pointer-events","all").appendTo(this.$pageL),this.$pageC=jQuery(document.createElement("div")).addClass("C").css({width:2e3*this.options.pageWidth/this.options.pageHeight+"px",height:"1000px",position:"absolute",top:"-500px",left:-1e3*this.options.pageWidth/this.options.pageHeight+"px","pointer-events":"none"}).appendTo(e),this.$pageCInner=jQuery(document.createElement("div")).css("pointer-events","all").appendTo(this.$pageC),this.htmlLayer=new FLIPBOOK.CSS3DObject(t),this.Scene.add(this.htmlLayer),this.cssRenderer=new FLIPBOOK.CSS3DRenderer;this.wrapper;this.wrapper.appendChild(this.cssRenderer.domElement),this.cssRenderer.domElement.style.position="absolute",this.cssRenderer.domElement.style.top="0",this.cssRenderer.domElement.style.left="0",this.cssRenderer.domElement.style.pointerEvents="none",this.cssRenderer.domElement.className="cssRenderer "+Math.random();var i=this;this.model.on("toolSelect",function(){i.updateTool()}),this.model.on("toolMove",function(){i.updateTool()})},FLIPBOOK.BookWebGL.prototype.enablePrev=function(t){this.prevEnabled=t},FLIPBOOK.BookWebGL.prototype.enableNext=function(t){this.nextEnabled=t},FLIPBOOK.BookWebGL.prototype.isZoomed=function(){return this.options.zoom>this.options.zoomMin&&1<this.options.zoom},FLIPBOOK.BookWebGL.prototype.getRightPage=function(){return this.pages[this.flippedleft]},FLIPBOOK.BookWebGL.prototype.getNextPage=function(){return this.pages[this.flippedleft+1]},FLIPBOOK.BookWebGL.prototype.getLeftPage=function(){return this.pages[this.flippedleft-1]},FLIPBOOK.BookWebGL.prototype.getPrevPage=function(){return this.pages[this.flippedleft-2]},FLIPBOOK.BookWebGL.prototype.onSwipe=function(t,e,i,o,s,n,r){if(!this.isZoomed()){"right"==i&&(o*=-1);var h=this.getLeftPage(),a=this.getRightPage(),p=this.getNextPage(),c=this.getPrevPage();if(!this.options.rotateCameraOnMouseDrag||a&&a.dragging||h&&h.dragging||"rotate"!=this.onMouseMove&&"scroll"!=this.onMouseMove){if(("cancel"==e||"end"==e)&&n<=1){if(1==this.view&&this.draggingBook&&"left"==i)return this.nextPage(),void(this.draggingBook=!1);if(1==this.view&&this.draggingBook&&"right"==i)return this.prevPage(),void(this.draggingBook=!1);!(o<0)||a&&a.dragging?!(0<o)||h&&h.dragging?0==o&&this.clickedPage&&(this.clickedPage.isFlippedLeft?this.prevPage():this.nextPage()):this.nextPage():this.prevPage(),a&&(a.dragging=!1),h&&(h.dragging=!1)}if("move"==e&&n<=1){if(this.draggingBook)return this.centerContainer.position.x=this.draggingBookStartX-o,void this.updateHtmlLayerPosition();if(1==this.view&&this.isFocusedLeft()&&"left"==i&&this.canFlipNext())return this.draggingBookStartX=this.centerContainer.position.x,void(this.draggingBook=!0);if(1==this.view&&this.isFocusedRight()&&"right"==i)return this.draggingBookStartX=this.centerContainer.position.x,void(this.draggingBook=!0);if(o=180*o/this.wrapperW,h&&h.flipping||a&&a.flipping)return;a&&a.dragging||h&&h.dragging?h&&!a||h&&!a.dragging?o<=0&&h._setAngle(o,i):(a&&!h||a&&!h.dragging)&&0<=o&&a._setAngle(o,i):0!=o&&("right"!=i||!h||a&&a.dragging||!this.prevEnabled?"left"==i&&a&&this.nextEnabled&&(a._setAngle(o,i),a.positionZ(200),p&&p.showMat()):(h._setAngle(o,i),h.positionZ(200),c&&c.showMat()))}}}},FLIPBOOK.BookWebGL.prototype.onResize=function(){var t,e=this.model,i=e.wrapperW,o=e.wrapperH,s=this.options,n=i/o,r=s.pageWidth/s.pageHeight;o<1e3&&1==window.devicePixelRatio?this.renderer.setPixelRatio(2):(t=window.devicePixelRatio<s.minPixelRatio?s.minPixelRatio:window.devicePixelRatio,this.renderer.setPixelRatio(t));var h=Math.min(this.zoom,1),a=Number(s.zoomMin);s.responsiveView&&i<=s.responsiveViewTreshold&&n<2*r?(this.view=1,this.sc=n<r?a*n/(r*h):1,0==this.rightIndex||this.isFocusedRight()?this.focusRight():this.focusLeft()):(this.view=2,this.sc=n<2*r?a*n/(2*r*h):1,0==this.flippedleft?this.focusRight():0==this.flippedright?this.focusLeft():this.focusBoth()),this.renderer.setSize(i,o),this.htmlLayer&&(this.cssRenderer.setSize(i,o),this.htmlLayer.scale.set(this.sc,this.sc,this.sc)),this.Camera.aspect=i/o,this.Camera.updateProjectionMatrix(),this.updateCameraPosition(),this.updateBookPosition(),this.options.main.turnPageComplete(),this.wrapperW=i,this.wrapperH=o},FLIPBOOK.BookWebGL.prototype.updateCameraPosition=function(){var t,e=Math.PI*this.tilt/180,i=this.options.cameraDistance*Math.sin(e)/this.zoom,o=this.options.cameraDistance*Math.cos(e)/this.zoom;this.centerContainer.scale.set(this.sc,this.sc,this.sc),e=Math.PI*this.pan/180,t=Math.sin(e)*o,o=Math.cos(e)*o,this.cameraZ=o,this.Camera.position.set(Math.round(t),Math.round(i),Math.round(o)),this.Camera.lookAt(this.Scene.position),this.needsUpdate=!0},FLIPBOOK.BookWebGL.prototype.createPages=function(){var t,e,i=this,o=i.options,s=o.pageMiddleShadowSize,n=document.createElement("canvas");n.width=64,n.height=64;var r=n.getContext("2d"),h=r.createLinearGradient(64-s,0,64,0);h.addColorStop(0,"#AAAAAA"),h.addColorStop(1,o.pageMiddleShadowColorL),r.fillStyle=h,r.fillRect(0,0,64,64);var a=new THREE.CanvasTexture(n);a.needsUpdate=!0,i.specularB=a;var p=document.createElement("canvas");p.width=64,p.height=64;var c=p.getContext("2d"),l=c.createLinearGradient(0,0,s,0);l.addColorStop(0,o.pageMiddleShadowColorR),l.addColorStop(1,"#AAAAAA"),c.fillStyle=l,c.fillRect(0,0,64,64);var d,u,g,f=new THREE.CanvasTexture(p);f.needsUpdate=!0,i.specularF=f,i.options.pagePreloader?(g=(new THREE.TextureLoader).load(i.options.pagePreloader,function(){}),i.options.lights?(d=new THREE.MeshStandardMaterial({map:g,roughness:i.options.pageRoughness,metalness:i.options.pageMetalness,emissive:0,color:15592941,lightMap:i.specularF}),u=new THREE.MeshStandardMaterial({map:g,roughness:i.options.pageRoughness,metalness:i.options.pageMetalness,emissive:0,color:15592941,lightMap:i.specularB})):d=u=new THREE.MeshBasicMaterial({map:g,color:15592941})):i.options.lights?(d=new THREE.MeshStandardMaterial({roughness:i.options.pageRoughness,metalness:i.options.pageMetalness,emissive:0,color:15592941,lightMap:i.specularF}),u=new THREE.MeshStandardMaterial({roughness:i.options.pageRoughness,metalness:i.options.pageMetalness,emissive:0,color:15592941,lightMap:i.specularB})):d=u=new THREE.MeshBasicMaterial({color:15592941});for(var m=i.options.pages,y=0;y<m.length/2;y++){t=0==y||y==m.length/2-1?i.options.coverHardness:i.options.pageHardness,e=new FLIPBOOK.PageWebGL(i,y,t,i.options,d,u),i.pages.push(e),i.centerContainer.add(e),i.flippedright++;0<y?(e.position.z=i.pages[y-1].position.z-2-2-1,this.bg&&(this.bg.position.z=e.position.z-5)):e.position.z=2}i.initialized=!0},FLIPBOOK.BookWebGL.prototype.getNumPages=function(){return this.pages.length},FLIPBOOK.BookWebGL.prototype.centerContainer=function(){return this.centerContainer},FLIPBOOK.BookWebGL.prototype.goToPage=function(t,e,i){if(this.initialized){if(e)for(var o=0;o<this.pages.length;o++)if(this.pages[o].flippingLeft||this.pages[o].flippingRight)return;var s;if(t<0&&(t=0),t>this.options.pages.length&&(t=this.options.pages.length),1!=this.view||i||(s=e?0:300,t%2==0?this.focusLeft(s):this.focusRight(s)),t%2!=0&&t--,t==this.rightIndex)return this.loadPages(),void this.turnPageComplete();this.goingToPage=!0;var n=this,r=1e3*this.options.pageFlipDuration/6;if(void 0!==e&&e){if(t>n.rightIndex)for(;n.rightIndex<t;)this.nextPageInstant();else for(;n.rightIndex>t;)this.prevPageInstant();return this.updateBookPosition(),this.loadPages(),void this.turnPageComplete()}this.rightIndex>t?(r=1/(this.rightIndex-t)*this.options.pageFlipDuration*1e3/6,10<this.rightIndex-t&&(r=0),this.rightIndex-2>t?(this.prevPage(!1),0<r?setTimeout(function(){n.goToPage(t,e,1)},r):n.goToPage(t,e,1)):(this.prevPage(),setTimeout(function(){if(void 0!==e&&e)for(var t=0;t<n.pages.length;t++)n.pages[t].duration=n.options.pageFlipDuration;n.loadPages()},r))):this.rightIndex<t&&(r=-1/(this.rightIndex-t)*this.options.pageFlipDuration*1e3/6,this.rightIndex-t<-10&&(r=0),this.rightIndex+2<t?(this.nextPage(!1),0<r?setTimeout(function(){n.goToPage(t,e,1)},r):n.goToPage(t,e,1)):(this.nextPage(),setTimeout(function(){if(void 0!==e&&e)for(var t=0;t<n.pages.length;t++)n.pages[t].duration=n.options.pageFlipDuration;n.loadPages()},r)))}else{var n=this;setTimeout(function(){n.goToPage(t,e)},100)}},FLIPBOOK.BookWebGL.prototype.nextPageInstant=function(t){if(0!=this.flippedright){for(var e=0;e<this.pages.length;e++)if(this.pages[e].flippingRight)return;if(1==this.view){if(this.isFocusedLeft()){if(!this.goingToPage)return this.focusRight(0),void this.turnPageComplete();this.focusLeft(0,0)}}else 1==this.flippedright?this.focusLeft(0):this.focusBoth(0);this.pages[this.pages.length-this.flippedright].flipLeftInstant(),this.flippedleft++,this.flippedright--,this.setRightIndex(this.rightIndex+2),this.updateBookPosition()}},FLIPBOOK.BookWebGL.prototype.setRightIndex=function(t){this.rightIndex=t},FLIPBOOK.BookWebGL.prototype.prevPageInstant=function(t){if(0!=this.flippedleft){for(var e=0;e<this.pages.length;e++)if(this.pages[e].flippingLeft)return;if(1==this.view){if(!this.goingToPage){if(this.isFocusedRight())return this.focusLeft(0),void this.turnPageComplete();this.focusRight(0,0)}}else 1==this.flippedleft?this.focusRight(0):this.focusBoth(0);this.pages[this.flippedleft-1].flipRightInstant(),this.flippedleft--,this.flippedright++,this.setRightIndex(this.rightIndex-2),this.updateBookPosition()}},FLIPBOOK.BookWebGL.prototype.nextPage=function(t){if(this.nextEnabled){this.clickedPage=null;for(var e=0,i=0;i<this.pages.length;i++){if(this.pages[i].flippingRight)return;this.pages[i].flipping&&e++}var o,s,n=this.pages[this.pages.length-this.flippedright],r=(this.pages[n.index-2],this.pages[n.index-3],this.pages[n.index+1]);if(r&&e<5&&r.showMat(),1==this.view){if(!this.goingToPage){if(this.isFocusedLeft())return void this.focusRight(300,0,this.turnPageComplete);this.focusLeft(600,200)}}else 1==this.flippedright?this.focusLeft(500):this.focusBoth(500);n.flipping||(o=this,void 0!==t&&!t||(s=function(t){o.loadPages(),o.turnPageComplete()}),n.flipLeft(s)),this.flippedleft++,this.flippedright--,this.setRightIndex(this.rightIndex+2)}},FLIPBOOK.BookWebGL.prototype.updateBookPosition=function(){1==this.view?0==this.flippedright||0!=this.flippedleft&&this.isFocusedLeft()?this.focusLeft():this.focusRight():0==this.rightIndex?this.focusRight():this.rightIndex>=this.options.numPages?this.focusLeft():this.focusBoth(),this.updateHtmlLayerPosition(),this.needsUpdate=!0},FLIPBOOK.BookWebGL.prototype.updateHtmlLayerPosition=function(){this.htmlLayer&&(this.htmlLayer.position.x=this.centerContainer.position.x,this.htmlLayer.position.y=this.centerContainer.position.y),this.needsUpdate=!0},FLIPBOOK.BookWebGL.prototype.turnPageComplete=function(){this.goingToPage=!1,this.options.main.turnPageComplete()},FLIPBOOK.BookWebGL.prototype.loadPages=function(){for(var t=this.pages,e=this.options.main,i=0;i<t.length;i++){if((o=t[i]).flippingLeft||o.flippingRight)return}this.options.cornerCurl&&this.pages[0]&&(0==this.flippedleft?this.pages[0].startCornerCurl():this.pages[0].stopCornerCurl());for(var o,s=this.pages[this.flippedleft],n=this.pages[this.flippedleft-1],r=(this.pages[this.flippedleft-2],this.pages[this.flippedleft+1],this.pages[this.flippedleft-3],this.pages[this.flippedleft+2],this.updateHtmlLayer),h=this.loadMorePages,i=0;i<t.length;i++){(o=t[i])!==s&&o!==n||o.showMat(),n&&o.index<n.index-2&&(o.hideMat(),this.options.pdfMode||o.disposeMat()),s&&o.index>s.index+2&&(o.hideMat(),this.options.pdfMode||o.disposeMat())}var s=this.pages[this.flippedleft],n=this.pages[this.flippedleft-1],r=(this.pages[this.flippedleft-2],this.pages[this.flippedleft+1],this.pages[this.flippedleft-3],this.pages[this.flippedleft+2],this.updateHtmlLayer),h=this.loadMorePages,a=this,t=this.pages;(e=this.options.main).setLoadingProgress(.1),n?n.load("back",function(t){s?s.load("front",function(t){n.loadHTML("back",function(){s.loadHTML("front",function(){r.call(a)})}),e.setLoadingProgress(1),h.call(a)}):(n.loadHTML("back",function(){r.call(a)}),e.setLoadingProgress(1),h.call(a))}):s.load("front",function(t){s.loadHTML("front",function(){r.call(a)}),e.setLoadingProgress(1),h.call(a)})},FLIPBOOK.BookWebGL.prototype.focusLeft=function(t,e,i){this.options.pageHeight;var o=this.options.pageWidth,s=(this.wrapperW,this.wrapperH,this.zoom,.5*o*this.centerContainer.scale.x);this.moveToPos({x:s,y:0},t,e,i)},FLIPBOOK.BookWebGL.prototype.focusRight=function(t,e,i){this.options.pageHeight;var o=this.options.pageWidth,s=(this.wrapperW,this.wrapperH,this.zoom,.5*-o*this.centerContainer.scale.x);this.moveToPos({x:s,y:0},t,e,i)},FLIPBOOK.BookWebGL.prototype.focusBoth=function(t,e,i){this.options.pageHeight,this.options.pageWidth,this.wrapperW,this.wrapperH,this.zoom;this.moveToPos({x:0,y:0},t,e,i)},FLIPBOOK.BookWebGL.prototype.moveToPos=function(t,e,i,o){var s;e&&this.movingTo!=t&&this.centerContainer.position.x!=t.x?((s=this).movingTo=t,this.bookMoveTween&&this.bookMoveTween.stop(),this.bookMoveTween=new FLIPBOOK.TWEEN.Tween(this.centerContainer.position).to({x:t.x,y:t.y},e).easing(FLIPBOOK.TWEEN.Easing.Sinusoidal.Out).onUpdate(function(){s.updateHtmlLayerPosition()}).onComplete(function(){s.movingTo=null,s.updateHtmlLayerPosition(),o&&o.call(s)}).delay(i||0).start()):(this.centerContainer.position.x=t.x,this.centerContainer.position.y=t.y,o&&o.call(this))},FLIPBOOK.BookWebGL.prototype.isFocusedLeft=function(){return 0<this.centerContainer.position.x},FLIPBOOK.BookWebGL.prototype.isFocusedRight=function(){return this.centerContainer.position.x<0},FLIPBOOK.BookWebGL.prototype.prevPage=function(t){if(this.prevEnabled){this.clickedPage=null;for(var e=0,i=0;i<this.pages.length;i++){if(this.pages[i].flippingLeft)return;this.pages[i].flipping&&e++}var o,s,n=this.pages[this.flippedleft-1],r=this.pages[n.index-1];this.pages[n.index+2],this.pages[n.index+3];if(r&&e<5&&r.showMat(),1==this.view){if(!this.goingToPage){if(this.isFocusedRight())return void this.focusLeft(300,0,this.turnPageComplete);this.focusRight(600,200)}}else 1==this.flippedleft?this.focusRight(500):this.focusBoth(500);n.flipping||(o=this,void 0!==t&&!t||(s=function(t){o.loadPages(),o.turnPageComplete()}),n.flipRight(s)),this.flippedleft--,this.flippedright++,this.setRightIndex(this.rightIndex-2)}},FLIPBOOK.BookWebGL.prototype.firstPage=function(){},FLIPBOOK.BookWebGL.prototype.flipFinnished=function(){this.needsUpdate=!0},FLIPBOOK.BookWebGL.prototype.lastPage=function(){},FLIPBOOK.BookWebGL.prototype.updateVisiblePages=function(){},FLIPBOOK.BookWebGL.prototype.loadMorePages=function(){for(var t=this.options.loadPagesF,e=this.options.loadPagesB,i=this.pages[this.flippedleft],o=this.pages[this.flippedleft-1],s=0;s<t;s++)(o=this.pages[this.flippedleft+s])&&o.load("back",function(){}),(i=this.pages[this.flippedleft+1+s])&&i.load("front",function(){});for(s=0;s<e;s++)(o=this.pages[this.flippedleft-2+s])&&o.load("back",function(){}),(i=this.pages[this.flippedleft-1+s])&&i.load("front",function(){})},FLIPBOOK.BookWebGL.prototype.updateHtmlLayer=function(){if(this.htmlLayer){for(var t=0;t<this.pages.length;t++)if(this.pages[t].flipping)return;this.htmlContentRightIndex=this.rightIndex,this.htmlLayerVisible=!1;var e,i=this.options.rightToLeft?this.options.pages.length-this.rightIndex-1:this.rightIndex,o=this.options.rightToLeft?i+1:i-1;this.options.doublePage?0==this.rightIndex?(e=this.options.pages[i].htmlContent,this.$pageL.hide(),this.$pageC.hide(),e?(this.$pageRInner.empty(),jQuery(e).appendTo(this.$pageRInner),this.$pageR.show(),this.htmlLayerVisible=!0):this.$pageR.hide()):this.rightIndex==2*this.pages.length?(e=this.options.pages[o].htmlContent,this.$pageR.hide(),this.$pageC.hide(),e?(this.$pageLInner.empty(),jQuery(e).appendTo(this.$pageLInner),this.$pageL.show(),this.htmlLayerVisible=!0):this.$pageL.hide()):(this.$pageL.hide(),this.$pageR.hide(),(e=this.options.pages[o].htmlContent||this.options.pages[i].htmlContent)?(this.$pageCInner.empty(),jQuery(e).appendTo(this.$pageCInner),this.$pageC.show(),this.htmlLayerVisible=!0):this.$pageC.hide()):(this.$pageC.hide(),0!=this.rightIndex&&this.options.pages[o].htmlContent?(this.$pageLInner.empty(),jQuery(this.options.pages[o].htmlContent).appendTo(this.$pageLInner),this.$pageL.show(),this.htmlLayerVisible=!0):this.$pageL.hide(),this.rightIndex!=2*this.pages.length&&this.options.pages[i].htmlContent?(this.$pageRInner.empty(),jQuery(this.options.pages[i].htmlContent).appendTo(this.$pageRInner),this.$pageR.show(),this.htmlLayerVisible=!0):this.$pageR.hide()),this.updateTool()}},FLIPBOOK.BookWebGL.prototype.updateTool=function(){"toolSelect"==this.options.main.tool?jQuery(".flipbook-textLayer").css("pointer-events","auto").removeClass(".flipbook-noselect"):jQuery(".flipbook-textLayer").css("pointer-events","none").addClass(".flipbook-noselect")},FLIPBOOK.BookWebGL.prototype.onZoom=function(){},FLIPBOOK.BookWebGL.prototype.render=function(t){this.rendering=t},FLIPBOOK.BookWebGL.prototype.zoomTo=function(t,e,i,o){var s,n,r,h,a,p,c,l,d,u;this.zooming||this.pages.length&&(void 0===e&&(e=0),l=this.centerContainer.position,void 0!==i&&void 0!==o&&(s=this.zoom*this.wrapper.clientHeight/1e3,n=t*this.wrapper.clientHeight/1e3,this.zoom,r=this.centerContainer.position,h=(i-this.wrapper.clientWidth/2)/s-r.x,a=(-o+this.wrapper.clientHeight/2)/s-r.y,p=(i-this.wrapper.clientWidth/2)/n-r.x,c=(-o+this.wrapper.clientHeight/2)/n-r.y,(l=r).x=r.x-(h-p),l.y=r.y-(a-c)),(d=this).options,newZoom=t>this.options.zoomMax?this.options.zoomMax:t,newZoom=t<this.options.zoomMin?this.options.zoomMin:t,newZoom==this.options.zoom&&(this.options,u=this.isFocusedLeft(),1==this.view?u?this.focusLeft():this.focusRight():this.centerContainer.position.set(0,0,0),this.updateBookPosition()),(e=0)<e?this.zooming||(this.zooming=!0,new FLIPBOOK.TWEEN.Tween(this).to({zoom:newZoom},e).easing(FLIPBOOK.TWEEN.Easing.Sinusoidal.In).onUpdate(this.updateCameraPosition).onComplete(function(){d.zooming=!1}).start(),new FLIPBOOK.TWEEN.Tween(this.centerContainer.position).to({x:l.x,y:l.y},e).easing(FLIPBOOK.TWEEN.Easing.Sinusoidal.In).onUpdate(function(){}).onComplete(function(){}).start(),this.htmlLayer&&new FLIPBOOK.TWEEN.Tween(this.htmlLayer.position).to({x:l.x,y:l.y},e).easing(FLIPBOOK.TWEEN.Easing.Sinusoidal.In).start()):(this.zoom=newZoom,this.centerContainer.position.set(l.x,l.y,0),d.updateHtmlLayerPosition(),this.updateCameraPosition(),this.zooming=!1),t<=1&&t<=this.zoom&&this.updateBookPosition(),this.options.main.onZoom(newZoom),this.loadPages())},FLIPBOOK.BookWebGL.prototype.tiltTo=function(t){var e=this.tilt+.3*t;e=(e=e>this.options.tiltMax?this.options.tiltMax:e)<this.options.tiltMin?this.options.tiltMin:e,this.tilt=e,this.updateCameraPosition()},FLIPBOOK.BookWebGL.prototype.panTo=function(t){var e=this.pan-.2*t;e=(e=e>this.options.panMax?this.options.panMax:e)<this.options.panMin?this.options.panMin:e,this.pan=e,this.updateCameraPosition()},FLIPBOOK.BookWebGL.prototype._bind=function(t,e,i){(e||this.wrapper).addEventListener(t,this,!!i)},FLIPBOOK.BookWebGL.prototype.handleEvent=function(t){var e=this;switch(t.type){case"mousedown":e._start(t);break;case"touchstart":e._touchstart(t);break;case"touchmove":e._touchmove(t);break;case"mousemove":e._move(t);break;case"mouseout":jQuery(e.wrapper).trigger("mouseup"),e.mouseDown=!1;break;case"mouseup":case"touchend":e._end(t)}},FLIPBOOK.BookWebGL.prototype.resetCameraPosition=function(){this.centerContainer.position.set(0,0,0)},FLIPBOOK.BookWebGL.prototype._start=function(t){this.mouseDown=!0,this.onMouseMove="",this.pointX=t.pageX,this.pointY=t.pageY,this.startPoint=t;var e=this._getVector(t);e.unproject(this.Camera);var i=new THREE.Raycaster(this.Camera.position,e.sub(this.Camera.position).normalize()).intersectObjects(this.pages,!0);this.pageMouseDown=0<i.length,"canvas"==t.target.nodeName.toLowerCase()&&!this.pageMouseDown&&this.options.lightBox&&this.options.lightboxCloseOnClick&&this.options.main.lightbox.closeLightbox()},FLIPBOOK.BookWebGL.prototype._touchstart=function(t){var e,i;1<t.touches.length?(this.touches=[],this.touches[0]={pageX:t.touches[0].pageX,pageY:t.touches[0].pageY},this.touches[1]={pageX:t.touches[1].pageX,pageY:t.touches[1].pageY},e=Math.abs(this.touches[0].pageX-this.touches[1].pageX),i=Math.abs(this.touches[0].pageY-this.touches[1].pageY),this.touchesDistStart=Math.sqrt(e*e+i*i)):(t=t.touches[0],this._start(t))},FLIPBOOK.BookWebGL.prototype._getVector=function(t){var e=jQuery(this.canvas).width(),i=jQuery(this.canvas).height(),o=t.pageX-jQuery(this.canvas).offset().left,s=t.pageY-jQuery(this.canvas).offset().top;jQuery(this.canvas).offset().x,jQuery(this.canvas).offset().y;return new THREE.Vector3(o/e*2-1,-s/i*2+1,.5)},FLIPBOOK.BookWebGL.prototype._touchmove=function(t){1<t.touches.length||(t=t.touches[0],this._move(t))},FLIPBOOK.BookWebGL.prototype.move=function(t){if(!(this.zoom<=1)){var e=0,i=0;switch(t){case"left":e=20;break;case"right":e=-20;break;case"up":i=20;break;case"down":i=-20}this.centerContainer.position.x+=1e4*e/(this.cameraZ*this.zoom*this.zoom),this.centerContainer.position.y-=1e4*i/(this.cameraZ*this.zoom*this.zoom),this.updateHtmlLayerPosition()}},FLIPBOOK.BookWebGL.prototype._move=function(t){var e=this._getVector(t);e.unproject(this.Camera);var i,o,s=new THREE.Raycaster(this.Camera.position,e.sub(this.Camera.position).normalize()).intersectObjects(this.pages,!0),n=t,r=.5*(n.pageX-this.pointX),h=.5*(n.pageY-this.pointY);if(this.pointX=n.pageX,this.pointY=n.pageY,!this.mouseDown)return this.onMouseMove="",void(this.options.rotateCameraOnMouseMove&&(this.tilt=this.options.tiltMin2+(this.options.tiltMax2-this.options.tiltMin2)*(1-this.pointY/jQuery(this.canvas).height()),this.pan=this.options.panMin2+(this.options.panMax2-this.options.panMin2)*this.pointX/jQuery(this.canvas).width(),this.updateCameraPosition()));if(0<s.length?""==this.onMouseMove&&1<this.zoom&&(this.onMouseMove="scroll"):""==this.onMouseMove&&(this.onMouseMove="rotate"),"scroll"==this.onMouseMove){if("toolSelect"==this.options.main.tool)return;0==r&&0==h||(this.moved=!0,this.centerContainer.position.x+=1e4*r/(this.cameraZ*this.zoom*this.zoom),this.centerContainer.position.y-=1e4*h/(this.cameraZ*this.zoom*this.zoom)),this.updateHtmlLayerPosition()}else{"rotate"==this.onMouseMove&&(i=this.getRightPage(),o=this.getLeftPage(),this.options.rotateCameraOnMouseMove||!this.options.rotateCameraOnMouseDrag||o&&o.dragging||i&&i.dragging||this.tiltTo(h))}},FLIPBOOK.BookWebGL.prototype._end=function(t){this.mouseDown=!1,void 0!==t.changedTouches&&(t=t.changedTouches[0]),this.pointX=t.pageX,this.pointY=t.pageY,this.endPoint=t;var e=this._getVector(this.endPoint);e.unproject(this.Camera);var i,o=new THREE.Raycaster(this.Camera.position,e.sub(this.Camera.position).normalize()).intersectObjects(this.pages,!0);0<o.length&&this.pageMouseDown&&!this.moved&&(i=o[0],this.clickedPage=i.object.parent),this.pageMouseDown=!1,this.moved=!1},FLIPBOOK.BookWebGL.prototype.moveCamera=function(t,e){},FLIPBOOK.BookWebGL.prototype.enable=function(){this.enabled||(this.enabled=!0,this.initialized||(this.init3d(),this.createPages(),this.rendering=!1,this.onResize()),this.render(!0)),this.onResize()},FLIPBOOK.BookWebGL.prototype.disable=function(){this.enabled=!1,this.render(!1)};var MOD3=MOD3||{};!function(t){t.Constants={PI:Math.PI,invPI:1/Math.PI,halfPI:.5*Math.PI,doublePI:2*Math.PI,toRad:1/180*Math.PI,toDeg:1/180*Math.PI},t.ModConstant={LEFT:-1,RIGHT:1,NONE:0,X:1,Y:2,Z:4}}(MOD3),function(o){var e=o.Constants;o.XMath={},o.XMath.normalize=function(t,e,i){return e-t==0?1:o.XMath.trim(0,1,(i-t)/e)},o.XMath.toRange=function(t,e,i){return e-t==0?0:t+(e-t)*i},o.XMath.inRange=function(t,e,i,o){return void 0===o&&(o=!1),o?t<=i&&i<=e:t<i&&i<e},o.XMath.sign=function(t,e){return void 0===e&&(e=0),0==t?e:0<t?1:-1},o.XMath.trim=function(t,e,i){return Math.min(e,Math.max(t,i))},o.XMath.wrap=function(t,e,i){return i<t?i+(e-t):e<=i?i-(e-t):i},o.XMath.degToRad=function(t){return t*e.toRad},o.XMath.radToDeg=function(t){return t*e.toDeg},o.XMath.presicion=function(t,e){var i=Math.pow(10,e);return Math.round(t*i)/i},o.XMath.uceil=function(t){return t<0?Math.floor(t):Math.ceil(t)}}(MOD3),function(e){e.Range=function(t,e){this.start=0,this.end=1,void 0!==t&&(this.start=t),void 0!==e&&(this.end=e)},e.Range.prototype.getSize=function(){return this.end-this.start},e.Range.prototype.move=function(t){this.start+=t,this.end+=t},e.Range.prototype.isIn=function(t){return t>=this.start&&t<=this.end},e.Range.prototype.normalize=function(t){return e.XMath.normalize(this.start,this.end,t)},e.Range.prototype.toRange=function(t){return e.XMath.toRange(this.start,this.end,t)},e.Range.prototype.trim=function(t){return e.XMath.trim(this.start,this.end,t)},e.Range.prototype.interpolate=function(t,e){return this.toRange(e.normalize(t))},e.Range.prototype.toString=function(){return"["+this.start+" - "+this.end+"]"}}(MOD3),function(t){t.Phase=function(t){void(this.value=0)!==t&&(this.value=t)},t.Phase.prototype.getPhasedValue=function(){return Math.sin(this.value)},t.Phase.prototype.getAbsPhasedValue=function(){return Math.abs(this.getPhasedValue())},t.Phase.prototype.getNormValue=function(){return.5*(this.getPhasedValue()+1)}}(MOD3),function(t){t.Point=function(t,e){this.y=this.x=0,void 0!==t&&(this.x=t),void 0!==e&&(this.y=e)},t.Point.prototype.clone=function(){return new t.Point(this.x,this.y)}}(MOD3),function(e){e.Matrix=function(t,e,i,o){this.m11=1,this.m21=this.m12=0,this.m22=1,void 0!==t&&(this.m11=t),void 0!==e&&(this.m12=e),void 0!==i&&(this.m21=i),void 0!==o&&(this.m22=o)},e.Matrix.prototype.rotate=function(t){var e=Math.cos(t),t=Math.sin(t);return this.m11=e,this.m12=-t,this.m21=t,this.m22=e,this},e.Matrix.prototype.scale=function(t,e){return this.m21=this.m12=0,void 0!==t&&(this.m22=this.m11=t),void 0!==e&&(this.m22=e),this},e.Matrix.prototype.multiply=function(t){var e=this.m11,i=this.m12,o=this.m21,s=this.m22,n=t.m11,r=t.m12,h=t.m21,t=t.m22;return this.m11=e*n+i*h,this.m12=e*r+i*t,this.m21=o*n+s*h,this.m22=o*r+s*t,this},e.Matrix.prototype.transformPoint=function(t){return new e.Point(this.m11*t.x+this.m12*t.y,this.m21*t.x+this.m22*t.y)}}(MOD3),function(a){a.Vector3=function(t,e,i){this.z=this.y=this.x=null,this.x=t,this.y=e,this.z=i},a.Vector3.ZERO=function(){return new a.Vector3(0,0,0)},a.Vector3.dot=function(t,e){return t.x*e.x+t.y*e.y+t.z*e.z},a.Vector3.prototype.clone=function(){return new a.Vector3(this.x,this.y,this.z)},a.Vector3.prototype.equals=function(t){return this.x==t.x&&this.y==t.y&&this.z==t.z},a.Vector3.prototype.zero=function(){this.x=this.y=this.z=0},a.Vector3.prototype.negate=function(){return new a.Vector3(-this.x,-this.y,-this.z)},a.Vector3.prototype.add=function(t){return new a.Vector3(this.x+t.x,this.y+t.y,this.z+t.z)},a.Vector3.prototype.subtract=function(t){return new a.Vector3(this.x-t.x,this.y-t.y,this.z-t.z)},a.Vector3.prototype.multiplyScalar=function(t){return new a.Vector3(this.x*t,this.y*t,this.z*t)},a.Vector3.prototype.multiply=function(t){return new a.Vector3(this.x*t.x,this.y*t.y,this.z*t.z)},a.Vector3.prototype.divide=function(t){return t=1/t,new a.Vector3(this.x*t,this.y*t,this.z*t)},a.Vector3.prototype.normalize=function(){var t=this.x,e=this.y,i=this.z;0<(t=t*t+e*e+i*i)&&(t=1/Math.sqrt(t),this.x*=t,this.y*=t,this.z*=t)},a.Vector3.prototype.getMagnitude=function(){var t=this.x,e=this.y,i=this.z;return Math.sqrt(t*t+e*e+i*i)},a.Vector3.prototype.setMagnitude=function(t){this.normalize(),this.x*=t,this.y*=t,this.z*=t},a.Vector3.prototype.toString=function(){return"["+this.x+" , "+this.y+" , "+this.z+"]"},a.Vector3.prototype.sum=function(t,e){return t.add(e)},a.Vector3.prototype.dot=function(t,e){return t.x*e.x+t.y*e.y+t.z*e.z},a.Vector3.prototype.cross=function(t,e){var i=t.x,o=t.y,s=t.z,n=e.x,r=e.y,h=e.z;return new a.Vector3(o*h-s*r,s*n-i*h,i*r-o*n)},a.Vector3.prototype.distance=function(t,e){var i=t.x-e.x,o=t.y-e.y,s=t.z-e.z;return Math.sqrt(i*i+o*o+s*s)}}(MOD3),function(t){t.Matrix4=function(t,e,i,o,s,n,r,h,a,p,c,l,d,u,g,f){this.n11=1,this.n21=this.n14=this.n13=this.n12=0,this.n22=1,this.n32=this.n31=this.n24=this.n23=0,this.n33=1,this.n43=this.n42=this.n41=this.n34=0,this.n44=1,void 0!==t&&(this.n11=t),void 0!==e&&(this.n12=e),void 0!==i&&(this.n13=i),void 0!==o&&(this.n14=o),void 0!==s&&(this.n21=s),void 0!==n&&(this.n22=n),void 0!==r&&(this.n23=r),void 0!==h&&(this.n24=h),void 0!==a&&(this.n31=a),void 0!==p&&(this.n32=p),void 0!==c&&(this.n33=c),void 0!==l&&(this.n34=l),void 0!==d&&(this.n41=d),void 0!==u&&(this.n42=u),void 0!==g&&(this.n43=g),void 0!==f&&(this.n44=f)},t.Matrix4.prototype.translationMatrix=function(t,e,i){return this.n14=t,this.n24=e,this.n34=i,this},t.Matrix4.prototype.scaleMatrix=function(t,e,i){return this.n11=t,this.n22=e,this.n33=i,this},t.Matrix4.prototype.rotationMatrix=function(t,e,i,o){var s=Math.cos(o),n=Math.sin(o),r=t*e*(o=1-s),h=e*i*o,a=t*i*o,p=n*i,c=n*e;return n*=t,this.n11=s+t*t*o,this.n12=r-p,this.n13=c+a,this.n14=0,this.n21=p+r,this.n22=s+e*e*o,this.n23=h-n,this.n24=0,this.n31=a-c,this.n32=n+h,this.n33=s+i*i*o,this.n34=0,this},t.Matrix4.prototype.calculateMultiply=function(t,e){var i=t.n11,o=e.n11,s=t.n21,n=e.n21,r=t.n31,h=e.n31,a=t.n12,p=e.n12,c=t.n22,l=e.n22,d=t.n32,u=e.n32,g=t.n13,f=e.n13,m=t.n23,y=e.n23,x=t.n33,M=e.n33,P=t.n14,v=e.n14,L=t.n24,O=e.n24,b=t.n34,w=e.n34;this.n11=i*o+a*n+g*h,this.n12=i*p+a*l+g*u,this.n13=i*f+a*y+g*M,this.n14=i*v+a*O+g*w+P,this.n21=s*o+c*n+m*h,this.n22=s*p+c*l+m*u,this.n23=s*f+c*y+m*M,this.n24=s*v+c*O+m*w+L,this.n31=r*o+d*n+x*h,this.n32=r*p+d*l+x*u,this.n33=r*f+d*y+x*M,this.n34=r*v+d*O+x*w+b},t.Matrix4.prototype.multiply=function(t,e){return this.calculateMultiply(t,e),this},t.Matrix4.prototype.multiplyVector=function(t,e){var i=e.x,o=e.y,s=e.z;e.x=i*t.n11+o*t.n12+s*t.n13+t.n14,e.y=i*t.n21+o*t.n22+s*t.n23+t.n24,e.z=i*t.n31+o*t.n32+s*t.n33+t.n34}}(MOD3),function(i){i.VertexProxy=function(t){this.originalZ=this.originalY=this.originalX=this.ratioZ=this.ratioY=this.ratioX=null,void 0!==t&&(this.vertex=t)},i.VertexProxy.prototype.setVertex=function(){},i.VertexProxy.prototype.setRatios=function(t,e,i){this.ratioX=t,this.ratioY=e,this.ratioZ=i},i.VertexProxy.prototype.setOriginalPosition=function(t,e,i){this.originalX=t,this.originalY=e,this.originalZ=i},i.VertexProxy.prototype.getX=function(){},i.VertexProxy.prototype.getY=function(){},i.VertexProxy.prototype.getZ=function(){},i.VertexProxy.prototype.setX=function(){},i.VertexProxy.prototype.setY=function(){},i.VertexProxy.prototype.setZ=function(){},i.VertexProxy.prototype.getValue=function(t){switch(t){case i.ModConstant.X:return this.getX();case i.ModConstant.Y:return this.getY();case i.ModConstant.Z:return this.getZ()}return 0},i.VertexProxy.prototype.setValue=function(t,e){switch(t){case i.ModConstant.X:this.setX(e);break;case i.ModConstant.Y:this.setY(e);break;case i.ModConstant.Z:this.setZ(e)}},i.VertexProxy.prototype.getRatio=function(t){switch(t){case i.ModConstant.X:return this.ratioX;case i.ModConstant.Y:return this.ratioY;case i.ModConstant.Z:return this.ratioZ}return-1},i.VertexProxy.prototype.getOriginalValue=function(t){switch(t){case i.ModConstant.X:return this.originalX;case i.ModConstant.Y:return this.originalY;case i.ModConstant.Z:return this.originalZ}return 0},i.VertexProxy.prototype.reset=function(){this.setX(this.originalX),this.setY(this.originalY),this.setZ(this.originalZ)},i.VertexProxy.prototype.collapse=function(){this.originalX=this.getX(),this.originalY=this.getY(),this.originalZ=this.getZ()},i.VertexProxy.prototype.getVector=function(){return new i.Vector3(this.getX(),this.getY(),this.getZ())},i.VertexProxy.prototype.setVector=function(t){this.setX(t.x),this.setY(t.y),this.setZ(t.z)},i.VertexProxy.prototype.getRatioVector=function(){return new i.Vector3(this.ratioX,this.ratioY,this.ratioZ)}}(MOD3),function(t){t.FaceProxy=function(){this.vertices=[]},t.FaceProxy.prototype.addVertex=function(t){this.vertices.push(t)},t.FaceProxy.prototype.getVertices=function(){return this.vertices}}(MOD3),function(m){m.MeshProxy=function(){this.depth=this.height=this.width=this.minAxis=this.midAxis=this.maxAxis=this.minZ=this.minY=this.minX=this.maxZ=this.maxY=this.maxX=null,this.vertices=[],this.faces=[],this.mesh=null},m.MeshProxy.prototype.getVertices=function(){return this.vertices},m.MeshProxy.prototype.getFaces=function(){return this.faces},m.MeshProxy.prototype.analyzeGeometry=function(){for(var t,e,i,o,s,n,r,h,a,p,c=this.getVertices(),l=c.length,d=l,u=!0,g=Math.min,f=Math.max;0<=--d;)e=(t=c[d]).getX(),i=t.getY(),o=t.getZ(),u?(s=n=e,r=h=i,a=p=o,u=!1):(s=g(s,e),r=g(r,i),a=g(a,o),n=f(n,e),h=f(h,i),p=f(p,o)),t.setOriginalPosition(e,i,o);for(e=n-s,i=h-r,depth=p-a,this.width=e,this.height=i,this.depth=depth,this.minX=s,this.maxX=n,this.minY=r,this.maxY=h,this.minZ=a,this.maxZ=p,d=f(e,f(i,depth)),g=g(e,g(i,depth)),d==e&&g==i?(this.minAxis=m.ModConstant.Y,this.midAxis=m.ModConstant.Z,this.maxAxis=m.ModConstant.X):d==e&&g==depth?(this.minAxis=m.ModConstant.Z,this.midAxis=m.ModConstant.Y,this.maxAxis=m.ModConstant.X):d==i&&g==e?(this.minAxis=m.ModConstant.X,this.midAxis=m.ModConstant.Z,this.maxAxis=m.ModConstant.Y):d==i&&g==depth?(this.minAxis=m.ModConstant.Z,this.midAxis=m.ModConstant.X,this.maxAxis=m.ModConstant.Y):d==depth&&g==e?(this.minAxis=m.ModConstant.X,this.midAxis=m.ModConstant.Y,this.maxAxis=m.ModConstant.Z):d==depth&&g==i&&(this.minAxis=m.ModConstant.Y,this.midAxis=m.ModConstant.X,this.maxAxis=m.ModConstant.Z),d=l;0<=--d;)(t=c[d]).setRatios((t.getX()-s)/e,(t.getY()-r)/i,(t.getZ()-a)/depth)},m.MeshProxy.prototype.resetGeometry=function(){for(var t=this.getVertices(),e=t.length;0<=--e;)t[e].reset()},m.MeshProxy.prototype.collapseGeometry=function(){for(var t=this.getVertices(),e=t.length;0<=--e;)t[e].collapse();this.analyzeGeometry()},m.MeshProxy.prototype.getMin=function(t){switch(t){case m.ModConstant.X:return this.minX;case m.ModConstant.Y:return this.minY;case m.ModConstant.Z:return this.minZ}return-1},m.MeshProxy.prototype.getMax=function(t){switch(t){case m.ModConstant.X:return this.maxX;case m.ModConstant.Y:return this.maxY;case m.ModConstant.Z:return this.maxZ}return-1},m.MeshProxy.prototype.getSize=function(t){switch(t){case m.ModConstant.X:return this.width;case m.ModConstant.Y:return this.height;case m.ModConstant.Z:return this.depth}return-1},m.MeshProxy.prototype.setMesh=function(t){this.mesh=t,this.vertices=[],this.faces=[]},m.MeshProxy.prototype.postApply=function(){},m.MeshProxy.prototype.updateMeshPosition=function(){}}(MOD3),function(t){t.Modifier=function(){this.mod=null},t.Modifier.prototype.setModifiable=function(t){this.mod=t},t.Modifier.prototype.getVertices=function(){return this.mod.getVertices()},t.Modifier.prototype.apply=function(){}}(MOD3),MOD3.Library3d=function(){this.id="",this.vertexClass=this.meshClass=null},function(t){t.PluginFactory={},t.PluginFactory.getMeshProxy=function(t){return new t.meshClass}}(MOD3),function(i){i.ModifierStack=function(t,e){this.lib3d=t,this.stack=this.baseMesh=null,this.baseMesh=i.PluginFactory.getMeshProxy(t),this.baseMesh.setMesh(e),this.baseMesh.analyzeGeometry(),this.stack=[]},i.ModifierStack.prototype.addModifier=function(t){t.setModifiable(this.baseMesh),this.stack.push(t)},i.ModifierStack.prototype.apply=function(){this.baseMesh.resetGeometry();for(var t=this.stack,e=t.length,i=0;i<e;)t[i++].apply();this.baseMesh.postApply()},i.ModifierStack.prototype.collapse=function(){this.apply(),this.baseMesh.collapseGeometry(),this.stack=[]},i.ModifierStack.prototype.clear=function(){this.stack=[]},i.ModifierStack.prototype.getMeshInfo=function(){return this.baseMesh}}(MOD3),function(o){o.Pivot=function(t,e,i){this.pivot=new o.Vector3(t,e,i)},o.Pivot.prototype=new o.Modifier,o.Pivot.prototype.constructor=o.Pivot,o.Pivot.prototype.setMeshCenter=function(){var t=this.mod;this.pivot=new o.Vector3(-(t.minX+.5*t.width),-(t.minY+.5*t.height),-(t.minZ+.5*t.depth))},o.Pivot.prototype.apply=function(){for(var t,e,i=this.mod.getVertices(),o=i.length,s=this.pivot;0<=--o;)e=(t=i[o]).getVector().clone(),t.setVector(e.add(s));this.mod.updateMeshPosition(s.clone().negate())}}(MOD3),function(v){v.Bend=function(t,e,i){this.diagAngle=this.angle=this.offset=this.force=null,this.constraint=v.ModConstant.NONE,this.m2=this.m1=this.origin=this.height=this.width=this.mid=this.min=this.max=null,this.switchAxes=!1,this.force=t,this.offset=e,this.setAngle(i)},v.Bend.prototype=new v.Modifier,v.Bend.prototype.constructor=v.Bend,v.Bend.prototype.setAngle=function(t){this.angle=t,this.m1=new v.Matrix,this.m1.rotate(t),this.m2=new v.Matrix,this.m2.rotate(-t)},v.Bend.prototype.setModifiable=function(t){v.Modifier.prototype.setModifiable.call(this,t),this.max=this.switchAxes?this.mod.midAxis:this.mod.maxAxis,this.min=this.mod.minAxis,this.mid=this.switchAxes?this.mod.maxAxis:this.mod.midAxis,this.width=this.mod.getSize(this.max),this.height=this.mod.getSize(this.mid),this.origin=this.mod.getMin(this.max),this.diagAngle=Math.atan(this.width/this.height)},v.Bend.prototype.apply=function(){if(0!=this.force)for(var t,e,i,o,s=this.mod.getVertices(),n=s.length,r=this.width,h=this.offset,a=this.origin,p=this.max,c=this.min,l=this.mid,d=this.m1,u=this.m2,g=a+r*h,f=r/Math.PI/this.force,m=v.Constants.doublePI*(r/(f*v.Constants.doublePI)),y=1/r,x=v.Constants.halfPI,M=Math.sin,P=Math.cos;0<=--n;)t=(r=s[n]).getValue(p),e=r.getValue(l),i=r.getValue(c),t=(e=d.transformPoint(new v.Point(t,e))).x,e=e.y,o=(t-a)*y,this.constraint==v.ModConstant.LEFT&&o<=h||this.constraint==v.ModConstant.RIGHT&&h<=o||(t=M(o=x-m*h+m*o)*(f+i),o=P(o)*(f+i),i=t-f,t=g-o),t=(e=u.transformPoint(new v.Point(t,e))).x,e=e.y,r.setValue(p,t),r.setValue(l,e),r.setValue(c,i)}}(MOD3),function(t){t.Bloat=function(){this.center=t.Vector3.ZERO(),this.radius=0,this.a=.01,this.u=t.Vector3.ZERO()},t.Bloat.prototype=new t.Modifier,t.Bloat.prototype.constructor=t.Bloat,t.Bloat.prototype.setRadius=function(t){this.radius=Math.max(0,t)},t.Bloat.prototype.setA=function(t){this.a=Math.max(0,t)},t.Bloat.prototype.apply=function(){for(var t,e,i=this.mod.getVertices(),o=i.length,s=this.center,n=this.radius,r=this.a;0<=--o;)t=i[o],this.u.x=t.getX()-s.x,this.u.y=t.getY()-s.y,this.u.z=t.getZ()-s.z,e=this.u.getMagnitude(),this.u.setMagnitude(e+n*Math.exp(-e*r)),t.setX(this.u.x+s.x),t.setY(this.u.y+s.y),t.setZ(this.u.z+s.z)}}(MOD3),function(h){h.Twist=function(t){this.vector=new h.Vector3(0,1,0),this.angle=t,this.center=h.Vector3.ZERO()},h.Twist.prototype=new h.Modifier,h.Twist.prototype.constructor=h.Twist,h.Twist.prototype.apply=function(){this.vector.normalize();for(var t,e=(r=this.mod).getVertices(),i=e.length,o=this.vector,s=this.angle,n=this.center,r=1/new h.Vector3(.5*r.maxX,.5*r.maxY,.5*r.maxZ).getMagnitude()*s,n=-h.Vector3.dot(o,n);0<=--i;)t=(s=e[i]).getX()*o.x+s.getY()*o.y+s.getZ()*o.z+n,this.twistPoint(s,t*r)},h.Twist.prototype.twistPoint=function(t,e){var i=(new h.Matrix4).translationMatrix(t.getX(),t.getY(),t.getZ()),i=(new h.Matrix4).multiply((new h.Matrix4).rotationMatrix(this.vector.x,this.vector.y,this.vector.z,e),i);t.setX(i.n14),t.setY(i.n24),t.setZ(i.n34)}}(MOD3),function(f){f.Skew=function(t){this.force=0,this.skewAxis=null,void 0!==t&&(this.force=t),this.offset=.5,this.constraint=f.ModConstant.NONE,this.falloff=this.power=1,this.swapAxes=this.oneSide=this.inverseFalloff=!1},f.Skew.prototype=new f.Modifier,f.Skew.prototype.constructor=f.Skew,f.Skew.prototype.setModifiable=function(t){f.Modifier.prototype.setModifiable.call(this,t),this.skewAxis=this.skewAxis||this.mod.maxAxis},f.Skew.prototype.apply=function(){for(var t,e,i,o=this.mod.getVertices(),s=o.length,n=this.constraint,r=this.skewAxis,h=this.offset,a=this.oneSide,p=this.inverseFalloff,c=this.falloff,l=1-c,d=this.power,u=this.force,g=this.getDisplaceAxis();0<=--s;)t=o[s],n==f.ModConstant.LEFT&&t.getRatio(r)<=h||n==f.ModConstant.RIGHT&&t.getRatio(r)>h||(e=t.getRatio(r)-h,a&&(e=Math.abs(e)),i=t.getRatio(g),p&&(i=1-i),i=c+i*l,e=Math.pow(Math.abs(e),d)*f.XMath.sign(e,1),e=t.getValue(g)+u*e*i,t.setValue(g,e))},f.Skew.prototype.getDisplaceAxis=function(){switch(this.skewAxis){case f.ModConstant.X:return this.swapAxes?f.ModConstant.Z:f.ModConstant.Y;case f.ModConstant.Y:return this.swapAxes?f.ModConstant.Z:f.ModConstant.X;case f.ModConstant.Z:return this.swapAxes?f.ModConstant.Y:f.ModConstant.X;default:return 0}}}(MOD3),function(p){p.Taper=function(t){this.power=this.force=null,this.start=0,this.end=1,this.vector=new p.Vector3(1,0,1),this.vector2=new p.Vector3(0,1,0),void 0!==t&&(this.force=t),this.power=1},p.Taper.prototype=new p.Modifier,p.Taper.prototype.constructor=p.Taper,p.Taper.prototype.setFalloff=function(t,e){this.start=0,this.end=1,void 0!==t&&(this.start=t),void 0!==e&&(this.end=e)},p.Taper.prototype.apply=function(){for(var t,e,i,o=this.mod.getVertices(),s=o.length,n=this.vector,r=this.vector2,h=this.force,a=this.power;0<=--s;)e=(t=o[s]).getRatioVector().multiply(r),e=h*Math.pow(e.getMagnitude(),a),e=(new p.Matrix4).scaleMatrix(1+e*n.x,1+e*n.y,1+e*n.z),i=t.getVector(),(new p.Matrix4).multiplyVector(e,i),t.setVector(i)}}(MOD3),function(h){h.Wheel=function(){this.radius=this.roll=this.turn=this.speed=null,this.steerVector=new h.Vector3(0,1,0),this.rollVector=new h.Vector3(0,0,1),this.roll=this.turn=this.speed=0},h.Wheel.prototype=new h.Modifier,h.Wheel.prototype.constructor=h.Wheel,h.Wheel.prototype.setModifiable=function(t){h.Modifier.prototype.setModifiable.call(this,t),this.radius=.5*this.mod.width},h.Wheel.prototype.apply=function(){this.roll+=this.speed;for(var t,e=this.mod.getVertices(),i=e.length,o=this.steerVector,s=this.turn,n=this.rollVector,r=this.roll,r=0!=s?(t=(new h.Matrix4).rotationMatrix(o.x,o.y,o.z,s),o=n.clone(),(new h.Matrix4).multiplyVector(t,o),(new h.Matrix4).rotationMatrix(o.x,o.y,o.z,r)):(new h.Matrix4).rotationMatrix(n.x,n.y,n.z,r);0<=--i;)n=(o=e[i]).getVector().clone(),0!=s&&(new h.Matrix4).multiplyVector(t,n),(new h.Matrix4).multiplyVector(r,n),o.setX(n.x),o.setY(n.y),o.setZ(n.z)},h.Wheel.prototype.getStep=function(){return this.radius*this.speed*h.Constants.invPI},h.Wheel.prototype.getPerimeter=function(){return this.radius*h.Constants.doublePI}}(MOD3),function(a){a.Break=function(t,e){this.bv=new a.Vector3(0,1,0),this.angle=this.offset=0,void 0!==t&&(this.offset=t),void 0!==e&&(this.angle=e),this.range=new a.Range(0,1)},a.Break.prototype=new a.Modifier,a.Break.prototype.constructor=a.Break,a.Break.prototype.apply=function(){for(var t=this.mod,e=t.getVertices(),i=e.length,o=this.range,s=this.angle,n=this.bv,r=(t=new a.Vector3(0,0,-(t.minZ+t.depth*this.offset))).negate(),h=(new a.Matrix4).rotationMatrix(n.x,n.y,n.z,s);0<=--i;)0<=(n=(n=(s=e[i]).getVector()).add(t)).z&&o.isIn(s.ratioY)&&(new a.Matrix4).multiplyVector(h,n),n=n.add(r),s.setX(n.x),s.setY(n.y),s.setZ(n.z)}}(MOD3),function(e){e.Noise=function(t){this.force=0,this.axc=e.ModConstant.NONE,this.end=this.start=0,void 0!==t&&(this.force=t)},e.Noise.prototype=new e.Modifier,e.Noise.prototype.constructor=e.Noise,e.Noise.prototype.constraintAxes=function(t){this.axc=t},e.Noise.prototype.setFalloff=function(t,e){this.start=0,this.end=1,void 0!==t&&(this.start=t),void 0!==e&&(this.end=e)},e.Noise.prototype.apply=function(){for(var t,e,i,o=this.mod,s=this.axc,n=this.start,r=this.end,h=o.getVertices(),a=h.length,p=this.force,c=.5*p,l=Math.random;0<=--a;)t=h[a],e=l()*p-c,i=t.getRatio(o.maxAxis),n<r?(i<n&&(i=0),r<i&&(i=1)):r<n?(n<(i=1-i)&&(i=0),i<r&&(i=1)):i=1,1&s||t.setX(t.getX()+e*i),s>>1&1||t.setY(t.getY()+e*i),s>>2&1||t.setZ(t.getZ()+e*i)}}(MOD3),function(t){t.LibraryThree=function(){this.id="Three.js",this.meshClass=t.MeshThree,this.vertexClass=t.VertexThree},t.LibraryThree.prototype=new t.Library3d,t.LibraryThree.prototype.constructor=t.LibraryThree}(MOD3),function(t){t.VertexThree=function(t){this.mesh=t},t.VertexThree.prototype=new t.VertexProxy,t.VertexThree.prototype.setVertex=function(t){this.vertex=t,this.originalX=t.x,this.originalY=t.y,this.originalZ=t.z},t.VertexThree.prototype.getX=function(){return this.vertex.x},t.VertexThree.prototype.getY=function(){return this.vertex.y},t.VertexThree.prototype.getZ=function(){return this.vertex.z},t.VertexThree.prototype.setX=function(t){this.vertex.x=t,(t=this.mesh).geometry.verticesNeedUpdate=!0,t.geometry.normalsNeedUpdate=!0,t.geometry.buffersNeedUpdate=!0,t.geometry.dynamic=!0},t.VertexThree.prototype.setY=function(t){this.vertex.y=t,(t=this.mesh).geometry.verticesNeedUpdate=!0,t.geometry.normalsNeedUpdate=!0,t.geometry.buffersNeedUpdate=!0,t.geometry.dynamic=!0},t.VertexThree.prototype.setZ=function(t){this.vertex.z=t,(t=this.mesh).geometry.verticesNeedUpdate=!0,t.geometry.normalsNeedUpdate=!0,t.geometry.buffersNeedUpdate=!0,t.geometry.dynamic=!0}}(MOD3),function(h){h.MeshThree=function(){},h.MeshThree.prototype=new h.MeshProxy,h.MeshThree.prototype.setMesh=function(t){h.MeshProxy.prototype.setMesh.call(this,t);for(var e,t=[],i=0,o=this.mesh.geometry.vertices,s=o.length,n=this.mesh.geometry.faces,r=n.length,i=0;i<s;)(e=new h.VertexThree(this.mesh)).setVertex(o[i]),this.vertices.push(e),t[o[i]]=e,i++;for(i=0;i<r;)s=new h.FaceProxy,n[i]instanceof THREE.Face3?(s.addVertex(t[o[n[i].a]]),s.addVertex(t[o[n[i].b]]),s.addVertex(t[o[n[i].c]])):n[i]instanceof THREE.Face4&&(s.addVertex(t[o[n[i].a]]),s.addVertex(t[o[n[i].b]]),s.addVertex(t[o[n[i].c]]),s.addVertex(t[o[n[i].d]])),this.faces.push(s),i++;delete lookup},h.MeshThree.prototype.updateMeshPosition=function(t){var e=this.mesh;e.position.x+=t.x,e.position.y+=t.y,e.position.z+=t.z}}(MOD3),function(t){t.LibraryPre3D=function(){this.id="pre3d.js",this.meshClass=t.MeshPre3D,this.vertexClass=t.VertexPre3D},t.LibraryThree.prototype=new t.Library3d,t.LibraryThree.prototype.constructor=t.LibraryPre3D}(MOD3),function(t){t.VertexPre3D=function(){},t.VertexPre3D.prototype=new t.VertexProxy,t.VertexPre3D.prototype.setVertex=function(t){this.vertex=t,this.originalX=t.x,this.originalY=t.y,this.originalZ=t.z},t.VertexPre3D.prototype.getX=function(){return this.vertex.x},t.VertexPre3D.prototype.getY=function(){return this.vertex.y},t.VertexPre3D.prototype.getZ=function(){return this.vertex.z},t.VertexPre3D.prototype.setX=function(t){this.vertex.x=t},t.VertexPre3D.prototype.setY=function(t){this.vertex.y=t},t.VertexPre3D.prototype.setZ=function(t){this.vertex.z=t}}(MOD3),function(h){h.MeshPre3D=function(){},h.MeshPre3D.prototype=new h.MeshProxy,h.MeshPre3D.prototype.setMesh=function(t){h.MeshProxy.prototype.setMesh.call(this,t);for(var t=[],e=this.mesh.vertices,i=this.mesh.quads,o=e.length,s=i.length,n=0;n<o;n++){var r=new h.VertexPre3D;r.setVertex(e[n]),this.vertices.push(r),t[e[n]]=r}for(n=0;n<s;n++)o=new h.FaceProxy,i[n]instanceof Pre3d.QuadFace&&(o.addVertex(t[e[i[n].i0]]),o.addVertex(t[e[i[n].i1]]),o.addVertex(t[e[i[n].i2]]),null!=i[n].i3&&o.addVertex(t[e[i[n].i3]])),this.faces.push(o);delete lookup},h.MeshPre3D.prototype.updateMeshPosition=function(){}}(MOD3),function(t){t.LibraryJ3D=function(){this.id="J3D",this.meshClass=t.MeshJ3D,this.vertexClass=t.VertexJ3D},t.LibraryJ3D.prototype=new t.Library3d,t.LibraryJ3D.prototype.constructor=t.LibraryJ3D}(MOD3),function(t){t.VertexJ3D=function(t){this.geometry=t},t.VertexJ3D.prototype=new t.VertexProxy,t.VertexJ3D.prototype.setVertex=function(t){this.vertex=t;var e=this.geometry;this.originalX=e.vertexPositionBuffer.data[t],this.originalY=e.vertexPositionBuffer.data[t+1],this.originalZ=e.vertexPositionBuffer.data[t+2]},t.VertexJ3D.prototype.getX=function(){return this.geometry.vertexPositionBuffer.data[this.vertex]},t.VertexJ3D.prototype.getY=function(){return this.geometry.vertexPositionBuffer.data[this.vertex+1]},t.VertexJ3D.prototype.getZ=function(){return this.geometry.vertexPositionBuffer.data[this.vertex+2]},t.VertexJ3D.prototype.setX=function(t){var e=this.geometry;e.vertexPositionBuffer.data[this.vertex]=t,e.replaceArray(e.vertexPositionBuffer,e.vertexPositionBuffer.data)},t.VertexJ3D.prototype.setY=function(t){var e=this.geometry;e.vertexPositionBuffer.data[this.vertex+1]=t,e.replaceArray(e.vertexPositionBuffer,e.vertexPositionBuffer.data)},t.VertexJ3D.prototype.setZ=function(t){var e=this.geometry;e.vertexPositionBuffer.data[this.vertex+2]=t,e.replaceArray(e.vertexPositionBuffer,e.vertexPositionBuffer.data)}}(MOD3),function(n){n.MeshJ3D=function(){},n.MeshJ3D.prototype=new n.MeshProxy,n.MeshJ3D.prototype.setMesh=function(t){n.MeshProxy.prototype.setMesh.call(this,t);for(var e,i=0,o=t.geometry.vertexPositionBuffer.data.length,s=t.geometry.vertexPositionBuffer.itemSize,i=0;i<o;)(e=new n.VertexJ3D(t.geometry)).setVertex(i),this.vertices.push(e),i+=s;this.faces=null},n.MeshJ3D.prototype.updateMeshPosition=function(t){var e=this.mesh;e.position.x+=t.x,e.position.y+=t.y,e.position.z+=t.z}}(MOD3),function(t){t.LibraryCopperlicht=function(){this.id="Copperlicht",this.meshClass=t.MeshCopperlicht,this.vertexClass=t.VertexCopperlicht},t.LibraryCopperlicht.prototype=new t.Library3d,t.LibraryCopperlicht.prototype.constructor=t.LibraryCopperlicht}(MOD3),function(t){t.VertexCopperlicht=function(t,e){this.node=t,this.buffer=e},t.VertexCopperlicht.prototype=new t.VertexProxy,t.VertexCopperlicht.prototype.setVertex=function(t){this.vertex=t,this.originalX=this.vertex.Pos.X,this.originalY=this.vertex.Pos.Y,this.originalZ=this.vertex.Pos.Z},t.VertexCopperlicht.prototype.getX=function(){return this.vertex.Pos.X},t.VertexCopperlicht.prototype.getY=function(){return this.vertex.Pos.Y},t.VertexCopperlicht.prototype.getZ=function(){return this.vertex.Pos.Z},t.VertexCopperlicht.prototype.setX=function(t){this.vertex.Pos.X=t,this.buffer.update(!0)},t.VertexCopperlicht.prototype.setY=function(t){this.vertex.Pos.Y=t,this.buffer.update(!0)},t.VertexCopperlicht.prototype.setZ=function(t){this.vertex.Pos.Z=t,this.buffer.update(!0)}}(MOD3),function(r){r.MeshCopperlicht=function(){},r.MeshCopperlicht.prototype=new r.MeshProxy,r.MeshCopperlicht.prototype.setMesh=function(t){r.MeshProxy.prototype.setMesh.call(this,t);for(var t=this.mesh.getMesh().GetMeshBuffers(),e=[],i=0;i<t.length;i++)for(var o=(e=t[i].Vertices).length,s=0;s<o;s++){var n=new r.VertexCopperlicht(this.mesh,t[i]);n.setVertex(e[s]),this.vertices.push(n)}this.faces=null,delete lookup},r.MeshCopperlicht.prototype.updateMeshPosition=function(t){this.mesh.Pos.X+=t.x,this.mesh.Pos.Y+=t.y,this.mesh.Pos.Z+=t.z}}(MOD3),function(t){var e;"performance"in t==!1&&(t.performance={}),Date.now=Date.now||function(){return(new Date).getTime()},"now"in t.performance==!1&&(e=t.performance.timing&&t.performance.timing.navigationStart?performance.timing.navigationStart:Date.now(),t.performance.now=function(){return Date.now()-e})}(this),FLIPBOOK.TWEEN=FLIPBOOK.TWEEN||function(){var i=[];return{REVISION:"14",getAll:function(){return i},removeAll:function(){i=[]},add:function(t){i.push(t)},remove:function(t){var e=i.indexOf(t);-1!==e&&i.splice(e,1)},update:function(t){if(0===i.length)return!1;var e=0;for(t=void 0!==t?t:window.performance.now();e<i.length;)i[e].update(t)?e++:i.splice(e,1);return!0}}}(),FLIPBOOK.TWEEN.Tween=function(t){var p=t,c={},l={},d={},u=1e3,g=0,f=!1,i=!1,m=0,y=null,x=FLIPBOOK.TWEEN.Easing.Linear.None,M=FLIPBOOK.TWEEN.Interpolation.Linear,P=[],v=null,L=!1,O=null,b=null,e=null;for(var o in t)c[o]=parseFloat(t[o],10);this.to=function(t,e){return void 0!==e&&(u=e),l=t,this},this.start=function(t){for(var e in FLIPBOOK.TWEEN.add(this),L=!(i=!0),y=void 0!==t?t:window.performance.now(),y+=m,l){if(l[e]instanceof Array){if(0===l[e].length)continue;l[e]=[p[e]].concat(l[e])}c[e]=p[e],c[e]instanceof Array==!1&&(c[e]*=1),d[e]=c[e]||0}return this},this.stop=function(){return i&&(FLIPBOOK.TWEEN.remove(this),i=!1,null!==e&&e.call(p),this.stopChainedTweens()),this},this.stopChainedTweens=function(){for(var t=0,e=P.length;t<e;t++)P[t].stop()},this.delay=function(t){return m=t,this},this.repeat=function(t){return g=t,this},this.yoyo=function(t){return f=t,this},this.easing=function(t){return x=t,this},this.interpolation=function(t){return M=t,this},this.chain=function(){return P=arguments,this},this.onStart=function(t){return v=t,this},this.onUpdate=function(t){return O=t,this},this.onComplete=function(t){return b=t,this},this.onStop=function(t){return e=t,this},this.update=function(t){var e;if(t<y)return!0;!1===L&&(null!==v&&v.call(p),L=!0);var i,o=(t-y)/u,s=x(o=1<o?1:o);for(e in l){var n=c[e]||0,r=l[e];r instanceof Array?p[e]=M(r,s):("string"==typeof r&&(r=n+parseFloat(r,10)),"number"==typeof r&&(p[e]=n+(r-n)*s))}if(null!==O&&O.call(p,s),1!=o)return!0;if(0<g){for(e in isFinite(g)&&g--,d){"string"==typeof l[e]&&(d[e]=d[e]+parseFloat(l[e],10)),f&&(i=d[e],d[e]=l[e],l[e]=i),c[e]=d[e]}return f&&0,y=t+m,!0}null!==b&&b.call(p);for(var h=0,a=P.length;h<a;h++)P[h].start(t);return!1}},FLIPBOOK.TWEEN.Easing={Linear:{None:function(t){return t}},Quadratic:{In:function(t){return t*t},Out:function(t){return t*(2-t)},InOut:function(t){return(t*=2)<1?.5*t*t:-.5*(--t*(t-2)-1)}},Cubic:{In:function(t){return t*t*t},Out:function(t){return--t*t*t+1},InOut:function(t){return(t*=2)<1?.5*t*t*t:.5*((t-=2)*t*t+2)}},Quartic:{In:function(t){return t*t*t*t},Out:function(t){return 1- --t*t*t*t},InOut:function(t){return(t*=2)<1?.5*t*t*t*t:-.5*((t-=2)*t*t*t-2)}},Quintic:{In:function(t){return t*t*t*t*t},Out:function(t){return--t*t*t*t*t+1},InOut:function(t){return(t*=2)<1?.5*t*t*t*t*t:.5*((t-=2)*t*t*t*t+2)}},Sinusoidal:{In:function(t){return 1-Math.cos(t*Math.PI/2)},Out:function(t){return Math.sin(t*Math.PI/2)},InOut:function(t){return.5*(1-Math.cos(Math.PI*t))}},Exponential:{In:function(t){return 0===t?0:Math.pow(1024,t-1)},Out:function(t){return 1===t?1:1-Math.pow(2,-10*t)},InOut:function(t){return 0===t?0:1===t?1:(t*=2)<1?.5*Math.pow(1024,t-1):.5*(2-Math.pow(2,-10*(t-1)))}},Circular:{In:function(t){return 1-Math.sqrt(1-t*t)},Out:function(t){return Math.sqrt(1- --t*t)},InOut:function(t){return(t*=2)<1?-.5*(Math.sqrt(1-t*t)-1):.5*(Math.sqrt(1-(t-=2)*t)+1)}},Elastic:{In:function(t){var e,i=.1;return 0===t?0:1===t?1:(e=!i||i<1?(i=1,.1):.4*Math.asin(1/i)/(2*Math.PI),-(i*Math.pow(2,10*--t)*Math.sin((t-e)*(2*Math.PI)/.4)))},Out:function(t){var e,i=.1;return 0===t?0:1===t?1:(e=!i||i<1?(i=1,.1):.4*Math.asin(1/i)/(2*Math.PI),i*Math.pow(2,-10*t)*Math.sin((t-e)*(2*Math.PI)/.4)+1)},InOut:function(t){var e,i=.1;return 0===t?0:1===t?1:(e=!i||i<1?(i=1,.1):.4*Math.asin(1/i)/(2*Math.PI),(t*=2)<1?i*Math.pow(2,10*--t)*Math.sin((t-e)*(2*Math.PI)/.4)*-.5:i*Math.pow(2,-10*--t)*Math.sin((t-e)*(2*Math.PI)/.4)*.5+1)}},Back:{In:function(t){return t*t*(2.70158*t-1.70158)},Out:function(t){return--t*t*(2.70158*t+1.70158)+1},InOut:function(t){var e=2.5949095;return(t*=2)<1?t*t*((1+e)*t-e)*.5:.5*((t-=2)*t*((1+e)*t+e)+2)}},Bounce:{In:function(t){return 1-FLIPBOOK.TWEEN.Easing.Bounce.Out(1-t)},Out:function(t){return t<1/2.75?7.5625*t*t:t<2/2.75?7.5625*(t-=1.5/2.75)*t+.75:t<2.5/2.75?7.5625*(t-=2.25/2.75)*t+.9375:7.5625*(t-=2.625/2.75)*t+.984375},InOut:function(t){return t<.5?.5*FLIPBOOK.TWEEN.Easing.Bounce.In(2*t):.5*FLIPBOOK.TWEEN.Easing.Bounce.Out(2*t-1)+.5}}},FLIPBOOK.TWEEN.Interpolation={Linear:function(t,e){var i=t.length-1,o=i*e,s=Math.floor(o),n=FLIPBOOK.TWEEN.Interpolation.Utils.Linear;return e<0?n(t[0],t[1],o):1<e?n(t[i],t[i-1],i-o):n(t[s],t[i<s+1?i:s+1],o-s)},Bezier:function(t,e){for(var i=0,o=t.length-1,s=Math.pow,n=FLIPBOOK.TWEEN.Interpolation.Utils.Bernstein,r=0;r<=o;r++)i+=s(1-e,o-r)*s(e,r)*t[r]*n(o,r);return i},CatmullRom:function(t,e){var i=t.length-1,o=i*e,s=Math.floor(o),n=FLIPBOOK.TWEEN.Interpolation.Utils.CatmullRom;return t[0]===t[i]?(e<0&&(s=Math.floor(o=i*(1+e))),n(t[(s-1+i)%i],t[s],t[(s+1)%i],t[(s+2)%i],o-s)):e<0?t[0]-(n(t[0],t[0],t[1],t[1],-o)-t[0]):1<e?t[i]-(n(t[i],t[i],t[i-1],t[i-1],o-i)-t[i]):n(t[s?s-1:0],t[s],t[i<s+1?i:s+1],t[i<s+2?i:s+2],o-s)},Utils:{Linear:function(t,e,i){return(e-t)*i+t},Bernstein:function(t,e){var i=FLIPBOOK.TWEEN.Interpolation.Utils.Factorial;return i(t)/i(e)/i(t-e)},Factorial:function(){var o=[1];return function(t){var e,i=1;if(o[t])return o[t];for(e=t;1<e;e--)i*=e;return o[t]=i}}(),CatmullRom:function(t,e,i,o,s){var n=.5*(i-t),r=.5*(o-e),h=s*s;return(2*e-2*i+n+r)*(s*h)+(-3*e+3*i-2*n-r)*h+n*s+e}}},FLIPBOOK.CSS3DObject=function(t){THREE.Object3D.call(this),this.element=t,this.element.style.position="absolute",this.element.style.pointerEvents="auto",this.addEventListener("removed",function(){this.traverse(function(t){t.element instanceof Element&&null!==t.element.parentNode&&t.element.parentNode.removeChild(t.element)})})},FLIPBOOK.CSS3DObject.prototype=Object.create(THREE.Object3D.prototype),FLIPBOOK.CSS3DObject.prototype.constructor=FLIPBOOK.CSS3DObject,FLIPBOOK.CSS3DSprite=function(t){FLIPBOOK.CSS3DObject.call(this,t)},FLIPBOOK.CSS3DSprite.prototype=Object.create(FLIPBOOK.CSS3DObject.prototype),FLIPBOOK.CSS3DSprite.prototype.constructor=FLIPBOOK.CSS3DSprite,FLIPBOOK.CSS3DRenderer=function(){var i,o,h,a,l=this,d=new THREE.Matrix4,u={camera:{fov:0,style:""},objects:new WeakMap},p=document.createElement("div");p.style.overflow="hidden",this.domElement=p;var g=document.createElement("div");g.style.WebkitTransformStyle="preserve-3d",g.style.transformStyle="preserve-3d",g.style.pointerEvents="none",p.appendChild(g);var f=/Trident/i.test(navigator.userAgent);function c(t){return Math.abs(t)<1e-10?0:t}function m(t){var e=t.elements;return"matrix3d("+c(e[0])+","+c(-e[1])+","+c(e[2])+","+c(e[3])+","+c(e[4])+","+c(-e[5])+","+c(e[6])+","+c(e[7])+","+c(e[8])+","+c(-e[9])+","+c(e[10])+","+c(e[11])+","+c(e[12])+","+c(-e[13])+","+c(e[14])+","+c(e[15])+")"}function y(t,e){var i=t.elements,o="matrix3d("+c(i[0])+","+c(i[1])+","+c(i[2])+","+c(i[3])+","+c(-i[4])+","+c(-i[5])+","+c(-i[6])+","+c(-i[7])+","+c(i[8])+","+c(i[9])+","+c(i[10])+","+c(i[11])+","+c(i[12])+","+c(i[13])+","+c(i[14])+","+c(i[15])+")";return f?"translate(-50%,-50%)translate("+h+"px,"+a+"px)"+e+o:"translate(-50%,-50%)"+o}this.getSize=function(){return{width:i,height:o}},this.setSize=function(t,e){h=(i=t)/2,a=(o=e)/2,p.style.width=t+"px",p.style.height=e+"px",g.style.width=t+"px",g.style.height=e+"px"};var s,n,x=(s=new THREE.Vector3,n=new THREE.Vector3,function(t,e){return s.setFromMatrixPosition(t.matrixWorld),n.setFromMatrixPosition(e.matrixWorld),s.distanceToSquared(n)});function M(t){for(var e,i=(e=[],t.traverse(function(t){t instanceof THREE.CSS3DObject&&e.push(t)}),e.sort(function(t,e){return u.objects.get(t).distanceToCameraSquared-u.objects.get(e).distanceToCameraSquared})),o=i.length,s=0,n=i.length;s<n;s++)i[s].element.style.zIndex=o-s}this.render=function(t,e){var i,o,s=e.projectionMatrix.elements[5]*a;u.camera.fov!==s&&(e.isPerspectiveCamera?(p.style.WebkitPerspective=s+"px",p.style.perspective=s+"px"):(p.style.WebkitPerspective="",p.style.perspective=""),u.camera.fov=s),!0===t.autoUpdate&&t.updateMatrixWorld(),null===e.parent&&e.updateMatrixWorld(),e.isOrthographicCamera&&(i=-(e.right+e.left)/2,o=(e.top+e.bottom)/2);var n=e.isOrthographicCamera?"scale("+s+")translate("+c(i)+"px,"+c(o)+"px)"+m(e.matrixWorldInverse):"translateZ("+s+"px)"+m(e.matrixWorldInverse),r=n+"translate("+h+"px,"+a+"px)";u.camera.style===r||f||(g.style.WebkitTransform=r,g.style.transform=r,u.camera.style=r),function t(e,i,o,s){var n,r,h,a;e instanceof FLIPBOOK.CSS3DObject&&(e.onBeforeRender(l,i,o),n=e instanceof FLIPBOOK.CSS3DSprite?(d.copy(o.matrixWorldInverse),d.transpose(),d.copyPosition(e.matrixWorld),d.scale(e.scale),d.elements[3]=0,d.elements[7]=0,d.elements[11]=0,d.elements[15]=1,y(d,s)):y(e.matrixWorld,s),r=e.element,void 0!==(h=u.objects.get(e))&&h.style===n||(r.style.WebkitTransform=n,a={style:r.style.transform=n},f&&(a.distanceToCameraSquared=x(o,e)),u.objects.set(e,a)),r.parentNode!==g&&g.appendChild(r),e.onAfterRender(l,i,o));for(var p=0,c=e.children.length;p<c;p++)t(e.children[p],i,o,s)}(t,t,e,n),f&&M(t)}};