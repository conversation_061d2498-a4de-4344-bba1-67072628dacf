body, html {
    background-color: #e59751;
}

.content,.content:before, .content:after {
    background-color: #f9b46e;
}

h1 {
    color: #fff;
}

.cancelBtnHolder .big-btn {
    background-image: url('../../general/img/close.png');
    border-color: #fff;
}

.heading p{
    margin-bottom: 0;
    color: #fff;
    height: 40px;
}


.big-btn {
    background-color: #e86981;
    color: #fff;
    border-bottom: solid 3px #ce4e6d;
}

.scenario-count ul {
    margin-bottom: 40px;
}

.scenario-count ul li {
  list-style: none;
  color: white;
  font-size: 14px;
  display: inline;
  border: 3px solid white;
  border-radius: 50%;
  padding: 6px 10px;
  font-weight: bold;
  margin: 0 50px;
}

.scenario-count ul li.selected {
    background-color: white;
    color: black;
}

.scenario-count ul li.active {
    background-color: white;
    color: black;
    padding: 16px 21px;
    margin: 0 40px;
}

li:after {
  border-top: 3px solid white;
  width: 95px;
  content: "";
  position: absolute;
  margin-top: 10px;
  margin-left: 16px;
}

ul > li:last-child:after {
    border: none;
}

.summary, #replay {
    display: none;
}

.summary > div {
    width: 50%;
    float: left;
    position: relative;
}

.summary span {
    font-size: 30px;
    font-weight: bold;
    position: absolute;
    left: 28%;
    margin: 10px;
    top: 42%;
}

.summary img {
  border-radius: 50%;
  width: 190px;
  margin: 15px;
}


.scenario {
    width: 100%;
    height: 68%;
}

.main-holder {
    width: 100%;
}

.main {
    width: 350px;
    border-radius: 50%;
}

.choice-holder {
    width: 65%;
    margin: 0 auto;
    position: relative;
    z-index: 1
}

.or-text {
    color: white;
    top: 50px;
    font-size: 22px;
    position: relative;
}
.choice {
    border-radius: 50%;
    width: 250px;
    margin-top: -100px;
    cursor: pointer;
    -webkit-box-shadow: 0px 0px 0px 4px rgba(32,29,33,0.1);
    -moz-box-shadow: 0px 0px 0px 4px rgba(32,29,33,0.1);
    box-shadow: 0px 0px 0px 4px rgba(32,29,33,0.1);
}

.choice1 {
    float: left;
}

.choice2 {
    float: right;
}

.choice.selected {
    border: 3px solid #367aa4;
    -webkit-box-shadow: 0px 0px 0px 4px rgba(54, 122, 164, 0.8);
    -moz-box-shadow: 0px 0px 0px 4px rgba(54, 122, 164, 0.8);
    box-shadow: 0px 0px 0px 4px rgba(54, 122, 164, 0.8);
}

.right {
    border: 3px solid #367aa4;
}

.wrong {
    border: 3px solid #ce4e6d;
}

#next:hover, #replay:hover {
  border-bottom-color: #e86981;
}