

/*
|--------------------------------------------------------------------------
| Login
|--------------------------------------------------------------------------
*/

header {
	.login {
		
		@include clearfix();
		background-color: $brand-light-blue;

		form {

			background-color: $brand-dark-blue;

			&.login-error {
				background-color: $brand-dark-pink;
			}
			
			width: 50%;
			padding-top: 15px;
			padding-bottom: 15px;
			float: right;
			
			.field-wrap, .submit-wrap {
				@include make-column(4);
			}
			.btn {
				height: 44px;
			}
		}

		.error {
			float: left;
			width: 50%;
			height: 74px;
			background-image: url('../image/login-arrow-blue.png');
			background-repeat: no-repeat;
			background-position: center right;
			border-right: solid 1px $brand-dark-blue;
			@include box-sizing(border-box);
		}

		.login-field {
			border: solid 2px $brand-light-blue;
			border-radius: $brdr-radius;
			@include clearfix();
			label {
				display: block;
				width: 20%;
				height: 40px;
				border-right: solid 2px $brand-light-blue;
				float: left;
				@include box-sizing(border-box);
			}
			.field {
				background-color: transparent;
				border: 0;
				padding: 10px;
				float: left;
				font-size: 16px;
				line-height:20px;
				width: 80%;
				@include placeholder($color: $brand-light-blue);
				@include box-sizing(border-box);
				color: #fff;
			}

			.login-user {
				background-image: url('../image/icons/icon_input_login_blue.png');
				background-repeat: no-repeat;
				background-position: center center;
			}

			.login-password {
				background-image: url('../image/icons/icon_input_password_blue.png');
				background-repeat: no-repeat;
				background-position: center center;
			}

			&.focus {
				border-color: #fff;
				label {
					border-color: #fff;
				}
				.login-password {
					background-image: url('../image/icons/icon_input_password_white.png');
				}
				.login-user {
					background-image: url('../image/icons/icon_input_login_white.png');
				}
			}

		}

		&.login-error {
			background-color: $brand-light-pink;
			.error {
				background-image: url('../image/login-arrow-pink.png');
				border-color: $brand-dark-pink;
			}
			.login-field {
				border-color: $brand-light-pink;
				label {
					border-color: $brand-light-pink;
					&.login-user { background-image: url('../image/icons/icon_input_login_red.png'); }
					&.login-password { background-image: url('../image/icons/icon_input_password_red.png'); }
				}
				.field {
					@include placeholder($color: $brand-light-pink);
				}
			}
		}

	}
}


/*
|--------------------------------------------------------------------------
| Navigation
|--------------------------------------------------------------------------
*/


header {
	nav {

		background-color: #F9F8F7;

		ul {
			@include clearfix();
			margin: 0;
			padding: 0;
			list-style: none;
			font-size: 14px;
		}

		li {
			width: 14.2857%;
			float: left;
			@include box-sizing(border-box);
			border-right: solid 1px darken($brand-cream, 5%);
			border-bottom: solid 4px rgba(0,0,0,0.1);
			&.purple a { color: $brand-dark-purple; }
			&.purple-darker a { color: $brand-darker-purple; }
			&.pink a { color: $brand-light-pink; }
			&.orange a { color: $brand-orange; }
			&.blue a { color: $brand-dark-blue; }
			&.green a { color: $brand-green; }
			&.active {
				border-bottom: solid 4px $brand-light-purple;
			}
			&:hover {
				background-color: #fff;
			}
		}

		a {
			display: block;
			padding: 15px 10px;
			text-align: center;
			text-decoration: none;
			height: 80px;
			span {
				display: block;
			}
		}

		img {
			display: block;
			margin: 0 auto;
			margin-bottom: 15px;
		}

		.logo {
			background-color: #fff;
			img { 
				margin: 10px auto 0 auto;
			}
		}

		ul.authed  {
			li {
				width: 14.2857%;
			}
			li.user {
				width: 28.5714%;
				padding: 15px;
				@include box-sizing(border-box);
				height: 114px;
				// Re-instate when alt dashboard is in use
				// &.dashboard {
				// 	background-color: $brand-orange;
				// 	border-bottom-color: #f7bc84;
				// }
				a { height: auto; }
				&:hover {
					background-color: #F9F8F7;
				}
			}
		}

		ul.authed li.user .user-dropdown {
			border-radius: 60px;
			padding-left: 130px;
			text-align: left;
			margin-top: 5px;
			color: $brand-dark-purple;
			background-color: #fff;
			background-repeat: no-repeat;
			background-position: 90% center;
			background-image: url('../image/icons/icon_select_darkpurple.png');

			strong {
				display: block;
			}

			&.active {
				border-radius: 20px 20px 0 0;
				ul {
					margin: 0;
					display: block;
					padding: 0;
					list-style: none;
					.drop {
						display: block;
						height: auto;
						border-bottom: 0;
						width: 100% !important;
					}
					a {
						display: block;
						padding: 10px;
						width: 100%;
					}				
				}
			}
			// Re-instate when alt dashboard is in use
			// &.dashboard {
			// 	background-color: #f7bc84;
			// 	background-image: ur('../image/icons/icon_select_white.png');
			// 	color: #fff;
			// }

		}
	}
}


header nav ul.authed li ul {
	display: none;
	border-radius: 0 0 20px 20px;
	overflow: hidden;
	@include box-shadow(0px 2px 3px rgba(0,0,0,0.2));
	position: relative;
	z-index: 9999;
}

header nav ul.authed li ul li {
	width: 100%;
	display: block;
	float: none;
	border:0;
	background-color: $brand-dark-purple;
	a {
		color: #fff;
		text-decoration: none;
		text-align: left;
		padding: 20px;
		&:hover {
			background-color: $brand-light-purple;
		}
	}
}

/*
|--------------------------------------------------------------------------
| Buttons
|--------------------------------------------------------------------------
*/


.btn {
	padding: 10px;
	display: inline-block;
	border-radius: $brdr-radius;
	border: 0;
	border-bottom: solid 3px;
	@include box-sizing(border-box);
	text-decoration: none;
	text-shadow: 0 1px 0 rgba(0,0,0,0.2);
	background-repeat: no-repeat;
	background-position: 15px center;
	&.btn-purple {
		background-color: $brand-dark-purple;
		border-color: darken($brand-dark-purple, 15%);
		color: #fff;
	}
	&.btn-orange {
		background-color: $brand-orange;
		border-color: darken($brand-orange, 15%);
		color: #fff;
	}
	&.btn-blue {
		background-color: $brand-light-blue;
		border-color: darken($brand-light-blue, 15%);
		color: #fff;
	}
	&.btn-pink {
		background-color: $brand-light-pink;
		border-color: darken($brand-dark-pink, 15%);
		color: #fff;
	}
	&.btn-block {
		width: 100%;
	}
	&.btn-large {
		padding: 15px 30px;
		font-size: 18px;
	}
	&.btn-music {
		padding-left: 50px;
		background-image: url('../image/icons/icon_button_music.png');
	}
	&.btn-read {
		padding-left: 50px;
		background-image: url('../image/icons/icon_button_read.png');
	}
	&.btn-resources {
		padding-left: 50px;
		background-image: url('../image/icons/icon_button_resources.png');
	}
	&.btn-add-resource {
		padding-left: 50px;
		background-image: url('../image/icons/icon_button_resources.png');
	}
	&.btn-light {
		padding-left: 50px;
		background-image: url('../image/icons/icon_button_candle.png');
	}
}



/*
|--------------------------------------------------------------------------
| Alerts
|--------------------------------------------------------------------------
*/


.alert {
	&.alert-login-error {
		padding: 30px;
		float: right;
		color: #fff;
	}
}




/*
|--------------------------------------------------------------------------
| Container types
|--------------------------------------------------------------------------
*/

@media(min-width: 1px) {
	.centered-container {
		margin: 0 30px;
	}
}

@media(min-width: 980px) {
	.centered-container {
		width: 960px;
		margin: 0 auto;
	}
}

@media(min-width: 1200px) {
	.centered-container {
		width: 1140px;
		margin: 0 auto;
	}
}




/*
|--------------------------------------------------------------------------
| Fields
|--------------------------------------------------------------------------
*/


input {
	&:focus {
		outline: none;
	}
}


/*
|--------------------------------------------------------------------------
| Task Bar
|--------------------------------------------------------------------------
*/

.task-bar {
	@include clearfix();
	.left, .right {
		@include box-sizing(border-box);
		float: left;
		padding: 15px 30px;
	}
	.left {
		width: 71.4285%;
	}
	.right {
		width: 28.5714%;
	}
	.search-field {
		display: block;
		width: 100%;
		background-color: transparent;
		border:solid 3px transparent;
		padding: 12px 0;
		color:#fff;
		background-image: url('../image/icons/icon_button_search.png');
		background-repeat: no-repeat;
		background-position: 95% center;
	}
	label {
		float: left;
		padding: 12px;
		color: #fff;
	}
}



/*
|--------------------------------------------------------------------------
| General Form Styling
|--------------------------------------------------------------------------
*/



.field-wrap {
	margin-bottom: 5px;
	label {
		display: block;
		padding: 10px 0;
	}
	.field {
		padding: 10px;
		border: solid 2px darken(#367aa4, 10%);
		border-radius: $brdr-radius;
		width: 100%;
		@include box-sizing(border-box);

	}
	textarea.field {
		height: 75px;
	}
	&.hide {
		display: none;
	}
}







