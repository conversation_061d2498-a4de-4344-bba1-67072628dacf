/* v 3.17
author http://codecanyon.net/user/creativeinteractivemedia/portfolio?ref=creativeinteractivemedia
*/

var FLIPBOOK=FLIPBOOK||{};FLIPBOOK.PdfService=function(i,e,g){var h=this;this.pdfDocument=i,this.pdfInfo=i._pdfInfo,this.numPages=this.pdfInfo.numPages,this.webgl="webgl"==g.viewMode&&1<this.numPages,this.options=g,this.main=g.main,this.model=e,this.pages=[],this.thumbs=[],this.canvasBuffer=[],this.viewports=[],this.textContents=[],this.pdfPages=[],this.pdfAnnotations=[],this.eventBus=new EventBus,this.linkService=new PDFLinkService({eventBus:this.eventBus}),this.linkService.setViewer(this.main),this.linkService.setDocument(i),this.linkService.externalLinkTarget=2,window._dbg=0,this.getCanvas=function(){for(var e,t=0;t<this.canvasBuffer.length;t++){if((e=this.canvasBuffer[t]).available){e.available=!1,e.double=!1;break}e=null}return e||((e=document.createElement("canvas")).available=!1,e.index=this.canvasBuffer.length,this.canvasBuffer.push(e)),e.rendering=!0,e},this.isRendering=function(t,n){var i=!1;return this.canvasBuffer.forEach(function(e){e.size==n&&e.pdfPageIndex==t&&e.rendering&&(i=!0)}),i},this.isRendered=function(t,n){var i=!1;return this.canvasBuffer.forEach(function(e){e.size==n&&e.pdfPageIndex==t&&e.rendered&&(i=!0)}),i},this.setRightIndex=function(t){var n=this,i=[];this.options.isMobile;this.canvasBuffer.forEach(function(e){(!e.rendering&&7<t-e.pageIndex||t-e.pageIndex<-6)&&-1<e.pageIndex&&(-1<e.pdfPageIndex&&(delete n.pages[e.pdfPageIndex].canvas[e.size],n.pages[e.pdfPageIndex].cleanup()),i.push({index:e.pageIndex,size:e.size}),e.double&&i.push({index:e.pageIndex-1,size:e.size}),e.getContext("2d").clearRect(0,0,e.width,e.height),e.width=e.height=0,e.pageIndex=-100,e.available=!0,e.rendered=!1)}),0<i.length&&this.model.trigger("pageUnloaded",{unloadedPages:i})},this.loadThumbs=function(t,n){var i=this;this.thumbLoading=this.thumbLoading||0,this.thumbLoading>=this.pdfInfo.numPages?n.call(i):this.loadThumb(this.thumbLoading,function(e){i.options.thumbLoaded(e),i.thumbLoading++,i.loadThumbs(t,n)})},this.loadThumb=function(r,o){var d=this;this.getViewport(r,function(){var e=d.pages[r],t=100/e.getViewport({scale:1}).height,n=e.getViewport({scale:t}),i=document.createElement("canvas");i.index=r;var a=i.getContext("2d");i.height=n.height,i.width=n.width;var s={canvasContext:a,viewport:n};e.cleanupAfterRender=!0,e.render(s).then(function(){e.cleanup(),o&&o.call(d,i)})})},this.init=function(t){h.getViewport(0,function(e){h.r1=e.width/e.height,1==h.pdfInfo.numPages?(h.double=!1,h.model.trigger("pdfinit")):h.getViewport(1,function(e){h.r2=e.width/e.height,h.double=1.5<h.r2/h.r1,h.backCover=t||!0,h.model.trigger("pdfinit")})})},this.loadOutline=function(t){var n=this;this.pdfDocument.getOutline().then(function(e){n.outline=e,n.outlineLoaded=!0,t.call(n,e)})},this.startLoadingText=function(){this.loadingText=!0},this.stopLoadingText=function(){this.loadingText=!1},this.getViewport=function(e,t){e>=h.pdfInfo.numPages||(h.pages[e]?(h.viewports[e]=h.pages[e].getViewport({scale:1}),t.call(h,h.viewports[e])):i.getPage(e+1).then(function(e){h.pages[e.pageIndex]=e,h.getViewport(e.pageIndex,t)}))},this.getAllViewports=function(e){},this.getText=function(n,i){var a=this;this.getViewport(n,function(e){var t=a.pages[n];a.getTextContent(t,function(){i.call(a,t)})})},this.getTextAllPages=function(e){var t=this;this.loadingTextFromPage=this.loadingTextFromPage||0,this.getText(this.loadingTextFromPage,function(){t.loadingTextFromPage==t.numPages-1?e&&e.call(t):(t.loadingTextFromPage++,t.getTextAllPages(e))})},this.findInPage=function(e,t,n){var i=this;this.findInPageCallbacks=this.findInPageCallbacks||[],this.findInPageCallbacks[t]=n,this.searchingString=e,this.pages[t]&&this.pages[t].textContent?i.findInPageTextContentAvailable(this.pages[t],t):this.getText(t,function(e){i.findInPageTextContentAvailable(e,t)})},this.findInPageTextContentAvailable=function(e,t){var n=e.textContent.items;if(void 0===e.textContentString){e.textContentString="";for(var i=0;i<n.length;i++)e.textContentString+=n[i].str}var a=e.textContentString.toUpperCase().search(this.searchingString.toUpperCase()),s=this.findInPageCallbacks[t];s&&s.call(this,a,e.htmlContent,e.pageIndex),this.findInPageCallbacks[t]=null},this.getThumb=function(r,o,d){this.getViewport(r,function(e){var t,n,i,a,s=h.pages[r];s.thumb?d.call(h,s.thumb):(t=o/h.viewports[r].height,e=s.getViewport({scale:t}),n=document.createElement("canvas"),i=(s.thumb=n).getContext("2d"),n.height=e.height,n.width=e.width,a={canvasContext:i,viewport:e},s.cleanupAfterRender=!0,s.render(a).then(function(){s.cleanup(),d.call(h,s.thumb)}))})},this.getPage=function(e,t){var n=this;(n.double?Math.round(e/2)+1:e+1)>this.pdfInfo.numPages||i.getPage(e).then(function(e){n.renderPage(e,t)})},this.renderPage=function(e,t,n){var i=this;if(e.canvas=e.canvas||{},e.canvas[t]&&e.canvas[t].rendered)return n&&n.call(i,e),void(n=null);e.rendering&&setTimeout(function(){i.renderPage(e,t,n)},300),e.rendering=!0;var a=i.getCanvas();a.size=t,a.pdfPageIndex=e.pageIndex;var s=e.getViewport({scale:1}),r=s.width<=s.height,o=r||!i.webgl?t/s.height:t/s.width,d=e.getViewport({scale:o});a.width=d.width,a.height=d.height,i.webgl&&(r?(a.height=t,a.width=d.width>t?d.width:t,a.scaleX=d.width/t,a.scaleY=1):(a.width=t,a.height=d.height>t?d.height:t,a.scaleY=d.height/t,a.scaleX=1));var h=a.getContext("2d");h.fillStyle="#FFFFFF",h.fillStyle="#000000";var l={canvasContext:h,viewport:d};e.scale=o,e.canvas[t]=a,e.canvas[t].ratio=d.width/d.height,e.cleanupAfterRender=!0,e.render(l).promise.then(function(){l=null,n&&n.call(i,e),e.rendering=!1,n=null})},this.renderBookPage=function(e,t,n){var i=this.options.doublePage?Math.round(e/2):e;this.renderPageFromPdf(i,t,n)},this.loadPageTextLayer=function(t,n,i){var a=this,s=this.pages[t];if(t>=this.pdfInfo.numPages&&i.call(a),this.pages[t])return!a.options.textLayer||s.textContentLoaded||s.textContentLoading?s.annotationsLoaded||s.annotationsLoading?void(!s.annotationsLoading&&!s.textContentLoading&&s.annotationsLoaded&&s.textContentLoaded?i.call(this,s,n):setTimeout(function(){a.loadPageTextLayer(t,n,i)},100)):(s.annotationsLoading=!0,void s.getAnnotations({intent:"display"}).then(function(e){s.annotationsLoaded=!0,s.annotationsLoading=!1,s.annotations=e,a.loadPageTextLayer(t,n,i)})):(s.textContentLoading=!0,void s.getTextContent().then(function(e){s.textContentLoaded=!0,s.textContentLoading=!1,s.textContent=e,a.loadPageTextLayer(t,n,i)}));this.getViewport(t,function(e){a.loadPageTextLayer(t,n,i)})},this.loadTextLayer=function(e,h){var l=this.options.doublePage?Math.round(e/2):e,c=this;this.loadPageTextLayer(l,e,function(e,t){var n=g.pages[t]||{};if(n.index=t,!n.htmlContentInitialized){n.htmlContentInitialized=!0,n.textRendering=!0;var i=document.createElement("div");i.classList.add("flipbook-page-htmlContent");var a=document.createElement("div");a.className="flipbook-textLayer",i.appendChild(a);var s=1e3/e.getViewport({scale:1}).height;a.style.width=String(1e3*e.getViewport({scale:1}).width/e.getViewport({scale:1}).height)+"px",a.style.height="1000px";var r,o,d=new TextLayerBuilder({eventBus:c.eventBus,textLayerDiv:a,pageIndex:l,viewport:e.getViewport({scale:s})});return d.setTextContent(e.textContent),d.render(),0<e.annotations.length&&((r=document.createElement("div")).className="annotationLayer",i.appendChild(r),o={viewport:e.getViewport({scale:1e3/e.getViewport({scale:1}).height}).clone({dontFlip:!0}),div:r,annotations:e.annotations,page:e,linkService:c.linkService},pdfjsLib.AnnotationLayer.render(o)),i.style.transformOrigin="0 0",c.eventBus.on("textlayerrendered",function(e){e.source.pageIdx==l&&(n.textRendering=!1,h.call(c))}),n.htmlContent&&jQuery(i).append(jQuery(n.htmlContent)),void(n.htmlContent=i)}n.textRendering||h.call(c)})},this.renderPageFromPdf=function(t,n,i){var e,a,s,r=this;t>=this.pdfInfo.numPages&&i.call(r),this.pages[t]?(e=t,s=(a=this.pages[t]).getViewport({scale:1}),Math.max(s.width,s.height),s.height,this.isRendering(e,n)?setTimeout(function(){r.renderPageFromPdf(t,n,i)},300):this.isRendered(e,n)?this.onPdfPageRendered(r.pages[e],n,i):this.renderPage(a,n,function(e){r.onPdfPageRendered(e,n,i)})):this.getViewport(t,function(e){r.renderPageFromPdf(t,n,i)})},this.onBookPageRendered=function(e,t,n,i){n=n,i=i;this.model.trigger("pageLoaded",{index:n,size:i,canvas:t})},this.getBookPage=function(t,n){var i=null;return this.canvasBuffer.forEach(function(e){e.pageIndex==t&&e.size==n&&(i=e)}),i},this.onPdfPageRendered=function(e,t,n){var i,a,s,r,o,d,h,l,c=this;e.canvas&&e.canvas[t]&&(i=e.canvas[t],a=e.pageIndex,i.pdfPageIndex=a,g.doublePage?0==a?(i.pageIndex=0,i.rendering=!1,i.rendered=!0,c.onBookPageRendered(e,i,0,t)):a==g.pages.length/2?(i.pageIndex=g.numPages-1,i.rendering=!1,i.rendered=!0,c.onBookPageRendered(e,i,g.numPages-1,t)):c.webgl?(i.double=!0,i.scaleX=i.width/2/t,i.scaleY=i.scaleY,i.pageIndex=2*a,i.rendering=!1,i.rendered=!0,c.onBookPageRendered(e,i,2*a,t),c.onBookPageRendered(e,i,2*a-1,t)):(s=c.options.rightToLeft?2*a-1:2*a,r=c.options.rightToLeft?2*a:2*a-1,(o=this.getBookPage(r,t))||((o=c.getCanvas()).size=t,d=o.getContext("2d"),o.width=i.width/2,o.height=i.height,d.fillStyle="#FFFFFF",o.pageIndex=r,o.pdfPageIndex=a,d.drawImage(i,0,0),o.rendering=!1,o.rendered=!0),c.onBookPageRendered(e,o,r,t),(h=this.getBookPage(s,t))||((h=c.getCanvas()).size=t,l=h.getContext("2d"),h.width=i.width/2,h.height=i.height,l.fillStyle="#FFFFFF",h.pageIndex=s,h.pdfPageIndex=a,l.drawImage(i,i.width/2,0,i.width/2,i.height,0,0,i.width/2,i.height),h.rendering=!1,h.rendered=!0),c.onBookPageRendered(e,h,s,t),i.size=200,i.pageIndex=r,i.rendering=!1,i.rendered=!0):(i.pageIndex=a,i.size=t,i.rendering=!1,i.rendered=!0,c.onBookPageRendered(e,i,a,t)),n&&n.call(c,{canvas:i,lCanvas:o,rCanvas:h,size:t,pdfPageIndex:a,htmlContent:e.htmlContent}),n=null)},this.getTextContent=function(t,e){if(t.getTextCallback=e,t.textContent)t.getTextCallback(t);else{if(t.textContentLoading){var n=this;return void setTimeout(function(){n.getTextContent(t,e)},100)}t.getTextContent().then(function(e){t.textContent=e,t.textContentLoading=!1,t.textContentLoaded=!0,t.getTextCallback(t)}),t.textContentLoading=!0}},this.getCanvasByHeight=function(e,t,n){}},FLIPBOOK.PdfService.prototype={};var _createClass=function(){function i(e,t){for(var n=0;n<t.length;n++){var i=t[n];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(e,t,n){return t&&i(e.prototype,t),n&&i(e,n),e}}();function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}var EventBus=function(){function n(){var e=(0<arguments.length&&void 0!==arguments[0]?arguments[0]:{}).dispatchToDOM,t=void 0!==e&&e;_classCallCheck(this,n),this._listeners=Object.create(null),this._dispatchToDOM=!0===t}return _createClass(n,[{key:"on",value:function(e,t){var n=this._listeners[e];n||(n=[],this._listeners[e]=n),n.push(t)}},{key:"off",value:function(e,t){var n=this._listeners[e],i=void 0;!n||(i=n.indexOf(t))<0||n.splice(i,1)}},{key:"dispatch",value:function(e){var t,n,i=this._listeners[e];i&&0!==i.length?(t=Array.prototype.slice.call(arguments,1),i.slice(0).forEach(function(e){e.apply(null,t)}),this._dispatchToDOM&&this._dispatchDOMEvent(e,t)):this._dispatchToDOM&&(n=Array.prototype.slice.call(arguments,1),this._dispatchDOMEvent(e,n))}},{key:"_dispatchDOMEvent",value:function(e,t){var n=1<arguments.length&&void 0!==t?t:null;if(this._dispatchToDOM){var i=Object.create(null);if(n&&0<n.length){var a=n[0];for(var s in a){var r=a[s];if("source"!==s)i[s]=r;else if(r===window||r===document)return}}var o=document.createEvent("CustomEvent");o.initCustomEvent(e,!0,!0,i),document.dispatchEvent(o)}}}]),n}(),EXPAND_DIVS_TIMEOUT=300,MATCH_SCROLL_OFFSET_TOP=-50,MATCH_SCROLL_OFFSET_LEFT=-400,TextLayerBuilder=function(){function h(e){var t=e.textLayerDiv,n=e.eventBus,i=e.pageIndex,a=e.viewport,s=e.findController,r=void 0===s?null:s,o=e.enhanceTextSelection,d=void 0!==o&&o;_classCallCheck(this,h),this.textLayerDiv=t,this.eventBus=n||(0,_dom_events.getGlobalEventBus)(),this.textContent=null,this.textContentItemsStr=[],this.textContentStream=null,this.renderingDone=!1,this.pageIdx=i,this.pageNumber=this.pageIdx+1,this.matches=[],this.viewport=a,this.textDivs=[],this.findController=r,this.textLayerRenderTask=null,this.enhanceTextSelection=d,this._boundEvents=Object.create(null),this._bindEvents(),this._bindMouse()}return _createClass(h,[{key:"_finishRendering",value:function(){var e;this.renderingDone=!0,this.enhanceTextSelection||((e=document.createElement("div")).className="endOfContent",this.textLayerDiv.appendChild(e)),this.eventBus.dispatch("textlayerrendered",{source:this,pageNumber:this.pageNumber,numTextDivs:this.textDivs.length})}},{key:"render",value:function(e){var t,n=this,i=0<arguments.length&&void 0!==e?e:0;!this.textContent&&!this.textContentStream||this.renderingDone||(this.cancel(),this.textDivs=[],t=document.createDocumentFragment(),this.textLayerRenderTask=(0,pdfjsLib.renderTextLayer)({textContent:this.textContent,textContentStream:this.textContentStream,container:t,viewport:this.viewport,textDivs:this.textDivs,textContentItemsStr:this.textContentItemsStr,timeout:i,enhanceTextSelection:this.enhanceTextSelection}),this.textLayerRenderTask.promise.then(function(){n.textLayerDiv.appendChild(t),n._finishRendering(),n.updateMatches()},function(e){}))}},{key:"cancel",value:function(){this.textLayerRenderTask&&(this.textLayerRenderTask.cancel(),this.textLayerRenderTask=null)}},{key:"setTextContentStream",value:function(e){this.cancel(),this.textContentStream=e}},{key:"setTextContent",value:function(e){this.cancel(),this.textContent=e}},{key:"convertMatches",value:function(e,t){var n=0,i=0,a=this.textContentItemsStr,s=a.length-1,r=null===this.findController?0:this.findController.state.query.length,o=[];if(!e)return o;for(var d=0,h=e.length;d<h;d++){for(var l=e[d];n!==s&&l>=i+a[n].length;)i+=a[n].length,n++;n===a.length&&console.error("Could not find a matching mapping");var c={begin:{divIdx:n,offset:l-i}};for(l+=t?t[d]:r;n!==s&&l>i+a[n].length;)i+=a[n].length,n++;c.end={divIdx:n,offset:l-i},o.push(c)}return o}},{key:"renderMatches",value:function(e){if(0!==e.length){var d=this.textContentItemsStr,h=this.textDivs,t=null,n=this.pageIdx,i=null!==this.findController&&n===this.findController.selected.pageIdx,a=null===this.findController?-1:this.findController.selected.matchIdx,s={divIdx:-1,offset:void 0},r=a,o=r+1;if(null!==this.findController&&this.findController.state.highlightAll)r=0,o=e.length;else if(!i)return;for(var l=r;l<o;l++){var c,g=e[l],u=g.begin,f=g.end,v=i&&l===a?" selected":"";if(this.findController&&this.findController.selected.matchIdx===l&&this.findController.selected.pageIdx===n&&(c={top:MATCH_SCROLL_OFFSET_TOP,left:MATCH_SCROLL_OFFSET_LEFT},(0,_ui_utils.scrollIntoView)(h[u.divIdx],c,!0)),t&&u.divIdx===t.divIdx?C(t.divIdx,t.offset,u.offset):(null!==t&&C(t.divIdx,t.offset,s.offset),m(u)),u.divIdx===f.divIdx)C(u.divIdx,u.offset,f.offset,"highlight"+v);else{C(u.divIdx,u.offset,s.offset,"highlight begin"+v);for(var p=u.divIdx+1,x=f.divIdx;p<x;p++)h[p].className="highlight middle"+v;m(f,"highlight end"+v)}t=f}t&&C(t.divIdx,t.offset,s.offset)}function m(e,t){var n=e.divIdx;h[n].textContent="",C(n,0,e.offset,t)}function C(e,t,n,i){var a=h[e],s=d[e].substring(t,n),r=document.createTextNode(s);if(i){var o=document.createElement("span");return o.className=i,o.appendChild(r),void a.appendChild(o)}a.appendChild(r)}}},{key:"updateMatches",value:function(){if(this.renderingDone){for(var e,t,n=this.matches,i=this.textDivs,a=this.textContentItemsStr,s=-1,r=0,o=n.length;r<o;r++){for(var d=n[r],h=Math.max(s,d.begin.divIdx),l=d.end.divIdx;h<=l;h++){var c=i[h];c.textContent=a[h],c.className=""}s=d.end.divIdx+1}this.findController&&this.findController.highlightMatches&&(t=e=void 0,null!==this.findController&&(e=this.findController.pageMatches[this.pageIdx]||null,t=this.findController.pageMatchesLength&&this.findController.pageMatchesLength[this.pageIdx]||null),this.matches=this.convertMatches(e,t),this.renderMatches(this.matches))}}},{key:"_bindEvents",value:function(){var n=this,i=this.eventBus,a=this._boundEvents;a.pageCancelled=function(e){if(e.pageNumber===n.pageNumber)if(n.textLayerRenderTask)console.error("TextLayerBuilder._bindEvents: `this.cancel()` should have been called when the page was reset, or rendering cancelled.");else for(var t in a)i.off(t.toLowerCase(),a[t]),delete a[t]},a.updateTextLayerMatches=function(e){e.pageIndex!==n.pageIdx&&-1!==e.pageIndex||n.updateMatches()},i.on("pagecancelled",a.pageCancelled),i.on("updatetextlayermatches",a.updateTextLayerMatches)}},{key:"_bindMouse",value:function(){var a=this,s=this.textLayerDiv,r=null;s.addEventListener("mousedown",function(e){if(a.enhanceTextSelection&&a.textLayerRenderTask)return a.textLayerRenderTask.expandTextDivs(!0),void(r&&(clearTimeout(r),r=null));var t,n,i=s.querySelector(".endOfContent");i&&(e.target!==s&&"none"!==window.getComputedStyle(i).getPropertyValue("-moz-user-select")&&(t=s.getBoundingClientRect(),n=Math.max(0,(e.pageY-t.top)/t.height),i.style.top=(100*n).toFixed(2)+"%"),i.classList.add("active"))}),s.addEventListener("mouseup",function(){var e;a.enhanceTextSelection&&a.textLayerRenderTask?r=setTimeout(function(){a.textLayerRenderTask&&a.textLayerRenderTask.expandTextDivs(!1),r=null},EXPAND_DIVS_TIMEOUT):(e=s.querySelector(".endOfContent"))&&(e.style.top="",e.classList.remove("active"))})}}]),h}(),DefaultTextLayerFactory=function(){function e(){_classCallCheck(this,e)}return _createClass(e,[{key:"createTextLayerBuilder",value:function(e,t,n,i){return new TextLayerBuilder({textLayerDiv:e,pageIndex:t,viewport:n,enhanceTextSelection:3<arguments.length&&void 0!==i&&i})}}]),e}(),PDFLinkService=function(){function r(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:{},t=e.eventBus,n=e.externalLinkTarget,i=void 0===n?null:n,a=e.externalLinkRel,s=void 0===a?null:a;_classCallCheck(this,r),this.eventBus=t||(0,_dom_events.getGlobalEventBus)(),this.externalLinkTarget=i,this.externalLinkRel=s,this.baseUrl=null,this.pdfDocument=null,this.pdfViewer=null,this.pdfHistory=null,this._pagesRefCache=null}return _createClass(r,[{key:"setDocument",value:function(e,t){var n=1<arguments.length&&void 0!==t?t:null;this.baseUrl=n,this.pdfDocument=e,this._pagesRefCache=Object.create(null)}},{key:"setViewer",value:function(e){this.pdfViewer=e}},{key:"setHistory",value:function(e){this.pdfHistory=e}},{key:"navigateTo",value:function(r){var o=this;new Promise(function(t,e){"string"!=typeof r?t({namedDest:"",explicitDest:r}):o.pdfDocument.getDestination(r).then(function(e){t({namedDest:r,explicitDest:e})})}).then(function(e){Array.isArray(e.explicitDest)?function t(e){var n=e.namedDest,i=e.explicitDest,a=i[0],s=void 0;if(a instanceof Object){if(null===(s=o._cachedPageNumber(a)))return void o.pdfDocument.getPageIndex(a).then(function(e){o.cachePageRef(e+1,a),t({namedDest:n,explicitDest:i})}).catch(function(){console.error('PDFLinkService.navigateTo: "'+a+'" is not a valid page reference, for dest="'+r+'".')})}else{if(!Number.isInteger(a))return void console.error('PDFLinkService.navigateTo: "'+a+'" is not a valid destination reference, for dest="'+r+'".');s=a+1}!s||s<1||s>o.pagesCount?console.error('PDFLinkService.navigateTo: "'+s+'" is not a valid page number, for dest="'+r+'".'):(o.pdfHistory&&(o.pdfHistory.pushCurrentPosition(),o.pdfHistory.push({namedDest:n,explicitDest:i,pageNumber:s})),o.pdfViewer.scrollPageIntoView({pageNumber:s,destArray:i}))}(e):console.error('PDFLinkService.navigateTo: "'+e.explicitDest+'" is not a valid destination array, for dest="'+r+'".')})}},{key:"getDestinationHash",value:function(e){if("string"==typeof e)return this.getAnchorUrl("#"+escape(e));if(Array.isArray(e)){var t=JSON.stringify(e);return this.getAnchorUrl("#"+escape(t))}return this.getAnchorUrl("")}},{key:"getAnchorUrl",value:function(e){return(this.baseUrl||"")+e}},{key:"setHash",value:function(e){var t=void 0,n=void 0;if(e.includes("=")){var i,a,s,r=(0,_ui_utils.parseQueryString)(e);if("search"in r&&this.eventBus.dispatch("findfromurlhash",{source:this,query:r.search.replace(/"/g,""),phraseSearch:"true"===r.phrase}),"nameddest"in r)return void this.navigateTo(r.nameddest);"page"in r&&(t=0|r.page||1),"zoom"in r&&(a=(i=r.zoom.split(","))[0],s=parseFloat(a),a.includes("Fit")?"Fit"===a||"FitB"===a?n=[null,{name:a}]:"FitH"===a||"FitBH"===a||"FitV"===a||"FitBV"===a?n=[null,{name:a},1<i.length?0|i[1]:null]:"FitR"===a?5!==i.length?console.error('PDFLinkService.setHash: Not enough parameters for "FitR".'):n=[null,{name:a},0|i[1],0|i[2],0|i[3],0|i[4]]:console.error('PDFLinkService.setHash: "'+a+'" is not a valid zoom value.'):n=[null,{name:"XYZ"},1<i.length?0|i[1]:null,2<i.length?0|i[2]:null,s?s/100:a]),n?this.pdfViewer.scrollPageIntoView({pageNumber:t||this.page,destArray:n,allowNegativeOffset:!0}):t&&(this.page=t),"pagemode"in r&&this.eventBus.dispatch("pagemode",{source:this,mode:r.pagemode})}else{n=unescape(e);try{n=JSON.parse(n),Array.isArray(n)||(n=n.toString())}catch(e){}if("string"==typeof n||isValidExplicitDestination(n))return void this.navigateTo(n);console.error('PDFLinkService.setHash: "'+unescape(e)+'" is not a valid destination.')}}},{key:"executeNamedAction",value:function(e){switch(e){case"GoBack":this.pdfHistory&&this.pdfHistory.back();break;case"GoForward":this.pdfHistory&&this.pdfHistory.forward();break;case"NextPage":this.page<this.pagesCount&&this.page++;break;case"PrevPage":1<this.page&&this.page--;break;case"LastPage":this.page=this.pagesCount;break;case"FirstPage":this.page=1}this.eventBus.dispatch("namedaction",{source:this,action:e})}},{key:"cachePageRef",value:function(e,t){var n;t&&(n=t.num+" "+t.gen+" R",this._pagesRefCache[n]=e)}},{key:"_cachedPageNumber",value:function(e){var t=e.num+" "+e.gen+" R";return this._pagesRefCache&&this._pagesRefCache[t]||null}},{key:"pagesCount",get:function(){return this.pdfDocument?this.pdfDocument.numPages:0}},{key:"page",get:function(){return this.pdfViewer.currentPageNumber},set:function(e){this.pdfViewer.currentPageNumber=e}},{key:"rotation",get:function(){return this.pdfViewer.pagesRotation},set:function(e){this.pdfViewer.pagesRotation=e}}]),r}();function isValidExplicitDestination(e){if(!Array.isArray(e))return!1;var t=e.length,n=!0;if(t<2)return!1;var i=e[0];if(!("object"===(void 0===i?"undefined":_typeof(i))&&Number.isInteger(i.num)&&Number.isInteger(i.gen)||Number.isInteger(i)&&0<=i))return!1;var a=e[1];if("object"!==(void 0===a?"undefined":_typeof(a))||"string"!=typeof a.name)return!1;switch(a.name){case"XYZ":if(5!==t)return!1;break;case"Fit":case"FitB":return 2===t;case"FitH":case"FitBH":case"FitV":case"FitBV":if(3!==t)return!1;break;case"FitR":if(6!==t)return!1;n=!1;break;default:return!1}for(var s=2;s<t;s++){var r=e[s];if(!("number"==typeof r||n&&null===r))return!1}return!0}var TEXT_LAYER_RENDER_DELAY=200,MAX_TEXT_DIVS_TO_RENDER=1e5,NonWhitespaceRegexp=/\S/;