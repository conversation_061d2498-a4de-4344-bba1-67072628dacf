<!DOCTYPE html>
<!--
Copyright (c) 2003-2014, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.md or http://ckeditor.com/license
-->
<html>
<head>
	<meta charset="utf-8">
	<title>UI Color Picker &mdash; CKEditor Sample</title>
	<script src="../../../ckeditor.js"></script>
	<link rel="stylesheet" href="../../../samples/sample.css">
	<meta name="ckeditor-sample-name" content="UIColor plugin">
	<meta name="ckeditor-sample-group" content="Plugins">
	<meta name="ckeditor-sample-description" content="Using the UIColor plugin to pick up skin color.">
</head>
<body>
	<h1 class="samples">
		<a href="../../../samples/index.html">CKEditor Samples</a> &raquo; UI Color Plugin
	</h1>
	<div class="description">
		<p>
			This sample shows how to use the UI Color picker toolbar button to preview the skin color of the editor.
			<strong>Note:</strong>The UI skin color feature depends on the CKEditor skin
			compatibility. The Moono and Kama skins are examples of skins that work with it.
		</p>
	</div>
	<form action="../../../samples/sample_posteddata.php" method="post">
	<div id="ui-color-plugin">
		<p>
			If the <strong>uicolor</strong> plugin along with the dedicated <strong>UIColor</strong>
			toolbar button is added to CKEditor, the user will also be able to pick the color of the
			UI from the color palette available in the <strong>UI Color Picker</strong> dialog window.
		</p>
		<p>
			To insert a CKEditor instance with the <strong>uicolor</strong> plugin enabled,
			use the following JavaScript call:
		</p>
		<pre class="samples">
CKEDITOR.replace( '<em>textarea_id</em>', {
	<strong>extraPlugins: 'uicolor',</strong>
	toolbar: [ [ 'Bold', 'Italic' ], [ <strong>'UIColor'</strong> ] ]
});</pre>
		<h2>Used in themed instance</h2>
		<p>
			Click the <strong>UI Color Picker</strong> toolbar button to open up a color picker dialog.
		</p>
		<p>
			<textarea cols="80" id="editor1" name="editor1" rows="10">&lt;p&gt;This is some &lt;strong&gt;sample text&lt;/strong&gt;. You are using &lt;a href="http://ckeditor.com/"&gt;CKEditor&lt;/a&gt;.&lt;/p&gt;</textarea>
			<script>

				// Replace the <textarea id="editor"> with an CKEditor
				// instance, using default configurations.
				CKEDITOR.replace( 'editor1', {
					extraPlugins: 'uicolor',
					toolbar: [
						[ 'Bold', 'Italic', '-', 'NumberedList', 'BulletedList', '-', 'Link', 'Unlink' ],
						[ 'FontSize', 'TextColor', 'BGColor' ],
						[ 'UIColor' ]
					]
				});

			</script>
		</p>
		<h2>Used in inline instance</h2>
		<p>
			Click the below editable region to display floating toolbar, then click <strong>UI Color Picker</strong> button.
		</p>
		<div id="editor2" contenteditable="true">
			<p>This is some <strong>sample text</strong>. You are using <a data-cke-saved-href="http://ckeditor.com/" href="http://ckeditor.com/">CKEditor</a>.</p>
		</div>
		<script>

			// Disable automatic creation of inline instances.
			CKEDITOR.disableAutoInline = true;

			// Replace the <div id="editor3"> with an inline CKEditor instance.
			CKEDITOR.inline( 'editor2', {
				extraPlugins: 'uicolor',
				toolbar: [
					[ 'Bold', 'Italic', '-', 'NumberedList', 'BulletedList', '-', 'Link', 'Unlink' ],
					[ 'FontSize', 'TextColor', 'BGColor' ],
					[ 'UIColor' ]
				]
			});

		</script>
	</div>
	<p>
		<input type="submit" value="Submit">
	</p>
	</form>
	<div id="footer">
		<hr>
		<p>
			CKEditor - The text editor for the Internet - <a class="samples" href="http://ckeditor.com/">http://ckeditor.com</a>
		</p>
		<p id="copy">
			Copyright &copy; 2003-2014, <a class="samples" href="http://cksource.com/">CKSource</a> - Frederico
			Knabben. All rights reserved.
		</p>
	</div>
</body>
</html>
