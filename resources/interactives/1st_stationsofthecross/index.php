<!DOCTYPE html>
<html>
<head>
	<link rel="stylesheet" type="text/css" href="/interactives/general/css/style.css" />
	<link rel="stylesheet" type="text/css" href="css/jquery-ui.min.css" />
	<link rel="stylesheet" type="text/css" href="css/style.css" />
</head>
<body>
<script>
	(function(i,s,o,g,r,a,m){i["GoogleAnalyticsObject"]=r;i[r]=i[r]||function(){
			(i[r].q=i[r].q||[]).push(arguments)},i[r].l=1*new Date();a=s.createElement(o),
		m=s.getElementsByTagName(o)[0];a.async=1;a.src=g;m.parentNode.insertBefore(a,m)
	})(window,document,"script",'//www.google-analytics.com/analytics.js','ga');

	ga('create', 'UA-2014879-79', 'auto');
	ga('send', 'pageview');

</script>

<?php
$configString = file_get_contents("config.json");
$config = json_decode($configString);
$title = $config->title;
$description = $config->description;
?>

<div class="content">

	<div class="heading">
		<h1><?php echo $title; ?></h1>
		<p style="height:50px;padding-bottom:30px;"></p>
	</div>

	<div class="maze">

		<svg version="1.1" id="Title_and_Buttons" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px"
			 y="0px" viewBox="0 0 463.4 463.4" style="enable-background:new 0 0 463.4 463.4;" xml:space="preserve">
					<style type="text/css">
						.st0{fill:none;stroke:#594634;stroke-width:4;stroke-linecap:round;stroke-linejoin:round;}
						.st1{fill:none;stroke:#594634;stroke-width:8;stroke-linecap:round;stroke-linejoin:round;}
					</style>
			<title>18_stationsofthecross_maze</title>
			<line class="st0" x1="4" y1="49.5" x2="49.5" y2="49.5"/>
			<line class="st0" x1="95.1" y1="49.5" x2="186.1" y2="49.5"/>
			<line class="st0" x1="185" y1="49.5" x2="368.3" y2="49.5"></line>
			<line class="st0" x1="413.8" y1="49.5" x2="459.4" y2="49.5"/>
			<line class="st0" x1="49.5" y1="95.1" x2="95.1" y2="95.1"/>
			<line class="st0" x1="140.1" y1="95.1" x2="184.7" y2="95.1"></line>
			<line class="st0" x1="368.3" y1="95.1" x2="413.8" y2="95.1"/>
			<line class="st0" x1="322.8" y1="140.6" x2="368.3" y2="140.6"/>
			<line class="st0" x1="4" y1="186.1" x2="49.5" y2="186.1"/>
			<line class="st0" x1="186.1" y1="186.1" x2="322.8" y2="186.1"/>
			<line class="st0" x1="368.3" y1="186.1" x2="459.4" y2="186.1"/>
			<line class="st0" x1="50" y1="231.7" x2="95.1" y2="231.7"></line>
			<line class="st0" x1="140.6" y1="231.7" x2="186.1" y2="231.7"/>
			<line class="st0" x1="322.8" y1="231.7" x2="413.8" y2="231.7"/>
			<line class="st0" x1="4" y1="277.2" x2="49.5" y2="277.2"/>
			<line class="st0" x1="140.6" y1="277.2" x2="231.7" y2="277.2"/>
			<line class="st0" x1="277.2" y1="277.2" x2="368.3" y2="277.2"/>
			<line class="st0" x1="49.5" y1="322.8" x2="140.6" y2="322.8"/>
			<line class="st0" x1="231.7" y1="322.8" x2="322.8" y2="322.8"/>
			<line class="st0" x1="49.5" y1="368.3" x2="140.6" y2="368.3"/>
			<line class="st0" x1="186.1" y1="368.3" x2="368.3" y2="368.3"/>
			<line class="st0" x1="4" y1="413.8" x2="49.5" y2="413.8"/>
			<line class="st0" x1="140.6" y1="413.8" x2="186.1" y2="413.8"/>
			<line class="st0" x1="231.7" y1="413.8" x2="277.2" y2="413.8"/>
			<line class="st0" x1="368.3" y1="413.8" x2="413.8" y2="413.8"/>
			<polyline class="st1" points="186.1,4 4,4 4,459.4 231.7,459.4 "/>
			<line class="st0" x1="49.5" y1="95.1" x2="49.5" y2="140.6"/>
			<line class="st0" x1="95.1" y1="49.5" x2="95.1" y2="95.1"/>
			<line class="st0" x1="95.1" y1="140.6" x2="95.1" y2="322.8"/>
			<line class="st0" x1="95.1" y1="368.3" x2="95.1" y2="459.4"/>
			<line class="st0" x1="140.6" y1="95.1" x2="140.6" y2="231.7"/>
			<line class="st0" x1="140.6" y1="277.2" x2="140.6" y2="322.8"/>
			<line class="st0" x1="186.1" y1="49.5" x2="186.1" y2="140.6"/>
			<line class="st0" x1="186.1" y1="231.7" x2="186.1" y2="277.2"/>
			<line class="st0" x1="186.1" y1="322.8" x2="186.1" y2="459.4"/>
			<line class="st0" x1="231.7" y1="95.1" x2="231.7" y2="231.7"/>
			<line class="st0" x1="231.7" y1="277.2" x2="231.7" y2="322.8"/>
			<line class="st0" x1="277.2" y1="49.5" x2="277.2" y2="140.6"/>
			<line class="st0" x1="277.2" y1="231.7" x2="277.2" y2="277.2"/>
			<line class="st0" x1="277.2" y1="413.8" x2="277.2" y2="459.4"/>
			<line class="st0" x1="322.8" y1="95.1" x2="322.8" y2="231.7"/>
			<line class="st0" x1="322.8" y1="368.3" x2="322.8" y2="413.8"/>
			<line class="st0" x1="368.3" y1="49.5" x2="368.3" y2="95.1"/>
			<line class="st0" x1="368.3" y1="231.7" x2="368.3" y2="368.3"/>
			<line class="st0" x1="368.3" y1="413.8" x2="368.3" y2="459.4"/>
			<line class="st0" x1="413.8" y1="95.1" x2="413.8" y2="186.1"/>
			<line class="st0" x1="413.8" y1="277.2" x2="413.8" y2="413.8"/>
			<polyline class="st1" points="231.7,4 459.4,4 459.4,459.4 277.2,459.4 "/>
				</svg>


	</div>

	<div class="paths">
		<div class="start"></div>
		<div class="b3"></div>
	</div>

	<div class="places">
		<div class="p1 active"></div>
		<div class="p2 active"></div>
		<div class="p3"></div>
		<div class="p4 active"></div>
		<div class="p5 active"></div>
	</div>

	<div class="doors">
		<div class="d1" data-door="2"></div>
		<div class="d2" data-door="3"></div>
		<div class="d3" data-door="4"></div>
		<div class="d4" data-door="5"></div>
		<div class="d5" data-door="6"></div>
	</div>

	<div class="character"></div>

	<?php
	$referer = isset($_SERVER["HTTP_REFERER"]) ? $_SERVER["HTTP_REFERER"] : '';

	if($referer != "") {?>
		<div class="cancelBtnHolder">
			<a class="big-btn" onclick="window.close(); return false;" href="<?= $referer ?>"></a>
		</div>
	<?php } ?>

</div>

<div class="question-screen">
	<div class="container">
		<h2 class="question"></h2>
		<input name="answer" type="hidden" value="" />
		<input name="door" type="hidden" value="" />
		<a class="big-btn answer false" href="#">False</a>
		<a class="big-btn answer true" href="#">True</a>
	</div>
</div>

<div class="win-screen">
	<div class="container">
		<div class="win">
			<img src="img/thumb-up.png" />
		</div>
		<h2>Well done!</h2>
		<a class="big-btn replay" href="#">Replay</a>
	</div>
</div>

<div class="loose-screen">
	<div class="container">
		<div class="loose">
			<img src="img/thumb-down.png" />
		</div>
		<h2>Try again!</h2>
		<a class="big-btn replay" href="#">Replay</a>
	</div>
</div>

<div class="start-screen">
	<div class="container">
		<h3>How to play</h3>
		<p>Use the arrows on your keyboard to help Jesus reach the hill of Calvary.</p>
		<div class="row row1 clearfix">
			<div class="col1">
				<img class="" src="img/howto.png" />
			</div>
			<div class="col2">
				<div class="up">
					<img src="img/up.png" alt="Up arrow" />
				</div>
				<div class="left">
					<img src="img/left.png" alt="Left arrow" />
				</div>
				<div class="down">
					<img src="img/down.png" alt="Down arrow" />
				</div>
				<div class="right">
					<img src="img/right.png" alt="Right arrow" />
				</div>

			</div>
		</div>
		<div class="divider"></div>

		<div class="row row2 clearfix">
			<p>Answer a question each time you come to a gate</p>
			<img class="" src="img/stories/door.png" />
		</div>

		<a class="big-btn start" href="#">Play now</a>
	</div>
</div>

<script type="text/javascript" src="js/jquery-1.11.2.min.js"></script>
<script type="text/javascript" src="js/jquery-ui.min.js"></script>
<!--    <script type="text/javascript" src="js/jquery-rotate.js"></script>-->
<script type="text/javascript" src="js/script.js"></script>
<script type="text/javascript" src="/interactives/general/js/script.js"></script>
</body>
</html>