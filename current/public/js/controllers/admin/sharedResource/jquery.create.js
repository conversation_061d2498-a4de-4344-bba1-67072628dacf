//When the page loads detect the value of the dropdown. If it is not theology then
//Do not show the book dropdown. Otherwise detect a change in the dropdown and show
//Book dropdown if category is equal to Theology. 
$(document).ready(function () {


	function UpdateVisibilities(dropdown) {

		var lang = dropdown.data("lang");

		var link_field, file_field;

		$('.form-group.hide').hide();

		if(lang != null) {
			link_field = $('.form-group.link-field.'+lang);
			file_field = $('.form-group.file-field.'+lang);
		} else {
			link_field = $('.form-group.link-field');
			file_field = $('.form-group.file-field');
		}

		if (dropdown.val() == 1 || dropdown.val() == 2) {
			link_field
				.removeClass('hide')
				.show();

			file_field
				.addClass('hide')
				.hide();

		} else if(dropdown.val() == 5) {
			
			link_field
				.addClass('hide')
				.hide();
			
			file_field
				.addClass('hide')
				.hide();
			
		} else {

			link_field
				.addClass('hide')
				.hide();

			file_field
				.removeClass('hide')
				.show();
		}

	}

	$(".choice-select-form-type").each(function() {
		UpdateVisibilities($(this));
	});

	/*
     |--------------------------------------------------------------------------
     | Dropdown for Choice
     |--------------------------------------------------------------------------
     */

	$('.form-group.hide').hide();

	$('.choice-select-form-type').change(function() {

		UpdateVisibilities($(this));
	});

});