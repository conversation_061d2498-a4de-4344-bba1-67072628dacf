// Romanian
plupload.addI18n({
    'Select files' : 'Selectare fişiere',
    'Add files to the upload queue and click the start button.' : 'Adaugă fişiere în lista apoi apasă butonul \'Începe încărcare\'.',
    'Filename' : 'Nume fişier',
    'Status' : 'Stare',
    'Size' : '<PERSON><PERSON><PERSON><PERSON>',
    'Add files' : 'Adăugare fişiere',
    'Stop current upload' : 'Întrerupe încărcarea curentă',
    'Start uploading queue' : 'Începe incărcarea',
    'Uploaded %d/%d files': 'Fişiere încărcate %d/%d',
    'N/A' : 'N/A',
    'Drag files here.' : 'Trage aici fişierele',
    'File extension error.': 'Extensie fişier eronată',
    'File size error.': 'Eroare dimensiune fişier',
    'Init error.': 'Eroare iniţializare',
    'HTTP Error.': 'Eroare HTTP',
    'Security error.': 'Eroare securitate',
    'Generic error.': '<PERSON><PERSON>re generică',
    'IO error.': 'Eroare Intrare/Ieşire',
    'Stop Upload': 'Oprire încărcare',
    'Start upload': 'Începe încărcare',
    '%d files queued': '%d fişiere listate'
});