/****************************/
/* General					*/
/****************************/


body, html {
	background-color: #0b2242;
	/*overflow: hidden;*/
}

.content,.content:before, .content:after {
	background: transparent;
}

h1 {
	color: #fff;
}

h2 {
	color: #333;
}

li:after {
	border-top: 3px solid white;
	width: 95px;
	content: "";
	position: absolute;
	margin-top: 10px;
	margin-left: 16px;
}

ul > li:last-child:after {
	border: none;
}

.hidden {
	display: none;
}

.clearfix:after {
	content: " ";
	display: block; 
	height: 0; 
	clear: both;
}

.heading {
	margin-top: -20px;
	padding-left: 320px;
	padding-right: 80px;
}

.heading p{
	margin-bottom: 30px;
	color: #fff;
}

.score-board {
	position: absolute;
	top: 10px;
	left: 20px;
	z-index: 5;
	width: 303px;
	height: 101px;
	background: url("../img/scorescroll_empty.png") repeat-x;
}

.score-board img {
	float: left;
	width: 101px;
	height: 101px;
}

/****************************/
/* Play area				*/
/****************************/

.content {
	padding: 0;
	overflow: hidden;
}

.heading {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	margin: 0 auto;
	z-index: 5;
}

.lane4 {
	position: absolute;
	top: 0;
	left: 0;
	width: 1024px;
	height: 450px;
	z-index: 1;
	border-radius: 40px 0 0 0;
	-webkit-border-radius: 40px 0 0 0;
	-moz-border-radius: 40px 0 0 0;
	overflow: hidden;
}

.lane4 .wrap {
	width: 2048px;
	height: 450px;
	position: relative;
	border-radius: 40px 0 0 0;
	-webkit-border-radius: 40px 0 0 0;
	-moz-border-radius: 40px 0 0 0;
	overflow: hidden;
}

.lane4 .wrap .lane {
	position: absolute;
	top: -60px;
	left: 0;
	width: 100%;
	height: 100%;
}

.lane3 {
	position: absolute;
	top: 190px;
	left: 0;
	width: 1024px;
	height: 300px;
	z-index: 2;
}

.lane3 .wrap {
	width: 2048px;
	height: 450px;
	position: relative;
	overflow: hidden;
}

.lane3 .wrap .lane {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
}

.lane2 {
	position: absolute;
	top: 320px;
	left: 0;
	width: 1024px;
	height: 300px;
	z-index: 3;
}

.lane2 .wrap {
	width: 2048px;
	height: 450px;
	position: relative;
	overflow: hidden;
}

.lane2 .wrap .lane {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
}

.lane1 {
	position: absolute;
	bottom: 0;
	left: 0;
	width: 1024px;
	height: 300px;
	border-radius: 0 0 40px 0;
	-webkit-border-radius: 0 0 40px 0;
	-moz-border-radius: 0 0 40px 0;
	overflow: hidden;
	z-index: 4;
}

.lane1 .wrap {
	width: 2048px;
	height: 450px;
	position: relative;
	border-radius: 0 0 40px 0;
	-webkit-border-radius: 0 0 40px 0;
	-moz-border-radius: 0 0 40px 0;
	overflow: hidden;
}

.lane1 .wrap .lane {
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
}

.character {
	position: absolute;
	top: 520px;
	left: 0;
	width: 213px;
	height: 250px;
	z-index: 5;
	background: url("../img/character.png") no-repeat;
	background-position: bottom center;
	animation: play .75s steps(24) infinite;
	background-position: 8px;
}

.character.stopped {
	animation: stop;
}

.wrapper {
	position: relative;
}

.holes {
	position: absolute;
	width: 151px;
	height: 151px;
	top: 0;
	left: 1200px;
	z-index: 5;
}

.items {
	position: absolute;
	width: 151px;
	height: 151px;
	top: 0;
	left: 1200px;
	z-index: 6;
}

.lane3 .hole,
.lane3 .item {
	top: 120px;
}

.lane2 .hole,
.lane2 .item {
	top: 140px;
}

.lane1 .hole,
.lane1 .item {
	top: 170px;
}


@keyframes play {
   100% { background-position: -5194px; }
}



/****************************/
/* Win, loose, start screen */
/****************************/

.question-screen,
.win-screen,
.loose-screen,
.start-screen {
	display: none;
	z-index: 5;
	position: absolute;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	overflow: hidden;
	background: rgba(0,0,0,0.5);
}

.start-screen {
	display: block;
}

.question-screen .container,
.win-screen .container,
.loose-screen .container,
.start-screen .container {
	position: absolute;
	background: white;
	border-radius: 10px;
	width: 550px;
	height: 600px;
	top: 50%;
	margin-top: -320px;
	left: 50%;
	margin-left: -275px;
	padding: 30px 60px;
}

.win-screen .container,
.loose-screen .container {
	padding-top: 60px;
}

.start-screen .container {
	height: 680px;
	margin-top: -325px;
}

.start-screen .container h3 {
	margin-bottom: 30px;
}

.start-screen .container .col1,
.start-screen .container .col2 {
	width: 50%;
	margin: 0 auto;
	float: left;
}

.start-screen .container .row1 .col1 img {
	height: 180px;
	/*	border-radius: 50%;*/
}

.start-screen .container .row2 .col1 {
	
	border-right: 2px solid #d5dae0;
	
}

.start-screen .container .col2 .up,
.start-screen .container .col2 .left,
.start-screen .container .col2 .down,
.start-screen .container .col2 .right {
	position: relative;
	width: 55px;
	height: 55px;
	background: #e0d5d1;
	margin: 0 auto;
	padding: 0;
	border-radius: 5px;
	padding-top: 15px;
}

.start-screen .container .col2 .up {
	margin-top: 10px;
}

.start-screen .container .col2 .left,
.start-screen .container .col2 .right {
	background: #efeae8;
}

.start-screen .container .col2 .disabled {
	position: absolute;
	font-weight: 300;
	top: -5px;
	right: -5px;
	width: 20px;
	height: 20px;
	opacity: 1;
	background: #ce4e6d;
	border-radius: 50%;
	font-family: "arial";
	padding: 0;
	line-height: 20px;
	font-size: 12px;
	margin: 0 auto;
	color: white;
	text-align: center;
}

.start-screen .container .col2 .down,
.start-screen .container .col2 .left,
.start-screen .container .col2 .right {
	float: left;
	margin-top: 6px;
	margin-right: 6px;
}

.start-screen .container .col2 .left {
	margin-left: 19px;
}

.start-screen .container .col1 img.rotate {
	-ms-transform: rotate(90deg); /* IE 9 */
	-webkit-transform: rotate(90deg); /* Chrome, Safari, Opera */
	transform: rotate(90deg);
}

.win-screen .container h2,
.loose-screen .container h2 {
	padding: 50px 0 40px 0;
	font-size: 42px;
}

.win-screen .container a,
.loose-screen .container a,
.start-screen .container a {
	width: 100%;
}

.container .win,
.container .loose {
	margin: 0 auto;
	border-radius: 50%;
	width: 180px;
	height: 180px;
	margin-top: 30px;
	margin-bottom: 30px;
}

.container .win {
	background: #79cee8;
}

.container .loose {
	background: #ce4e6d;
}

.start-screen .container .win,
.start-screen .container .loose {
	width: 120px;
	height: 120px;
}

.start-screen .container .win {
	background: #f9b46e;
}

.start-screen .container .divider {
	height: 2px;
	background: #d5dae0;
}

.start-screen .container .win img {
	margin-top: 25px;
}

.start-screen .container .loose img {
	margin-top: 35px;
}

.win-screen .container .win img {
	margin-top: 50px;
}

.loose-screen .container .loose img {
	margin-top: 65px;	
}




/****************************/
/* Buttons					*/
/****************************/

.container .big-btn {
	background: #ac88b5;
	border-bottom: solid 3px #816289;
	/*	margin-top: 40px;*/
}

.container .big-btn.start {
	margin-top: 30px;
}

.container .big-btn:hover {
	border-bottom: solid 3px #9a75a3;
}

.cancelBtnHolder {
	position: absolute;
	top: 10px;
	right: 0;
	margin: auto;
	z-index: 5
}

.cancelBtnHolder .big-btn {
	background-image: url('../../general/img/close.png');
	border-color: #fff;
}
