.top-stories .story,
.bottom-stories .story{
    width: calc(20% - 26px);
    float: left;
    margin-left:12.5px;
    margin-right:12.5px;
    text-align: center;
    padding-left: 12.5px;
    padding-right: 12.5px;
    padding-top: 25px;
    padding-bottom: 25px;
    -moz-border-radius: 4px;
    -webkit-border-radius: 4px;
    border-radius: 4px;
    height: 163px;
    background-color: #fff;
    position: relative;
}

.bottom-stories .story img,
.top-stories .story img {
    width: 100%;
    top: -10px;
    position: relative;
}


.spacer {
    float: left;
    background-position: center;
    background-repeat: no-repeat;
    width:100%;
    height: 150px;
}

.top-stories .story:before,
.bottom-stories .story:before {
  border-radius: 50%;
  position: absolute;
  display: block;
  width: 20px;
  height: 20px;
  content: "";
  background-color: #dbd0cb;
  left: calc(50% - 10px);
}

.top-stories .matched:after {
  width: 4px;
  background-color: #dbd0cb;
  height: 80px;
  bottom: -20px;
  display: block;
  content: '';
  position: relative;
  margin: 0 auto;
}


.top-stories .story:before {
  bottom: -40px;
}

.bottom-stories .story:before {
  top: -50px;
}

.story {
  cursor: pointer;
}

.top-stories .story.purple.active:before,
.story.purple.matched:before, 
.story.purple.matched:after {
    background-color: #816289;
}

.top-stories .story.blue.active:before,
.story.blue.matched:before,
.story.blue.matched:after {
    background-color: #479dc0;
}

.top-stories .story.orange.active:before,
.story.orange.matched:before,
.story.orange.matched:after  {
    background-color: #f7bc80;
}

.top-stories .story.pink.active:before,
.story.pink.matched:before,
.story.pink.matched:after   {
    background-color: #ce4e6d;
}

.top-stories .story.darkblue.active:before,
.story.darkblue.matched:before,
.story.darkblue.matched:after  {
    background-color: #31425b;
}

.bottom-stories .story.active:before {
    background-color: #686262;
}