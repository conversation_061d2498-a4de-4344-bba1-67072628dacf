#SetEnv host 127.0.0.1
#SetEnv database gil_laravel
#SetEnv username gil_laravel
#SetEnv password password
#change

<IfModule mod_rewrite.c>

    <IfModule mod_negotiation.c>
        Options -MultiViews
    </IfModule>

    RewriteEngine On

	RewriteCond %{HTTP_HOST} ^app\.growinlove\.ie$
	RewriteRule (.*) $1 [E=LARAVEL_ENVIRONMENT:production]

	RewriteCond %{HTTP_HOST} ^app\.staging\.growinlove\.ie$
	RewriteRule (.*) $1 [E=LARAVEL_ENVIRONMENT:staging]

	RewriteCond %{HTTP_HOST} ^app\.growinlove\.dev$
	RewriteRule (.*) $1 [E=LARAVEL_ENVIRONMENT:local]

    # Redirect Trailing Slashes...
    RewriteRule ^(.*)/$ /$1 [L,R=301]

    # Handle Front Controller...
    RewriteCond %{REQUEST_FILENAME} !-d
    RewriteCond %{REQUEST_FILENAME} !-f
    RewriteRule ^ index.php [L]
</IfModule>

<IfModule mod_speling.c>
    CheckSpelling On
    CheckCaseOnly On
</IfModule>
