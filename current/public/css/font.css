/* ------------------ ../fonts  -------------------- */
/* Generated by <PERSON>ont Squirrel (http://www.../fontsquirrel.com) on January 14, 2015 */


@font-face {
    font-family: 'CambridgeRound-Semibold';
    src: url('../fonts/cambridge/avp_-_cambridgeround-semibold-webfont.eot');
    src: url('../fonts/cambridge/avp_-_cambridgeround-semibold-webfont.eot?#iefix') format('embedded-opentype'),
         url('../fonts/cambridge/avp_-_cambridgeround-semibold-webfont.woff2') format('woff2'),
         url('../fonts/cambridge/avp_-_cambridgeround-semibold-webfont.woff') format('woff'),
         url('../fonts/cambridge/avp_-_cambridgeround-semibold-webfont.ttf') format('truetype'),
         url('../fonts/cambridge/avp_-_cambridgeround-semibold-webfont.svg#cambridge_roundsemibold') format('svg');
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: 'CambridgeRound-SemiboldIt';
    src: url('../fonts/cambridge/avp_-_cambridgeround-semiboldit-webfont.eot');
    src: url('../fonts/cambridge/avp_-_cambridgeround-semiboldit-webfont.eot?#iefix') format('embedded-opentype'),
         url('../fonts/cambridge/avp_-_cambridgeround-semiboldit-webfont.woff2') format('woff2'),
         url('../fonts/cambridge/avp_-_cambridgeround-semiboldit-webfont.woff') format('woff'),
         url('../fonts/cambridge/avp_-_cambridgeround-semiboldit-webfont.ttf') format('truetype'),
         url('../fonts/cambridge/avp_-_cambridgeround-semiboldit-webfont.svg#cambridge_roundSBdIt') format('svg');
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: 'CambridgeRound-Regular';
    src: url('../fonts/cambridge/avp_-_cambridgeround-regular-webfont.eot');
    src: url('../fonts/cambridge/avp_-_cambridgeround-regular-webfont.eot?#iefix') format('embedded-opentype'),
         url('../fonts/cambridge/avp_-_cambridgeround-regular-webfont.woff2') format('woff2'),
         url('../fonts/cambridge/avp_-_cambridgeround-regular-webfont.woff') format('woff'),
         url('../fonts/cambridge/avp_-_cambridgeround-regular-webfont.ttf') format('truetype'),
         url('../fonts/cambridge/avp_-_cambridgeround-regular-webfont.svg#cambridge_roundregular') format('svg');
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: 'CambridgeRound-LightIt';
    src: url('../fonts/cambridge/avp_-_cambridgeround-lightit-webfont.eot');
    src: url('../fonts/cambridge/avp_-_cambridgeround-lightit-webfont.eot?#iefix') format('embedded-opentype'),
         url('../fonts/cambridge/avp_-_cambridgeround-lightit-webfont.woff2') format('woff2'),
         url('../fonts/cambridge/avp_-_cambridgeround-lightit-webfont.woff') format('woff'),
         url('../fonts/cambridge/avp_-_cambridgeround-lightit-webfont.ttf') format('truetype'),
         url('../fonts/cambridge/avp_-_cambridgeround-lightit-webfont.svg#cambridge_roundlight_italic') format('svg');
    font-weight: normal;
    font-style: normal;

}

@font-face {
    font-family: 'CambridgeRound-Light';
    src: url('../fonts/cambridge/avp_-_cambridgeround-light-webfont.eot');
    src: url('../fonts/cambridge/avp_-_cambridgeround-light-webfont.eot?#iefix') format('embedded-opentype'),
         url('../fonts/cambridge/avp_-_cambridgeround-light-webfont.woff2') format('woff2'),
         url('../fonts/cambridge/avp_-_cambridgeround-light-webfont.woff') format('woff'),
         url('../fonts/cambridge/avp_-_cambridgeround-light-webfont.ttf') format('truetype'),
         url('../fonts/cambridge/avp_-_cambridgeround-light-webfont.svg#cambridge_roundlight') format('svg');
    font-weight: normal;
    font-style: normal;

}

@font-face {
    font-family: 'CambridgeRound-BoldIt';
    src: url('../fonts/cambridge/avp_-_cambridgeround-boldit-webfont.eot');
    src: url('../fonts/cambridge/avp_-_cambridgeround-boldit-webfont.eot?#iefix') format('embedded-opentype'),
         url('../fonts/cambridge/avp_-_cambridgeround-boldit-webfont.woff2') format('woff2'),
         url('../fonts/cambridge/avp_-_cambridgeround-boldit-webfont.woff') format('woff'),
         url('../fonts/cambridge/avp_-_cambridgeround-boldit-webfont.ttf') format('truetype'),
         url('../fonts/cambridge/avp_-_cambridgeround-boldit-webfont.svg#cambridge_roundbold_italic') format('svg');
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: 'CambridgeRound-It';
    src: url('../fonts/cambridge/avp_-_cambridgeround-it-webfont.eot');
    src: url('../fonts/cambridge/avp_-_cambridgeround-it-webfont.eot?#iefix') format('embedded-opentype'),
         url('../fonts/cambridge/avp_-_cambridgeround-it-webfont.woff2') format('woff2'),
         url('../fonts/cambridge/avp_-_cambridgeround-it-webfont.woff') format('woff'),
         url('../fonts/cambridge/avp_-_cambridgeround-it-webfont.ttf') format('truetype'),
         url('../fonts/cambridge/avp_-_cambridgeround-it-webfont.svg#cambridge_rounditalic') format('svg');
    font-weight: normal;
    font-style: normal;
}

@font-face {
    font-family: 'CambridgeRound-Bold';
    src: url('../fonts/cambridge/avp_-_cambridgeround-bold-webfont.eot');
    src: url('../fonts/cambridge/avp_-_cambridgeround-bold-webfont.eot?#iefix') format('embedded-opentype'),
         url('../fonts/cambridge/avp_-_cambridgeround-bold-webfont.woff2') format('woff2'),
         url('../fonts/cambridge/avp_-_cambridgeround-bold-webfont.woff') format('woff'),
         url('../fonts/cambridge/avp_-_cambridgeround-bold-webfont.ttf') format('truetype'),
         url('../fonts/cambridge/avp_-_cambridgeround-bold-webfont.svg#cambridge_roundbold') format('svg');
    font-weight: normal;
    font-style: normal;
}


/* ------------------ General UI -------------------- */