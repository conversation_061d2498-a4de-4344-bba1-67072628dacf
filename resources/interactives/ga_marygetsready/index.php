<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8"> 
    <link rel="stylesheet" type="text/css" href="/interactives/general/css/style.css"/>
    <link rel="stylesheet" type="text/css" href="css/jquery-ui.min.css" />
    <link rel="stylesheet" type="text/css" href="css/style.css" />
</head>
<body>
<script>
	(function(i,s,o,g,r,a,m){i['GoogleAnalyticsObject']=r;i[r]=i[r]||function(){
		(i[r].q=i[r].q||[]).push(arguments)},i[r].l=1*new Date();a=s.createElement(o),
		m=s.getElementsByTagName(o)[0];a.async=1;a.src=g;m.parentNode.insertBefore(a,m)
	})(window,document,'script','https://www.google-analytics.com/analytics.js','ga');

	ga('create', 'UA-2014879-79', 'auto');
	ga('send', 'pageview');

</script>

    <?php 
        $configString = file_get_contents("config.json");
        $config = json_decode($configString);
        $title = $config->title;
        $description = $config->description;
    ?>

    <div class='content'>
        <div class='heading'>
            <h1><?php echo $title; ?></h1>
            <p><?php echo $description; ?></p>
        </div>

        <div class="images-holder">
            <div class="top clearfix">
                <div class="image-container">
                    <img src="">
                </div>
                <div class="image-container">
                    <img src="">
                </div>
            </div>

            <div class="middle clearfix">
                <div class="image-container">
                    <img src="">
                </div>
                <div class="image-container">
                    <img src="">
                </div>
            </div>


            <div class="bottom clearfix">
                <div class="image-container">
                    <img src="">
                </div>
                <div class="image-container">
                    <img src="">
                </div>
            </div>       
        </div>

        <div class="center-drop">
            <div class="heart"></div>
        </div>
        
        <?php
        $referer = isset($_SERVER['HTTP_REFERER']) ? $_SERVER['HTTP_REFERER'] : '';

        if($referer != '') {?>
            <div class='cancelBtnHolder'>
                <a class='big-btn' onclick="window.close(); return false;" href="<?= $referer ?>"></a>
            </div>
        <?php } ?>
    </div>
    
    <script type="text/javascript" src="js/jquery-1.11.2.min.js"></script>
    <script type="text/javascript" src="js/jquery-ui.min.js"></script>
    <script type="text/javascript" src="js/script.js"></script>
</body>
</html>