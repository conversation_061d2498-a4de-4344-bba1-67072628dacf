Array.prototype.clone = function() {
	return this.slice(0);
};

$(function() {

	var pictureSequence = [
		"img/stories/pix_01.png",
		"img/stories/pix_02.png",
		"img/stories/pix_03.png",
		"img/stories/pix_04.png",
		"img/stories/pix_05.png",
	];
	
	var locations = [
		"bethelem",
		"nazareth",
		"jerusalem",
		"sea-of-galilee",
		"jerusalem"
	];

	var audiotypes={
		"mp3": "audio/mpeg",
		"mp4": "audio/mp4",
		"ogg": "audio/ogg",
		"wav": "audio/wav"
	};

	var soundOnePlayed = false;

	var step = 1;
	
	function ss_soundbits(sound, loop) {
		sound = 'sounds/' + sound;

		var audio_element = document.createElement('audio')
		if (audio_element.canPlayType){
			for (var i=0; i == 0 && i<arguments.length; i++){
				for(var key in audiotypes) {
					var source_element = document.createElement('source')
					source_element.setAttribute('src', arguments[i] + "." + key)
					source_element.setAttribute('type', audiotypes[key])
					audio_element.appendChild(source_element);
				}
			}

			//audio_element.play()
			audio_element.playclip=function(){
				this.pause()
				//this.currentTime=0
				this.play()
			}

			if(loop) {
				audio_element.addEventListener('ended', function() {
					ss_soundbits(sound.replace('sounds/', ''), loop).playclip();
				}, false);
			}

			return audio_element
		}
	}

	function shuffleImages() {
		var o = pictureSequence.clone();

		for(var j, x, i = o.length; i; j = Math.floor(Math.random() * i), x = o[--i], o[i] = o[j], o[j] = x);

		$(".step1 .top-stories .story").each(function (i, item) {
			$(this).find("img").first().attr("src", o[i]).attr('original-image', o[i]);
		});

		$(".step2 .top-stories .story").each(function (i, item) {
			
			$(this).find("img").first().attr("src", o[i]).attr('original-image', o[i]);
			
			var src = $(this).find("img").attr("src").replace("pix_", "pic_");
			var location = locations[pictureSequence.indexOf(o[i])];
			
			$(this).find("img").attr("src", src).data("location", location);
			
		});
	}

	function setCurrentStep() {
		$(".steps").hide();
		$(".step" + step).show();
		$(".content").removeClass("step1 step2");
		$(".content").addClass("step" + step);
		$(".scenario-count li").removeClass("active");
		$(".scenario-count li").eq(step-1).addClass("active");
	}

	// Step 1
	$(".step1 > .top-stories .story img").mouseover(function () {
		var src = $(this).attr("src").replace("pix_", "pic_");
		$(this).attr("src", src);
	});

	$(".step1 > .top-stories .story img").mouseout(function () {
		if(!$(this).parent().data('flipped')) {
			var src = $(this).attr("src").replace("pic_", "pix_");
			$(this).attr("src", src);
		}
	});

	$(".step1 > .top-stories .story").draggable({
		revert: true,
		scope: "step1"
	});

	$(".step1 > .top-stories .story").click(function () {
		$(this).data('flipped', true);
	});

	$(".step1 > .bottom-stories .story").droppable({
		scope: "step1",
		drop: function (event, ui) {
			if($(this).find("img").attr('src') != '') {
				return false;
			}

			var src = $(ui.draggable).find('img').attr('original-image');
			src = src.replace("pix_", "pic_");
			$(ui.draggable).css('visibility', 'hidden');

			$(this).find("img").attr('src', src);

			$(this).addClass('filled');

			// lets check if all items has been selected
			var numberChosen = 0;
			$(".step1 .bottom-stories .story img").each(function () {
				if($(this).attr('src').indexOf('boy_jesus') >= 0) {
					numberChosen++;
				}
			});

			$(this).find("p").addClass("hidden");

			ss_soundbits('dragndrop').playclip();
		}
	});

	$(".step1 > .bottom-stories .story").click(function () {
		var src = $(this).find("img").attr('src');

		if(src != '') {
			src = src.replace("pic_", "pix_");
			var targetImageHolder = $(".step1 img[original-image='" + src + "']").parent();
			if($(targetImageHolder).data('flipped')) {
				src = src.replace("pix_", "pic_");
			}
			$(targetImageHolder).css('visibility', '');
			$(targetImageHolder).find('img').attr('src', src);
			$(this).find("img").attr('src', '');
			$(this).removeClass('filled');
			$(this).find("p").removeClass("hidden");
		}
	});

	// Step 2
	/*$(".step2 > .top-stories .story img").mouseover(function () {
		var src = $(this).attr("src").replace("pix_", "pic_");
		$(this).attr("src", src);
	});

	$(".step2 > .top-stories .story img").mouseout(function () {
		if(!$(this).parent().data('flipped')) {
			var src = $(this).attr("src").replace("pic_", "pix_");
			$(this).attr("src", src);
		}
	});*/

	$(".step2 > .top-stories .story").draggable({
		revert: true,
		scope: "step2"
	});

	$(".step2 > .bottom-stories .story").droppable({
		scope: "step2",
		drop: function (event, ui) {
			if($(this).find("img").attr('src') != '') {
				return false;
			}

			var src = $(ui.draggable).find('img').attr('original-image');
			
			var drag_location = $(ui.draggable).find('img').data('location');
			var drop_location = $(this).data('location');
			
			src = src.replace("pix_", "pic_");
			$(ui.draggable).css('visibility', 'hidden');

			$(this).find("img").attr('src', src);

			$(this).addClass('filled');

			// lets check if all items has been selected
			var numberChosen = 0;
			$(".step2 .bottom-stories .story img").each(function () {
				if($(this).attr('src').indexOf('boy_jesus') >= 0) {
					numberChosen++;
				}
			});

			$(this).find("p").addClass("hidden");

			ss_soundbits('dragndrop').playclip();
			
			if(drag_location == drop_location) {
				$(this).data('correct', 1);
			} else {
				$(this).data('correct', 0);
			}
		}
	});

	$(".step2 > .bottom-stories .story").click(function () {
		
		var drop_src = $(this).find("img").attr('src');
		
		if(drop_src != '') {
			$(this).data('correct', 0);
			$(".step2 > .top-stories .story").find("img[src='"+drop_src+"']").parent().css('visibility', 'visible');
			$(this).find("img").attr('src', '');
			$(this).removeClass('filled');
			$(this).find("p").removeClass("hidden");
		}
	});

	$(document).ready(function () {
		
		shuffleImages();

		setCurrentStep();

		$("#resetSequence").click(function (e) {
			window.location.reload();
			e.preventDefault();
			e.stopPropagation();
		});

		$("#verifySequence1").click(function (e) {
			e.stopPropagation();
			e.preventDefault();

			if(!$(this).hasClass('disabled')) {
				var numberCorrect = 0;
				$(".step1 > .bottom-stories .story img").each(function (i, item) {
					var src = $(this).attr("src").replace("pic_", "pix_");

					if(src == pictureSequence[i]) {
						numberCorrect++;
					}
				});

				if(numberCorrect >= pictureSequence.length) {
					if(!soundOnePlayed) {
						ss_soundbits('win').playclip();
					} else {
						ss_soundbits('win2').playclip();
					}
					soundOnePlayed = !soundOnePlayed;

					step = 2;
					setCurrentStep();
					$('.start-screen2').show();
					
				} else {
					ss_soundbits('negative').playclip();
				}
			}
		});

		$("#verifySequence2").click(function (e) {
			e.stopPropagation();
			e.preventDefault();

			if(!$(this).hasClass('disabled')) {
				var numberCorrect = 0;
				$(".step2 > .bottom-stories .story").each(function (i, item) {
					
					var correct = $(this).data("correct");

					if(correct == 1) {
						numberCorrect++;
					}
				});

				if(numberCorrect == 5) {
					if(!soundOnePlayed) {
						ss_soundbits('win').playclip();
					} else {
						ss_soundbits('win2').playclip();
					}
					soundOnePlayed = !soundOnePlayed;
					setTimeout(function () {
						$(".win-screen").show();
					}, 1500);
				} else {
					ss_soundbits('negative').playclip();
				}
			}
		});

		$('.replay').click(function (e) {
			window.location.reload();
			e.preventDefault();
			e.stopPropagation();
		});

		$('.start').click(function (e) {
			e.preventDefault();
			e.stopPropagation();

			$('.start-screen').hide();
		});

		$('.start2').click(function (e) {
			e.preventDefault();
			e.stopPropagation();

			$('.start-screen2').hide();
		});

	});
});