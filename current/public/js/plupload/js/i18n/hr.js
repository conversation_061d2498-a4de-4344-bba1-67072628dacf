// Croatian
plupload.addI18n({
    'Select files': 'Izaberite datoteke:',
    'Add files to the upload queue and click the start button.': 'Dodajte datoteke u listu i kliknite Upload.',
    'Filename': 'Ime datoteke',
    'Status': 'Status',
    'Size': '<PERSON><PERSON><PERSON><PERSON>',
    'Add files': 'Dodajte datoteke',
    'Stop current upload': 'Zaustavi trenutan upload',
    'Start uploading queue': 'Pokreni Upload',
    'Uploaded %d/%d files': 'Uploadano %d/%d datoteka',
    'N/A': 'N/A',
    'Drag files here.': 'Dovucite datoteke ovdje',
    'File extension error.': 'Greška ekstenzije datoteke.',
    'File size error.': 'Greška veličine datoteke.',
    'Init error.': 'Greška inicijalizacije.',
    'HTTP Error.': 'HTTP greška.',
    'Security error.': '<PERSON><PERSON><PERSON><PERSON><PERSON> greš<PERSON>.',
    'Generic error.': '<PERSON>ri<PERSON>ka greška.',
    'IO error.': 'I/O greška.',
    'Stop Upload': 'Zaustavi upload.',
    'Add Files': 'Dodaj datoteke',
    'Start Upload': 'Pokreni upload.',
    '%d files queued': '%d datoteka na čekanju.'
});