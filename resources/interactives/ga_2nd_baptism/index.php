<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<link rel="stylesheet" type="text/css" href="/interactives/general/css/style.css"/>
	<link rel="stylesheet" type="text/css" href="css/jquery-ui.min.css" />
	<link rel="stylesheet" type="text/css" href="css/style.css" />
</head>
<body>
<!--<script>
(function(i,s,o,g,r,a,m){i['GoogleAnalyticsObject']=r;i[r]=i[r]||function(){
(i[r].q=i[r].q||[]).push(arguments)},i[r].l=1*new Date();a=s.createElement(o),
m=s.getElementsByTagName(o)[0];a.async=1;a.src=g;m.parentNode.insertBefore(a,m)
})(window,document,'script','https://www.google-analytics.com/analytics.js','ga');

ga('create', 'UA-2014879-79', 'auto');
ga('send', 'pageview');

</script>-->

<?php
$configString = file_get_contents("config.json");
$config = json_decode($configString);
$title = $config->title;
$description = $config->description;
?>

<div class="content">
	<div class="heading">

	</div>

	<div class="content-holder">

		<div class="timer-holder">
			<h1>0:<span class="countdown">40</span></h1>
		</div>

		<div class="image-holder">
			<div class="image-holder-wrapper">

				<div class="pic">
					<svg class="blur" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="100%" height="100%">
						<defs>
							<mask id="mask1">
								<circle cx="-50%" cy="-50%" r="70" fill="white" />
							</mask>
							<mask id="mask2">
								<rect x="0" y="0" width="100%" height="100%" fill="white" />
								<circle cx="-50%" cy="-50%" r="70" fill="black" />
							</mask>
						</defs>
						<image mask="url(#mask1)" xlink:href="img/stories/pic.png" width="100%" height="100%" ></image>
						<image mask="url(#mask2)" xlink:href="img/stories/pic_mask.png" width="100%" height="100%" ></image>
					</svg>
				</div>

			</div>
		</div>

		<div class="status-holder">

			<div class="items item1">
				<img src="img/stories/item1.png" />
				<p>Uisce Coisricthe</p>
				<div class="status"></div>
			</div>

			<div class="items item2">
				<img src="img/stories/item2.png" />
				<p>Seál Baiste</p>
				<div class="status"></div>
			</div>

			<div class="items item3">
				<img src="img/stories/item3.png" />
				<p>Coinneal</p>
				<div class="status"></div>
			</div>

			<div class="items item4">
				<img src="img/stories/item4.png" />
				<p>Umar Baiste</p>
				<div class="status"></div>
			</div>

		</div>

	</div>


	<?php
	$referer = isset($_SERVER["HTTP_REFERER"]) ? $_SERVER["HTTP_REFERER"] : '';

	if($referer != "") {?>
		<div class='cancelBtnHolder'>
			<a class='big-btn' onclick="window.close(); return false;" href="<?= $referer ?>"></a>
		</div>
	<?php } ?>

</div>

<div class="win-screen">
	<div class="container">
		<div class="win">
			<img src="img/thumb-up.png" />
		</div>
		<h2>An-mhaith!</h2>
		<a class="big-btn replay" href="#">Imir Arís</a>
	</div>
</div>

<div class="loose-screen">
	<div class="container">
		<div class="loose">
			<img src="img/thumb-down.png" />
		</div>
		<h2>Déan iarracht arís!</h2>
		<a class="big-btn replay" href="#">Imir Arís</a>
	</div>
</div>

<div class="start-screen">
	<div class="container">
		<h3>Conas an cluiche a imirt</h3>
		<p>Bain úsáid as an luch chun ainliú thar an tábla. Beidh spotsolas le feiceáil chun na míreanna atá air a thaispeáint duit.</p>
		<div class="divider"></div>
		<p>Cliceáil ar na míreanna a bhfuil gá leo le haghaidh an bhaisteadh</p>
		<div class="divider"></div>
		<p>Tá 40 soicind agat chun an tasc a chur i gcrích</p>
		<div class="divider"></div>
		<a class="big-btn start" href="#">Imir Anois</a>
	</div>
</div>

<script type="text/javascript" src="js/jquery-1.11.2.min.js"></script>
<script type="text/javascript" src="js/jquery-ui.min.js"></script>
<script type="text/javascript" src="js/script.js"></script>
<script type="text/javascript" src="/interactives/general/js/script.js"></script>
</body>
</html>
