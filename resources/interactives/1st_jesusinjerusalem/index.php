<!DOCTYPE html>
<html>
<head>
	<link rel="stylesheet" type="text/css" href="/interactives/general/css/style.css" />
	<link rel="stylesheet" type="text/css" href="css/jquery-ui.min.css" />
	<link rel="stylesheet" type="text/css" href="css/style.css" />
</head>
<body>
<script>
	(function(i,s,o,g,r,a,m){i["GoogleAnalyticsObject"]=r;i[r]=i[r]||function(){
			(i[r].q=i[r].q||[]).push(arguments)},i[r].l=1*new Date();a=s.createElement(o),
		m=s.getElementsByTagName(o)[0];a.async=1;a.src=g;m.parentNode.insertBefore(a,m)
	})(window,document,"script",'//www.google-analytics.com/analytics.js','ga');

	ga('create', 'UA-2014879-79', 'auto');
	ga('send', 'pageview');

</script>

<?php
$configString = file_get_contents("config.json");
$config = json_decode($configString);
$title = $config->title;
$description = $config->description;
?>

<div class="content">

	<div class="heading">
		<h1><?php echo $title; ?></h1>
		<p><?php echo $description; ?></p>
	</div>

	<div class="maze">

		<svg version="1.1" id="Title_and_Buttons" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px"
			 y="0px" viewBox="0 0 480 480" style="enable-background:new 0 0 480 480;" xml:space="preserve">
					<defs>
						<style>
							.cls-1,.cls-2{fill:none;stroke:#594634;stroke-linecap:round;stroke-linejoin:round;}.cls-1{stroke-width:4px;}.cls-2{stroke-width:8px;}
							.st0{fill:none;stroke:#594634;stroke-width:4;stroke-linecap:round;stroke-linejoin:round;}
							.st1{fill:none;stroke:#594634;stroke-width:8;stroke-linecap:round;stroke-linejoin:round;}
						</style>
					</defs>
			<title>18_stationsofthecross_maze_map</title>
			<g>
				<title>background</title>
				<rect fill="none" id="canvas_background" height="482" width="482" y="-1" x="-1"/>
			</g>
			<title>18_stationsofthecross_maze_map</title>
			<g>
				<rect stroke="null" id="svg_52" height="46.773076" width="92.820483" y="421.025504" x="193.076938" fill-opacity="null" stroke-opacity="null" stroke-width="null" fill="#fff2ca"/>
				<rect stroke="null" id="svg_51" height="186.153785" width="135.897388" y="282.05119" x="58.205189" fill-opacity="null" stroke-opacity="null" stroke-width="null" fill="#fff2ca"/>
				<rect stroke="null" id="svg_50" height="227.273358" width="91.186432" y="149.393688" x="12.06647" fill-opacity="null" stroke-opacity="null" stroke-width="null" fill="#fff2ca"/>
				<rect stroke="null" id="svg_49" height="182.325603" width="228.372122" y="102.79071" x="103.023237" stroke-opacity="null" stroke-width="null" fill="#fff2ca"/>
				<rect stroke="null" id="svg_48" height="46.654454" width="91.923077" y="283.730162" x="375.961533" stroke-opacity="null" stroke-width="null" fill="#ded09f"/>
				<rect stroke="null" id="svg_47" height="181.578945" width="136.315792" y="103.421054" x="331.052632" stroke-opacity="null" stroke-width="null" fill="#ded09f"/>
				<rect stroke="null" id="svg_46" height="92.526313" width="226.631576" y="10.5" x="193.868431" stroke-opacity="null" stroke-width="null" fill="#fff2ca"/>
				<line class="st0" x1="12.3" y1="376.6" x2="103.4" y2="376.6"/>
				<line class="st0" x1="285.5" y1="57.9" x2="331.1" y2="57.9"/>
				<line class="st0" x1="331.1" y1="103.4" x2="467.7" y2="103.4"/>
				<line class="st0" x1="148.9" y1="148.9" x2="240" y2="148.9"/>
				<line class="st0" x1="331.1" y1="148.9" x2="422.1" y2="148.9"/>
				<line class="st0" x1="12.7" y1="194.5" x2="194.5" y2="194.5"/>
				<line class="st0" x1="285.5" y1="194.5" x2="376.6" y2="194.5"/>
				<line class="st0" x1="148.9" y1="240" x2="285.5" y2="240"/>
				<line class="st0" x1="376.6" y1="240" x2="422.1" y2="240"/>
				<line class="st0" x1="12.3" y1="331.1" x2="57.9" y2="331.1"/>
				<line class="st0" x1="148.9" y1="376.6" x2="194.5" y2="376.6"/>
				<line class="st0" x1="103.4" y1="422.1" x2="148.9" y2="422.1"/>
				<line class="st0" x1="57.9" y1="194.5" x2="57.9" y2="285.5"/>
				<line class="st0" x1="103.4" y1="330.4" x2="103.4" y2="422.1"/>
				<line class="st0" x1="103.4" y1="240" x2="103.4" y2="330.4"/>
				<line class="st0" x1="148.9" y1="240" x2="148.9" y2="376.6"/>
				<polyline class="st1" points="375.8,12.3 194.5,12.3 194.5,103.4 192.6,103.4 103.4,103.4 103.4,148.9 102.3,148.9 12.3,148.9
													  12.3,376.6 12.3,376.7 57.9,376.7 57.9,376.6 57.9,467.7 240,467.7 "/>
				<line class="st0" x1="240" y1="12.3" x2="240" y2="194.5"/>
				<line class="st0" x1="285.5" y1="103.4" x2="285.5" y2="194.5"/>
				<line class="st0" x1="331.1" y1="57.9" x2="331.1" y2="148.9"/>
				<line class="st0" x1="331.1" y1="194.5" x2="331.1" y2="285.5"/>
				<line class="st0" x1="376.6" y1="12.3" x2="376.6" y2="57.9"/>
				<line class="st0" x1="422.1" y1="148.9" x2="422.1" y2="285.5"/>
				<polyline class="st1" points="285.5,467.7 285.5,422.1 194.5,422.1 194.5,285.5 376.6,285.5 376.6,331.1 467.7,331.1 467.7,103.4
													  422.1,103.4 422.1,12.3 "/>
			</g>
				</svg>

	</div>

	<div class="paths">
		<div class="start"></div>
	</div>

	<div class="buildings">
		<div class="b1 active"></div>
		<div class="b2 active"></div>
		<div class="b3 active"></div>
		<div class="b4"></div>
		<div class="b5 active"></div>
		<div class="b6 active"></div>
	</div>

	<div class="doors">
		<div class="d1" data-door="2"></div>
		<div class="d2" data-door="3"></div>
		<div class="d3" data-door="4"></div>
		<div class="d4" data-door="5"></div>
		<div class="d5" data-door="6"></div>
	</div>

	<div class="character"></div>

	<?php
	$referer = isset($_SERVER["HTTP_REFERER"]) ? $_SERVER["HTTP_REFERER"] : '';

	if($referer != "") {?>
		<div class="cancelBtnHolder">
			<a class="big-btn" onclick="window.close(); return false;" href="<?= $referer ?>"></a>
		</div>
	<?php } ?>

</div>

<div class="question-screen">
	<div class="container">
		<h2 class="question"></h2>
		<input name="answer" type="hidden" value="" />
		<input name="door" type="hidden" value="" />
		<a class="big-btn answer false" href="#">False</a>
		<a class="big-btn answer true" href="#">True</a>
	</div>
</div>

<div class="win-screen">
	<div class="container">
		<div class="win">
			<img src="img/thumb-up.png" />
		</div>
		<h2>Well done!</h2>
		<a class="big-btn replay" href="#">Replay</a>
	</div>
</div>

<div class="loose-screen">
	<div class="container">
		<div class="loose">
			<img src="img/thumb-down.png" />
		</div>
		<h2>Try again!</h2>
		<a class="big-btn replay" href="#">Replay</a>
	</div>
</div>

<div class="start-screen">
	<div class="container">
		<h3>How to play</h3>
		<p>Use the arrows on your keyboard to reach the hill of Calvary.</p>
		<div class="row row1 clearfix">
			<div class="col1">
				<img class="" src="img/howto.png" />
			</div>
			<div class="col2">
				<div class="up">
					<img src="img/up.png" alt="Up arrow" />
				</div>
				<div class="left">
					<img src="img/left.png" alt="Left arrow" />
				</div>
				<div class="down">
					<img src="img/down.png" alt="Down arrow" />
				</div>
				<div class="right">
					<img src="img/right.png" alt="Right arrow" />
				</div>

			</div>
		</div>
		<div class="divider"></div>

		<div class="row row2 clearfix">
			<p>Unlock the gates by answering to a question</p>
			<img class="" src="img/stories/18_stationsofthecross_maze_door.png" />
		</div>

		<a class="big-btn start" href="#">Play now</a>
	</div>
</div>

<script type="text/javascript" src="js/jquery-1.11.2.min.js"></script>
<script type="text/javascript" src="js/jquery-ui.min.js"></script>
<!--    <script type="text/javascript" src="js/jquery-rotate.js"></script>-->
<script type="text/javascript" src="js/script.js"></script>
<script type="text/javascript" src="/interactives/general/js/script.js"></script>
</body>
</html>