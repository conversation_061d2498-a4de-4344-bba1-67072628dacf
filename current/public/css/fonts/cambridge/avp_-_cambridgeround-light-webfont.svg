<?xml version="1.0" standalone="no"?>
<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd" >
<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1">
<metadata></metadata>
<defs>
<font id="cambridge_roundlight" horiz-adv-x="1032" >
<font-face units-per-em="2048" ascent="1638" descent="-410" />
<missing-glyph horiz-adv-x="450" />
<glyph horiz-adv-x="0" />
<glyph horiz-adv-x="682" />
<glyph unicode=" "  horiz-adv-x="450" />
<glyph unicode="&#x09;" horiz-adv-x="450" />
<glyph unicode="&#xa0;" horiz-adv-x="450" />
<glyph unicode="!" horiz-adv-x="520" d="M174 66q0 36 23.5 57.5t62.5 21.5t62.5 -21.5t23.5 -57.5q0 -39 -23.5 -60.5t-62.5 -21.5t-62.5 21.5t-23.5 60.5zM207 1362q0 12 9.5 21.5t21.5 9.5h45q12 0 21 -9.5t9 -21.5l-20 -979q0 -13 -8.5 -22t-24.5 -9q-33 0 -33 31z" />
<glyph unicode="&#x22;" horiz-adv-x="657" d="M156 1360q0 14 8.5 23.5t21.5 9.5h58q13 0 21.5 -9.5t8.5 -23.5q-16 -287 -20 -451q0 -12 -9.5 -21t-21.5 -9h-16q-12 0 -21.5 8t-9.5 20zM385 1362q0 14 8.5 22.5t22.5 8.5h57q13 0 22 -8.5t9 -22.5q-6 -121 -11 -228t-7 -149t-3 -76q0 -12 -9 -21t-21 -9h-17 q-26 0 -30 28t-6 65.5t-3.5 74.5t-4.5 131.5t-7 183.5z" />
<glyph unicode="#" horiz-adv-x="1191" d="M59 260v33q0 12 9.5 21.5t21.5 9.5h201q5 29 30.5 174t40.5 231h-184q-12 0 -21.5 9.5t-9.5 21.5v33q0 12 9.5 21t21.5 9h201l51 297q6 25 29 25l39 -6q24 -6 24 -29v-10l-49 -277h352l52 297q6 25 28 25l39 -6q25 -6 25 -29v-10l-47 -277h184q12 0 21.5 -9t9.5 -21v-33 q0 -12 -9.5 -21.5t-21.5 -9.5h-201q-5 -29 -30.5 -174t-40.5 -231h184q12 0 21.5 -9.5t9.5 -21.5v-33q0 -12 -9.5 -21.5t-21.5 -9.5h-203l-51 -297q-1 -10 -10.5 -18t-20.5 -8q-2 0 -39 6q-10 1 -16 11.5t-6 21.5l49 284h-352l-52 -297q-1 -10 -10 -18t-20 -8q-2 0 -39 6 q-23 6 -23 31l49 286h-184q-12 0 -21.5 9.5t-9.5 21.5zM385 324h352l72 405h-352q-10 -56 -34 -189.5t-38 -215.5z" />
<glyph unicode="$" horiz-adv-x="997" d="M119 106q0 1 1 3.5t1 3.5l20 49q10 16 27 16q10 0 16 -4q141 -88 291 -88q133 0 209 70t76 186q0 39 -10.5 73.5t-34 66t-45.5 55t-63 52t-66.5 44.5t-73.5 45q-83 51 -131.5 85.5t-100 84.5t-75 105.5t-23.5 121.5q0 133 82 218.5t221 109.5v164q0 12 9.5 21t21.5 9h47 q12 0 21.5 -9t9.5 -21v-158q129 -8 229 -61q7 -3 13 -12t6 -17q0 -1 -1 -3t-1 -3l-13 -43q-10 -27 -28 -27q-5 0 -17 4q-120 60 -223 60q-116 0 -190 -60.5t-74 -167.5q0 -32 9.5 -61t31.5 -57t42.5 -49t58.5 -48t62 -42.5t70 -43.5q50 -30 73 -45t68 -46.5t68 -53 t54.5 -56.5t47 -67t27.5 -74.5t12 -87.5q0 -147 -90.5 -242.5t-234.5 -117.5v-160q0 -12 -9.5 -21.5t-21.5 -9.5h-47q-12 0 -21.5 9.5t-9.5 21.5v158q-175 13 -305 94q-16 8 -16 26z" />
<glyph unicode="%" horiz-adv-x="1675" d="M90 979q0 121 35.5 218.5t102 154.5t151.5 57q86 0 153 -57.5t102.5 -155t35.5 -217.5q0 -187 -82 -308.5t-209 -121.5q-126 0 -207.5 122.5t-81.5 307.5zM186 979q0 -146 55.5 -248t137.5 -102q81 0 137.5 102.5t56.5 247.5q0 146 -56 249t-138 103t-137.5 -103 t-55.5 -249zM385 29q0 9 6 18q81 135 179.5 297t274.5 451.5t353 580.5q3 5 12 10t15 5h26q14 0 23.5 -8.5t9.5 -20.5q0 -10 -6 -19l-805 -1329q-3 -5 -12 -9.5t-15 -4.5h-28q-14 0 -23.5 8.5t-9.5 20.5zM1006 414q0 185 80 307.5t208 122.5q130 0 210.5 -122.5t80.5 -307.5 t-82 -307.5t-209 -122.5q-125 0 -206.5 122.5t-81.5 307.5zM1104 414q0 -146 54.5 -248.5t135.5 -102.5q83 0 140 102.5t57 248.5t-56.5 249t-140.5 103q-81 0 -135.5 -103t-54.5 -249z" />
<glyph unicode="&#x26;" horiz-adv-x="1419" d="M127 317q0 217 334 437v8q-201 209 -201 360q0 130 94.5 208.5t229.5 78.5q130 0 223.5 -82t93.5 -209q0 -100 -75.5 -197.5t-204.5 -179.5v-8l378 -409q147 173 252 362q12 16 27 16l43 -10q12 -1 19.5 -9t7.5 -19q0 -2 -5 -17q-73 -132 -137.5 -225t-138.5 -170 l193 -203q10 -10 10 -20q0 -11 -10 -20t-23 -9h-43q-30 0 -55 25l-148 153q-118 -106 -223 -151t-227 -45q-175 0 -294.5 92.5t-119.5 242.5zM233 319q0 -99 91 -169t213 -70q107 0 197 39.5t190 132.5l-398 432q-142 -101 -217.5 -189t-75.5 -176zM365 1126 q0 -108 190 -307q117 77 178.5 157t61.5 146q0 85 -61 137t-154 52q-87 0 -151 -51.5t-64 -133.5z" />
<glyph unicode="'" horiz-adv-x="466" d="M174 1360q0 14 9 23.5t22 9.5h57q13 0 22 -9.5t9 -23.5q-19 -367 -21 -451q0 -12 -9 -21t-21 -9h-13q-12 0 -21 8t-9 20z" />
<glyph unicode="(" horiz-adv-x="618" d="M184 483q0 512 258 887q13 21 41 21h25q20 0 20 -17q0 -9 -10 -26q-48 -79 -85 -155.5t-74 -184t-57.5 -241t-20.5 -284.5t20 -284t58 -240t74 -182.5t85 -155.5q10 -17 10 -29q0 -16 -20 -16h-25q-27 0 -41 23q-258 375 -258 884z" />
<glyph unicode=")" horiz-adv-x="618" d="M90 -408q0 12 10 29q49 80 85.5 155.5t74 182.5t58 240.5t20.5 283.5q0 151 -20.5 284.5t-58 241t-74 183.5t-85.5 156q-10 17 -10 26q0 17 21 17h24q28 0 41 -21q258 -375 258 -887q0 -509 -258 -884q-14 -23 -41 -23h-24q-21 0 -21 16z" />
<glyph unicode="*" horiz-adv-x="980" d="M66 895q0 25 22 31l311 86v22q-280 80 -311 88q-22 6 -22 29q0 8 2 12l26 62q6 18 27 18q10 0 16 -4l279 -160l22 21q-137 231 -162 278q-4 8 -4 15q0 19 19 28l61 27q4 2 13 2q21 0 28 -23l88 -309h27l80 309q7 23 30 23q9 0 13 -2l63 -27q19 -6 19 -26q0 -11 -4 -17 l-158 -278l20 -21q236 137 277 160q12 4 16 4q21 0 27 -18l27 -62q2 -4 2 -12q0 -22 -23 -29l-309 -88v-24l309 -84q23 -7 23 -31q0 -8 -2 -12l-27 -62q-6 -18 -27 -18q-10 0 -16 4l-279 158l-18 -19l158 -278q4 -6 4 -17q0 -20 -19 -26l-61 -27q-4 -2 -12 -2q-23 0 -29 22 l-86 310h-25l-86 -310q-6 -22 -30 -22q-9 0 -13 2l-61 27q-19 6 -19 26q0 11 4 17l162 278l-20 19q-244 -138 -279 -158q-6 -4 -16 -4q-20 0 -29 18l-26 62q-2 4 -2 12z" />
<glyph unicode="+" horiz-adv-x="1196" d="M113 469v33q0 12 9 21t21 9h408v408q0 12 9.5 21.5t21.5 9.5h32q12 0 21.5 -9.5t9.5 -21.5v-408h408q12 0 21 -9t9 -21v-33q0 -12 -9 -21.5t-21 -9.5h-408v-407q0 -12 -9.5 -21.5t-21.5 -9.5h-32q-12 0 -21.5 9.5t-9.5 21.5v407h-408q-12 0 -21 9.5t-9 21.5z" />
<glyph unicode="," horiz-adv-x="434" d="M70 -299q0 4 4 16q104 186 104 355q0 32 22.5 52.5t53.5 20.5t53.5 -20.5t22.5 -52.5q0 -141 -178 -402q-9 -12 -25 -12q-6 0 -14 4l-27 14q-16 10 -16 25z" />
<glyph unicode="-" horiz-adv-x="831" d="M115 455v39q0 12 9 21t21 9h543q12 0 21.5 -9t9.5 -21v-39q0 -12 -9.5 -21.5t-21.5 -9.5h-543q-12 0 -21 9.5t-9 21.5z" />
<glyph unicode="." horiz-adv-x="448" d="M174 66q0 36 23.5 57.5t62.5 21.5t62.5 -21.5t23.5 -57.5q0 -39 -23.5 -60.5t-62.5 -21.5t-62.5 21.5t-23.5 60.5z" />
<glyph unicode="/" horiz-adv-x="739" d="M66 -119q0 9 2 13l505 1476q3 8 11.5 14.5t17.5 6.5h41q13 0 22 -8.5t9 -20.5q0 -8 -2 -12l-506 -1477q-8 -20 -29 -20h-41q-13 0 -21.5 8t-8.5 20z" />
<glyph unicode="0" horiz-adv-x="1052" d="M78 694q0 200 54.5 362.5t157.5 257.5t236 95q132 0 235 -95t158.5 -257.5t55.5 -362.5q0 -201 -56.5 -363.5t-159.5 -255.5t-233 -93t-232.5 93t-159 255.5t-56.5 363.5zM188 694q0 -168 44.5 -308.5t122.5 -222t171 -81.5t171 81.5t122.5 222t44.5 308.5 q0 167 -44 307.5t-121.5 223t-172.5 82.5t-172.5 -82.5t-121.5 -223t-44 -307.5z" />
<glyph unicode="1" horiz-adv-x="1060" d="M145 1106q0 18 15 29l346 237q34 21 59 21h21q12 0 21 -9.5t9 -21.5v-1264h304q12 0 21 -9t9 -21v-37q0 -12 -9 -21.5t-21 -9.5h-715q-12 0 -21.5 9.5t-9.5 21.5v37q0 12 9.5 21t21.5 9h301v1168l-299 -215q-11 -7 -21 -7q-20 0 -26 19q0 1 -7 18.5t-8 24.5z" />
<glyph unicode="2" horiz-adv-x="1064" d="M141 31v26q0 18 13 31q274 311 342 395q100 121 169 222.5t98 166.5t39.5 101.5t10.5 60.5q0 130 -78.5 204.5t-202.5 74.5q-132 0 -301 -137q-9 -9 -20 -9q-22 0 -29 27q-5 23 -8 29l-4 20q0 17 12 25q96 73 179.5 110t170.5 37q170 0 280 -103t110 -286 q0 -92 -88 -250.5t-256 -355.5l-279 -322h592q12 0 21.5 -9t9.5 -21v-37q0 -12 -9.5 -21.5t-21.5 -9.5h-719q-12 0 -21.5 9.5t-9.5 21.5z" />
<glyph unicode="3" horiz-adv-x="1060" d="M125 115q0 1 1 4t1 4l12 43q6 22 27 22q4 0 16 -4q181 -100 320 -100q147 0 236 79t89 218q0 131 -93 204t-314 77q-13 2 -22 10t-9 20v41q0 12 8.5 21.5t20.5 9.5q154 6 263.5 93t109.5 200q0 110 -74 180t-197 70q-121 0 -301 -97q-12 -4 -16 -4q-20 0 -27 21l-12 43 q0 1 -1 4t-1 4q0 23 16 29q199 102 342 102q164 0 272.5 -97t108.5 -249q0 -95 -60 -188t-169 -142v-10q129 -33 197.5 -124t68.5 -218q0 -182 -120.5 -289.5t-315.5 -107.5q-167 0 -361 104q-16 6 -16 27z" />
<glyph unicode="4" horiz-adv-x="1052" d="M88 414v41q0 19 12 39l605 884q10 15 24 15h53q12 0 21.5 -9.5t9.5 -21.5v-881h119q12 0 21.5 -9t9.5 -21v-37q0 -12 -9.5 -21.5t-21.5 -9.5h-119v-352q0 -12 -9.5 -21.5t-21.5 -9.5h-41q-12 0 -21 9.5t-9 21.5v352h-592q-12 0 -21.5 9.5t-9.5 21.5zM219 477q86 4 168 4 h324v443q0 162 10 309h-14l-488 -741v-15z" />
<glyph unicode="5" horiz-adv-x="1064" d="M162 53q2 25 12 47l4 9q6 16 27 16q1 0 4 -1t4 -1q136 -39 240 -39q141 0 242.5 106.5t101.5 255.5q0 159 -90.5 261t-253.5 102q-100 0 -191 -23q-2 0 -5.5 -1t-4.5 -1q-12 0 -20.5 9.5t-8.5 21.5l43 549q2 12 10.5 20.5t20.5 8.5h541q12 0 21 -9.5t9 -21.5v-35 q0 -12 -9 -21.5t-21 -9.5h-476q-9 -119 -32 -393q76 6 123 6q210 0 332 -130t122 -333q0 -190 -133 -326t-321 -136q-131 0 -269 41q-22 7 -22 28z" />
<glyph unicode="6" horiz-adv-x="1050" d="M68 446q0 112 30.5 221t79 199.5t121 182t143.5 162.5t160 147q25 22 42 28.5t42 6.5h31q15 0 24 -9.5t9 -21.5q0 -15 -13 -25q-285 -229 -448 -483l6 -8q110 65 229 65q201 0 328 -128t127 -337q0 -206 -127 -334t-328 -128q-202 0 -329 128t-127 334zM178 446 q0 -162 94 -262t252 -100q157 0 250.5 99t93.5 263q0 163 -92.5 263t-251.5 100q-179 0 -262.5 -96t-83.5 -267z" />
<glyph unicode="7" horiz-adv-x="1050" d="M117 1004v358q0 12 9 21.5t21 9.5h828q12 0 21.5 -9.5t9.5 -21.5v-27q0 -17 -9 -39q-489 -1069 -583 -1278q-3 -6 -12.5 -12t-16.5 -6h-53q-14 0 -23.5 8.5t-9.5 20.5q0 6 4 14l582 1253h-674v-292q0 -12 -9.5 -21.5t-21.5 -9.5h-33q-12 0 -21 9.5t-9 21.5z" />
<glyph unicode="8" horiz-adv-x="1052" d="M113 344q0 259 297 391v8q-114 55 -186 138.5t-72 191.5q0 143 104.5 239.5t269.5 96.5t270 -96.5t105 -239.5q0 -108 -72 -191.5t-186 -138.5v-8q297 -133 297 -391q0 -157 -116 -258.5t-298 -101.5t-297.5 101.5t-115.5 258.5zM223 344q0 -119 84 -188.5t219 -69.5 q134 0 218.5 70t84.5 188q0 113 -85 201t-218 135q-133 -47 -218 -135t-85 -201zM262 1073q0 -90 71.5 -162.5t192.5 -113.5q121 41 193 113.5t72 162.5q0 108 -77.5 171t-187.5 63t-187 -63t-77 -171z" />
<glyph unicode="9" horiz-adv-x="1050" d="M68 946q0 206 127 334.5t327 128.5q202 0 329.5 -128.5t127.5 -334.5q0 -112 -30.5 -221t-79 -199t-121.5 -181.5t-144 -162.5t-160 -147q-25 -22 -42 -28.5t-42 -6.5h-30q-14 0 -23.5 9t-9.5 22q0 15 12 24q286 230 449 484l-6 8q-111 -66 -230 -66q-201 0 -327.5 128 t-126.5 337zM178 946q0 -163 92.5 -262.5t251.5 -99.5q180 0 263 96t83 266q0 162 -94 262.5t-252 100.5q-157 0 -250.5 -99.5t-93.5 -263.5z" />
<glyph unicode=":" horiz-adv-x="448" d="M174 66q0 36 23.5 57.5t62.5 21.5t62.5 -21.5t23.5 -57.5q0 -39 -23.5 -60.5t-62.5 -21.5t-62.5 21.5t-23.5 60.5zM174 758q0 36 23.5 58t62.5 22t62.5 -22t23.5 -58q0 -39 -23.5 -60.5t-62.5 -21.5t-62.5 21.5t-23.5 60.5z" />
<glyph unicode=";" horiz-adv-x="442" d="M70 -299q0 4 4 16q104 186 104 355q0 32 22.5 52.5t53.5 20.5t53.5 -20.5t22.5 -52.5q0 -141 -178 -402q-9 -12 -25 -12q-6 0 -14 4l-27 14q-16 10 -16 25zM168 758q0 36 23.5 58t62.5 22t62.5 -22t23.5 -58q0 -39 -23.5 -60.5t-62.5 -21.5t-62.5 21.5t-23.5 60.5z" />
<glyph unicode="&#x3c;" horiz-adv-x="1198" d="M115 461v47q0 7 6 16.5t12 12.5q717 341 883 419q4 2 12 2q17 0 27 -16l14 -29q4 -8 4 -14q0 -17 -16 -27q-571 -274 -826 -391q235 -100 813 -383l13 -6q16 -10 16 -26q0 -7 -4 -15l-14 -28q-10 -17 -27 -17q-8 0 -12 2l-883 424q-6 3 -12 12.5t-6 16.5z" />
<glyph unicode="=" horiz-adv-x="1196" d="M113 266v33q0 12 9 21.5t21 9.5h910q12 0 21 -9.5t9 -21.5v-33q0 -12 -9 -21t-21 -9h-910q-12 0 -21 9t-9 21zM113 672v33q0 12 9 21t21 9h910q12 0 21 -9t9 -21v-33q0 -12 -9 -21.5t-21 -9.5h-910q-12 0 -21 9.5t-9 21.5z" />
<glyph unicode="&#x3e;" horiz-adv-x="1198" d="M115 66q0 16 16 26l12 6q578 283 813 383q-234 107 -825 391q-16 10 -16 27q0 6 4 14l14 29q10 16 27 16q8 0 12 -2l883 -419q6 -3 12 -12.5t6 -16.5v-47q0 -7 -6 -16.5t-12 -12.5q-717 -346 -883 -424q-4 -2 -12 -2q-17 0 -27 17l-14 28q-4 8 -4 15z" />
<glyph unicode="?" horiz-adv-x="815" d="M96 1327q0 25 17 35q106 47 237 47q162 0 250 -79.5t88 -196.5q0 -48 -13.5 -93t-44 -93t-55.5 -81t-73 -90l-19 -20q-75 -88 -101 -165.5t-26 -207.5q0 -12 -9 -21.5t-21 -9.5h-25q-11 0 -20.5 8.5t-10.5 18.5v37q0 119 32.5 216t88.5 159q98 108 142.5 181.5 t44.5 153.5q0 96 -67 141.5t-183 45.5q-98 0 -178 -39q-8 -4 -15 -4q-11 0 -20.5 7t-10.5 17zM240 66q0 37 23 58t63 21q39 0 62.5 -21.5t23.5 -57.5q0 -39 -23.5 -60.5t-62.5 -21.5q-40 0 -63 21.5t-23 60.5z" />
<glyph unicode="@" horiz-adv-x="1595" d="M90 483q0 157 51 294.5t141.5 238t223 158t287.5 57.5q199 0 360 -88t252.5 -243.5t91.5 -346.5q0 -114 -29.5 -204.5t-78.5 -144t-107 -81t-119 -27.5q-75 0 -129.5 50.5t-58.5 138.5h-12q-45 -98 -115.5 -143.5t-153.5 -45.5q-102 0 -165.5 74.5t-63.5 208.5 q0 116 34.5 213t89.5 157t120.5 92.5t130.5 32.5q131 0 260 -90q12 -12 12 -26q-49 -272 -49 -377q0 -188 119 -188q22 0 44 5t55 26t57 56.5t41.5 104t17.5 160.5q0 258 -171 424t-433 166q-179 0 -317 -84t-212 -231.5t-74 -336.5q0 -187 73.5 -334t211.5 -231t318 -84 q110 0 208 31q4 2 11 2q23 0 30 -25l7 -30q0 -28 -21 -35q-99 -37 -237 -37q-210 0 -371 98.5t-245.5 266.5t-84.5 378zM565 391q0 -93 46 -145.5t114 -52.5q61 0 113.5 50.5t86 130t53.5 169.5t23 176q-32 23 -77.5 41t-87.5 18t-87.5 -25t-87.5 -71t-69 -122.5t-27 -168.5 z" />
<glyph unicode="A" horiz-adv-x="1267" d="M53 29q0 8 2 12l523 1331q3 8 12 14.5t16 6.5h56q7 0 16 -6.5t12 -14.5l522 -1331q2 -4 2 -12q0 -12 -8.5 -20.5t-21.5 -8.5h-56q-8 0 -16.5 6.5t-11.5 13.5l-142 361h-651q-10 -25 -33.5 -85.5t-35.5 -92.5l-70 -183q-3 -7 -12 -13.5t-17 -6.5h-55q-13 0 -22 8.5 t-9 20.5zM346 479h576q-238 625 -281 766h-12q-21 -65 -91.5 -256.5t-131.5 -350.5z" />
<glyph unicode="B" horiz-adv-x="1191" d="M197 27v1337q0 10 9.5 19.5t20.5 11.5q124 14 260 14q119 0 209.5 -22.5t142.5 -57t84.5 -83t43 -92.5t10.5 -93q0 -102 -52 -188.5t-149 -127.5v-8q58 -13 112 -42t99.5 -72.5t72.5 -107t27 -136.5q0 -63 -14.5 -117.5t-53 -107t-98 -89t-156.5 -59t-222 -22.5 q-202 0 -320 12q-10 1 -18 10.5t-8 20.5zM303 96q91 -12 266 -12q96 0 169 18.5t117 48t71 72t37 83.5t10 89q0 120 -101 201.5t-266 81.5h-303v-582zM303 776h262q125 0 212 78t87 188q0 40 -8 75t-31.5 71.5t-61 62t-101.5 42t-148 16.5q-211 0 -211 -11v-522z" />
<glyph unicode="C" horiz-adv-x="1261" d="M113 696q0 201 88 364t245.5 256t352.5 93q197 0 368 -84q19 -11 19 -29l-10 -45q-1 -12 -9.5 -19t-19.5 -7q-5 0 -17 4q-161 80 -331 80q-160 0 -291.5 -81t-206 -221.5t-74.5 -310.5t74.5 -310t206 -221t291.5 -81q163 0 329 78q12 4 17 4q19 0 26 -21l15 -39 q0 -1 1 -4t1 -4q0 -21 -17 -28q-180 -86 -372 -86q-195 0 -352.5 93t-245.5 255.5t-88 363.5z" />
<glyph unicode="D" horiz-adv-x="1435" d="M197 27v1337q0 11 8 20.5t18 10.5q107 14 312 14q162 0 295 -39t223.5 -105.5t152.5 -158t90.5 -194.5t28.5 -216t-28.5 -216t-90.5 -194t-152.5 -157.5t-223.5 -105.5t-295 -39q-221 0 -312 12q-10 1 -18 10.5t-8 20.5zM303 94q16 -4 51 -4q147 -6 199 -6q303 0 480 158 t177 454q0 295 -177 454t-480 159q-93 0 -250 -9v-1206z" />
<glyph unicode="E" horiz-adv-x="1026" d="M197 31v1331q0 12 9 21.5t21 9.5h654q12 0 21 -9.5t9 -21.5v-37q0 -12 -9 -21.5t-21 -9.5h-578v-520h514q12 0 21.5 -9.5t9.5 -21.5v-34q0 -12 -9.5 -21.5t-21.5 -9.5h-514v-580h588q12 0 21.5 -9t9.5 -21v-37q0 -12 -9.5 -21.5t-21.5 -9.5h-664q-12 0 -21 9.5t-9 21.5z " />
<glyph unicode="F" horiz-adv-x="970" d="M197 31v1331q0 12 9 21.5t21 9.5h633q12 0 21.5 -9.5t9.5 -21.5v-37q0 -12 -9.5 -21.5t-21.5 -9.5h-557v-520h502q12 0 21.5 -9.5t9.5 -21.5v-34q0 -12 -9.5 -21.5t-21.5 -9.5h-502v-647q0 -12 -9.5 -21.5t-21.5 -9.5h-45q-12 0 -21 9.5t-9 21.5z" />
<glyph unicode="G" horiz-adv-x="1445" d="M113 696q0 201 88 364t245.5 256t352.5 93q197 0 368 -84q19 -11 19 -29l-10 -45q-1 -12 -9.5 -19t-19.5 -7q-5 0 -17 4q-161 80 -331 80q-160 0 -291.5 -81t-206 -221.5t-74.5 -310.5t74.5 -310t206 -221t291.5 -81q52 0 153 10t191 27v497h-266q-12 0 -21.5 9.5 t-9.5 21.5v37q0 12 9.5 21.5t21.5 9.5h342q12 0 21 -9.5t9 -21.5v-620q0 -9 -6.5 -18.5t-15.5 -12.5q-79 -21 -213.5 -36t-214.5 -15q-195 0 -352.5 93t-245.5 255.5t-88 363.5z" />
<glyph unicode="H" horiz-adv-x="1415" d="M197 31v1331q0 12 9 21.5t21 9.5h45q12 0 21.5 -9.5t9.5 -21.5v-594h809v594q0 12 9.5 21.5t21.5 9.5h45q12 0 21.5 -9.5t9.5 -21.5v-1331q0 -12 -9.5 -21.5t-21.5 -9.5h-45q-12 0 -21.5 9.5t-9.5 21.5v639h-809v-639q0 -12 -9.5 -21.5t-21.5 -9.5h-45q-12 0 -21 9.5 t-9 21.5z" />
<glyph unicode="I" horiz-adv-x="858" d="M131 31v32q0 12 9.5 21.5t21.5 9.5h213v1204h-213q-12 0 -21.5 9.5t-9.5 21.5v33q0 12 9.5 21.5t21.5 9.5h532q12 0 21.5 -9.5t9.5 -21.5v-33q0 -12 -9.5 -21.5t-21.5 -9.5h-213v-1204h213q12 0 21.5 -9.5t9.5 -21.5v-32q0 -12 -9.5 -21.5t-21.5 -9.5h-532 q-12 0 -21.5 9.5t-9.5 21.5z" />
<glyph unicode="J" horiz-adv-x="862" d="M96 84q0 1 1 4t1 4l15 35q6 18 26 18q12 0 21 -6q77 -55 182 -55q96 0 156.5 63t60.5 162v989h-328q-12 0 -21 9.5t-9 21.5v33q0 12 9 21.5t21 9.5h404q12 0 21.5 -9.5t9.5 -21.5v-1051q0 -144 -90.5 -235.5t-231.5 -91.5q-129 0 -233 71q-15 11 -15 29z" />
<glyph unicode="K" horiz-adv-x="1173" d="M197 31v1331q0 12 9 21.5t21 9.5h45q12 0 21.5 -9.5t9.5 -21.5v-565h8l557 585q11 11 23 11h61q14 0 23.5 -8.5t9.5 -20.5q0 -13 -10 -23l-551 -565l682 -725q10 -10 10 -22t-9.5 -20.5t-23.5 -8.5h-63q-13 0 -23 10l-686 744h-8v-723q0 -12 -9.5 -21.5t-21.5 -9.5h-45 q-12 0 -21 9.5t-9 21.5z" />
<glyph unicode="L" horiz-adv-x="985" d="M197 31v1331q0 12 9 21.5t21 9.5h45q12 0 21.5 -9.5t9.5 -21.5v-1264h600q12 0 21.5 -9t9.5 -21v-37q0 -12 -9.5 -21.5t-21.5 -9.5h-676q-12 0 -21 9.5t-9 21.5z" />
<glyph unicode="M" horiz-adv-x="1695" d="M180 31l47 1333q0 12 9.5 20.5t21.5 8.5h98q6 0 15 -5t12 -10l467 -704l467 704q3 5 11.5 10t14.5 5h95q12 0 21 -8.5t9 -20.5l48 -1333q0 -13 -9 -22t-22 -9h-49q-10 0 -20 10t-11 23l-33 1237h-8l-487 -754q-10 -16 -27 -16t-27 16l-489 754h-8l-33 -1239 q0 -12 -9.5 -21.5t-21.5 -9.5h-51q-13 0 -22 9t-9 22z" />
<glyph unicode="N" horiz-adv-x="1478" d="M197 31v1331q0 12 9 21.5t21 9.5h103q6 0 14.5 -5t11.5 -10l820 -1259h8q-8 188 -8 233v1010q0 12 9 21.5t21 9.5h45q12 0 21.5 -9.5t9.5 -21.5v-1331q0 -12 -9.5 -21.5t-21.5 -9.5h-102q-6 0 -15 4.5t-12 9.5l-819 1256h-8q8 -188 8 -232v-1007q0 -12 -9.5 -21.5 t-21.5 -9.5h-45q-12 0 -21 9.5t-9 21.5z" />
<glyph unicode="O" horiz-adv-x="1554" d="M113 694q0 150 47.5 281.5t133 227.5t210.5 151t272 55q199 0 352 -94.5t234.5 -257t81.5 -363.5q0 -203 -80.5 -363.5t-233.5 -253.5t-354 -93q-200 0 -352 93t-231.5 253.5t-79.5 363.5zM227 694q0 -270 151.5 -440t397.5 -170q249 0 401 170t152 440q0 179 -68 318.5 t-194.5 218t-290.5 78.5q-246 0 -397.5 -171.5t-151.5 -443.5z" />
<glyph unicode="P" horiz-adv-x="1079" d="M197 31v1335q0 11 8 20.5t18 10.5q110 12 266 12q119 0 211.5 -26.5t149 -68.5t92.5 -101.5t50 -119t14 -126.5q0 -103 -36.5 -184.5t-100 -131.5t-143.5 -76t-171 -26h-252v-518q0 -12 -9.5 -21.5t-21.5 -9.5h-45q-12 0 -21 9.5t-9 21.5zM303 647h264q147 0 235.5 88.5 t88.5 235.5q0 66 -19 123t-60 107t-117.5 79t-180.5 29q-211 0 -211 -11v-651z" />
<glyph unicode="Q" horiz-adv-x="1554" d="M113 694q0 202 80 363.5t232.5 256.5t350.5 95q199 0 352 -94.5t234.5 -257t81.5 -363.5q0 -184 -69.5 -336.5t-200.5 -250t-302 -117.5l-16 -2q32 -67 139 -111.5t273 -48.5q13 0 21.5 -9.5t8.5 -21.5q0 -8 -2 -12l-8 -31q-1 -10 -11.5 -18t-21.5 -8q-201 0 -344 76 t-182 182q-187 9 -328.5 106t-214.5 254t-73 348zM227 694q0 -271 151 -439.5t398 -168.5t400 168.5t153 439.5t-153.5 443t-399.5 172t-397.5 -171.5t-151.5 -443.5z" />
<glyph unicode="R" horiz-adv-x="1153" d="M197 31v1333q0 11 8 20.5t18 10.5q124 14 266 14q118 0 208 -30t141 -73.5t83 -103t42 -109t10 -100.5q0 -66 -26.5 -126.5t-69.5 -104.5t-93 -75.5t-102 -47.5v-10q137 -71 305 -371q62 -112 88 -156l33 -57q4 -12 4 -16q0 -12 -9 -20.5t-22 -8.5h-32q-46 0 -66 41 q-102 192 -106 199q-86 163 -184.5 265.5t-194.5 102.5h-195v-577q0 -12 -9.5 -21.5t-21.5 -9.5h-45q-12 0 -21 9.5t-9 21.5zM303 707h201q164 0 259 81t95 211q0 40 -9.5 80.5t-34.5 82.5t-62.5 74t-99 52.5t-138.5 20.5q-211 0 -211 -11v-591z" />
<glyph unicode="S" horiz-adv-x="962" d="M96 109q0 1 1 4t1 4l17 39q6 22 26 22q10 0 19 -6q137 -88 291 -88q134 0 210 71t76 187q0 33 -9.5 64.5t-22.5 56.5t-38.5 52t-46 45t-56.5 43t-58.5 39t-63.5 38q-79 49 -128 84t-100 84.5t-75 105t-24 121.5q0 157 109.5 245.5t273.5 88.5q139 0 258 -61q16 -6 16 -27 q0 -1 -1 -4t-1 -4l-14 -45q-7 -23 -27 -23q-4 0 -16 4q-114 60 -224 60q-114 0 -187 -61.5t-73 -168.5q0 -38 16 -74t38 -64t62 -60t72.5 -53.5t84.5 -53.5q48 -29 72 -44.5t68 -46t68 -52.5t55 -56.5t47 -66.5t28 -74t12 -86q0 -165 -114.5 -264.5t-272.5 -99.5 q-206 0 -354 96q-15 8 -15 29z" />
<glyph unicode="T" horiz-adv-x="1077" d="M39 1325v37q0 12 9.5 21.5t21.5 9.5h938q12 0 21 -9.5t9 -21.5v-37q0 -12 -9 -21.5t-21 -9.5h-414v-1263q0 -12 -9.5 -21.5t-21.5 -9.5h-45q-12 0 -21.5 9.5t-9.5 21.5v1263h-417q-12 0 -21.5 9.5t-9.5 21.5z" />
<glyph unicode="U" horiz-adv-x="1454" d="M197 436v926q0 12 9 21.5t21 9.5h45q12 0 21.5 -9.5t9.5 -21.5v-928q0 -143 125 -246.5t301 -103.5q174 0 298 103.5t124 246.5v928q0 12 9.5 21.5t21.5 9.5h45q12 0 21 -9.5t9 -21.5v-926q0 -187 -155 -319.5t-373 -132.5q-144 0 -266.5 60.5t-194 164.5t-71.5 227z" />
<glyph unicode="V" horiz-adv-x="1265" d="M61 1364q0 12 9 20.5t22 8.5h58q7 0 16 -6.5t12 -14.5q402 -1056 447 -1206h14q21 67 132.5 368.5t212.5 569.5l101 268q3 8 12.5 14.5t16.5 6.5h57q13 0 22 -8.5t9 -20.5q0 -8 -2 -12l-508 -1332q-3 -7 -11.5 -13.5t-16.5 -6.5h-64q-8 0 -17 6.5t-12 13.5l-508 1332 q-2 4 -2 12z" />
<glyph unicode="W" horiz-adv-x="1982" d="M53 1364q0 12 9.5 20.5t21.5 8.5h55q9 0 19.5 -8t11.5 -17q1 -4 146.5 -557t166.5 -645h15q47 176 436 1206q3 8 12.5 14.5t16.5 6.5h49q7 0 16 -6.5t12 -14.5q393 -1039 437 -1206h14q21 92 166.5 645t146.5 557q1 9 11.5 17t19.5 8h55q12 0 21.5 -8.5t9.5 -20.5 q0 -2 -1 -5t-1 -5l-363 -1331q-8 -23 -28 -23h-72q-8 0 -17 6.5t-12 13.5q-381 1020 -434 1215h-12q-53 -195 -434 -1215q-3 -7 -12 -13.5t-17 -6.5h-72q-20 0 -28 23l-363 1331q0 2 -1 5t-1 5z" />
<glyph unicode="X" horiz-adv-x="1300" d="M45 29q0 11 6 20l533 686q-76 98 -236.5 304t-236.5 304q-7 11 -7 21q0 12 9 20.5t22 8.5h62q15 0 24 -13l422 -551h10l422 551q3 4 11.5 8.5t13.5 4.5h59q13 0 22 -8.5t9 -20.5t-6 -21l-471 -608q194 -253 348 -452.5t180 -233.5q6 -9 6 -20q0 -12 -8.5 -20.5 t-21.5 -8.5h-58q-16 0 -24 12l-482 625h-10l-481 -625q-8 -12 -25 -12h-61q-13 0 -22 8.5t-9 20.5z" />
<glyph unicode="Y" horiz-adv-x="1122" d="M70 1364q0 29 30 29h54q6 0 14.5 -5t11.5 -10q314 -508 373 -639h12q59 131 373 639q3 5 12 10t15 5h53q14 0 22.5 -8.5t8.5 -20.5q0 -10 -5 -18l-432 -721v-594q0 -12 -9 -21.5t-21 -9.5h-45q-12 0 -21.5 9.5t-9.5 21.5v598l-432 717q-4 6 -4 18z" />
<glyph unicode="Z" horiz-adv-x="1224" d="M111 31v26q0 27 18 54l791 1175v8h-762q-12 0 -21.5 9.5t-9.5 21.5v37q0 12 9.5 21.5t21.5 9.5h874q12 0 21.5 -9.5t9.5 -21.5v-21q0 -33 -16 -49l-736 -1087l-69 -94v-13h839q12 0 21.5 -9t9.5 -21v-37q0 -12 -9.5 -21.5t-21.5 -9.5h-940q-12 0 -21 9.5t-9 21.5z" />
<glyph unicode="[" horiz-adv-x="522" d="M166 -391v1753q0 12 9.5 21.5t21.5 9.5h235q12 0 21.5 -9.5t9.5 -21.5v-31q0 -12 -9.5 -21.5t-21.5 -9.5h-162v-1632h162q12 0 21.5 -9t9.5 -21v-29q0 -12 -9.5 -21.5t-21.5 -9.5h-235q-12 0 -21.5 9.5t-9.5 21.5z" />
<glyph unicode="\" horiz-adv-x="739" d="M66 1364q0 12 8.5 20.5t21.5 8.5h41q9 0 17.5 -6.5t11.5 -14.5l506 -1481q2 -4 2 -12q0 -12 -9 -20.5t-22 -8.5h-41q-9 0 -17.5 6.5t-11.5 14.5l-505 1481q-2 4 -2 12z" />
<glyph unicode="]" horiz-adv-x="526" d="M59 -362q0 12 9.5 21t21.5 9h164v1632h-164q-12 0 -21.5 9.5t-9.5 21.5v31q0 12 9.5 21.5t21.5 9.5h236q12 0 21 -9.5t9 -21.5v-1753q0 -12 -9 -21.5t-21 -9.5h-236q-12 0 -21.5 9.5t-9.5 21.5v29z" />
<glyph unicode="^" horiz-adv-x="1155" d="M98 508q0 6 4 14q46 100 412 869q3 6 12.5 12t16.5 6h69q7 0 16.5 -6t12.5 -12q25 -55 412 -869q4 -8 4 -14q0 -12 -9.5 -20.5t-23.5 -8.5h-39q-7 0 -16.5 6.5t-12.5 12.5l-370 817h-13l-376 -817q-3 -6 -12.5 -12.5t-16.5 -6.5h-37q-14 0 -23.5 8.5t-9.5 20.5z" />
<glyph unicode="_" horiz-adv-x="989" d="M8 -207q0 12 9.5 21.5t21.5 9.5h911q12 0 21.5 -9.5t9.5 -21.5v-31q0 -12 -9.5 -21t-21.5 -9h-911q-12 0 -21.5 9t-9.5 21v31z" />
<glyph unicode="`" horiz-adv-x="690" d="M209 1372q0 9 8 16t21 7h51q30 0 47 -25q120 -161 143 -190q6 -9 6 -19q0 -18 -22 -18h-21q-32 0 -51 22l-172 185q-10 13 -10 22z" />
<glyph unicode="a" horiz-adv-x="1056" d="M92 485q0 220 121.5 361t310.5 141q76 0 143 -18t148 -72l8 47q1 10 10.5 18.5t20.5 8.5h16q12 0 21.5 -9.5t9.5 -21.5v-573q0 -143 31 -332q0 -35 -31 -35h-31q-11 0 -20 7.5t-10 17.5l-23 94h-14q-114 -135 -293 -135q-188 0 -303 140t-115 361zM201 485 q0 -182 84 -292.5t229 -110.5q110 0 186 62t99 151v498q-62 47 -135 71.5t-140 24.5q-145 0 -234 -112t-89 -292z" />
<glyph unicode="b" horiz-adv-x="1058" d="M158 55v1307q0 12 9 21.5t21 9.5h41q12 0 21.5 -9.5t9.5 -21.5v-494h8q55 62 128 90.5t139 28.5q189 0 310.5 -141t121.5 -361t-121.5 -360.5t-310.5 -140.5q-204 0 -355 43q-22 7 -22 28zM260 111q145 -29 275 -29q145 0 234 111t89 292q0 180 -89 292t-234 112 q-169 0 -275 -137v-641z" />
<glyph unicode="c" horiz-adv-x="888" d="M92 485q0 221 122.5 361.5t320.5 140.5q137 0 256 -53q20 -7 20 -29q0 -6 -2 -10q-3 -7 -8 -33q-6 -24 -31 -24q-8 0 -12 2q-63 27 -111 38t-112 11q-149 0 -241.5 -110t-92.5 -294q0 -185 92 -294t242 -109q65 0 111 11t112 38q4 2 12 2q25 0 31 -24l12 -37v-4 q0 -20 -18 -31q-116 -53 -260 -53q-198 0 -320.5 140t-122.5 361z" />
<glyph unicode="d" horiz-adv-x="1056" d="M92 485q0 220 121.5 361t310.5 141q143 0 275 -80v455q0 12 9 21.5t21 9.5h41q12 0 21.5 -9.5t9.5 -21.5v-995q0 -143 31 -332q0 -16 -9 -25.5t-22 -9.5h-31q-11 0 -20 7.5t-10 17.5l-23 94h-14q-119 -135 -293 -135q-188 0 -303 140t-115 361zM201 485q0 -182 84 -292.5 t229 -110.5q109 0 184.5 61.5t100.5 151.5v498q-121 96 -275 96q-145 0 -234 -112t-89 -292z" />
<glyph unicode="e" horiz-adv-x="993" d="M92 485q0 221 121.5 361.5t318.5 140.5q167 0 269 -110.5t102 -296.5q0 -59 -2 -88q-2 -12 -10.5 -20.5t-20.5 -8.5h-669q6 -177 98 -279t236 -102q136 0 274 59q8 4 12 4q23 0 29 -22l12 -37q0 -1 1 -3.5t1 -4.5q0 -25 -18 -31q-60 -26 -145 -44.5t-166 -18.5 q-197 0 -320 140t-123 361zM205 555h592v31q0 142 -72 222.5t-190 80.5q-136 0 -224.5 -89t-105.5 -245z" />
<glyph unicode="f" horiz-adv-x="591" d="M70 909v31q0 12 9 21.5t21 9.5h70v180q0 126 68 192t190 66q108 0 215 -35q23 -8 23 -35q-5 -25 -13 -41q-6 -18 -30 -18q-2 0 -5 1t-4 1q-102 31 -170 31q-87 0 -129.5 -43t-42.5 -119v-180h197q12 0 21.5 -9.5t9.5 -21.5v-31q0 -12 -9.5 -21t-21.5 -9h-197v-848 q0 -12 -9 -21.5t-21 -9.5h-41q-12 0 -21.5 9.5t-9.5 21.5v848h-70q-12 0 -21 9t-9 21z" />
<glyph unicode="g" horiz-adv-x="1058" d="M92 485q0 220 121.5 361t310.5 141q71 0 136 -17t149 -73l8 47q1 10 10.5 18.5t20.5 8.5h22q12 0 21.5 -9.5t9.5 -21.5v-1067q0 -149 -105 -230t-272 -81q-74 0 -166 15.5t-159 39.5q-21 7 -21 29q0 6 2 10l13 39q7 20 28 20q6 0 10 -2q150 -53 281 -53q146 0 216.5 56.5 t70.5 152.5v246h-15q-54 -51 -125.5 -76t-134.5 -25q-193 0 -312.5 127t-119.5 344zM201 485q0 -177 87.5 -274.5t235.5 -97.5q159 0 275 129v551q-61 48 -134 72t-141 24q-145 0 -234 -112t-89 -292z" />
<glyph unicode="h" d="M158 31v1331q0 12 9 21.5t21 9.5h41q12 0 21.5 -9.5t9.5 -21.5v-494q122 119 272 119q173 0 256.5 -90.5t83.5 -241.5v-624q0 -12 -9 -21.5t-21 -9.5h-41q-12 0 -21.5 9.5t-9.5 21.5v610q0 120 -54.5 184t-195.5 64q-84 0 -151 -40t-109 -95v-723q0 -12 -9.5 -21.5 t-21.5 -9.5h-41q-12 0 -21 9.5t-9 21.5z" />
<glyph unicode="i" horiz-adv-x="413" d="M129 1221q0 34 21 52.5t57 18.5t57 -18.5t21 -52.5t-21.5 -54t-56.5 -20t-56.5 20t-21.5 54zM156 31v909q0 12 9 21.5t21 9.5h41q12 0 21.5 -9.5t9.5 -21.5v-909q0 -12 -9.5 -21.5t-21.5 -9.5h-41q-12 0 -21 9.5t-9 21.5z" />
<glyph unicode="j" horiz-adv-x="423" d="M-125 -379q0 15 8 25t19 12q131 21 196.5 87t65.5 159v1036q0 12 9.5 21.5t21.5 9.5h41q12 0 21 -9.5t9 -21.5v-1026q0 -143 -94 -233t-254 -115q-28 0 -35 24zM137 1221q0 34 21 52.5t57 18.5t57 -18.5t21 -52.5t-21.5 -54t-56.5 -20t-56.5 20t-21.5 54z" />
<glyph unicode="k" horiz-adv-x="894" d="M158 31v1331q0 12 9 21.5t21 9.5h41q12 0 21.5 -9.5t9.5 -21.5v-746h12q24 25 93 85.5t173 150t119 102.5q23 17 41 17h39q13 0 22 -9.5t9 -21.5q0 -14 -10 -22l-379 -334q348 -416 448 -533q9 -9 9 -22q0 -12 -9 -20.5t-22 -8.5h-60q-12 0 -24 12q-388 459 -449 555h-12 v-536q0 -12 -9.5 -21.5t-21.5 -9.5h-41q-12 0 -21 9.5t-9 21.5z" />
<glyph unicode="l" horiz-adv-x="563" d="M158 231v1131q0 12 9 21.5t21 9.5h41q12 0 21.5 -9.5t9.5 -21.5v-1114q0 -166 152 -166q39 0 88 12h8q23 0 29 -22l12 -45q0 -24 -25 -31q-54 -12 -125 -12q-116 0 -178.5 61t-62.5 186z" />
<glyph unicode="m" horiz-adv-x="1642" d="M150 940q0 12 9 21.5t21 9.5h33q12 0 20.5 -8.5t10.5 -20.5l8 -72h10q56 57 129.5 87t143.5 30q101 0 176.5 -35t115.5 -100h17q135 135 303 135q169 0 253.5 -88.5t84.5 -249.5v-618q0 -12 -9.5 -21.5t-21.5 -9.5h-41q-12 0 -21.5 9.5t-9.5 21.5v602q0 61 -11 105 t-38 79.5t-77 53.5t-123 18q-154 0 -261 -137v-721q0 -12 -9 -21.5t-21 -9.5h-41q-12 0 -21.5 9.5t-9.5 21.5v602q0 61 -11 105t-37.5 79.5t-77 53.5t-124.5 18q-146 0 -260 -137v-721q0 -12 -9.5 -21.5t-21.5 -9.5h-41q-12 0 -21 9.5t-9 21.5v760z" />
<glyph unicode="n" d="M158 31v909q0 12 9 21.5t21 9.5h25q12 0 20.5 -8.5t10.5 -20.5l8 -72h12q57 58 129.5 87.5t141.5 29.5q173 0 256 -90t83 -242v-624q0 -12 -9 -21.5t-21 -9.5h-41q-12 0 -21.5 9.5t-9.5 21.5v608q0 120 -55.5 185t-196.5 65q-146 0 -260 -137v-721q0 -12 -9.5 -21.5 t-21.5 -9.5h-41q-12 0 -21 9.5t-9 21.5z" />
<glyph unicode="o" horiz-adv-x="1069" d="M92 485q0 221 123 361.5t320 140.5q198 0 321 -140.5t123 -361.5t-123 -361t-321 -140q-197 0 -320 140t-123 361zM201 485q0 -185 92 -294t242 -109t242.5 109t92.5 294q0 184 -93 294t-242 110t-241.5 -110t-92.5 -294z" />
<glyph unicode="p" horiz-adv-x="1058" d="M158 -391v1331q0 12 9 21.5t21 9.5h27q12 0 20.5 -8.5t10.5 -20.5l8 -65h14q61 57 132 83.5t135 26.5q189 0 310.5 -141t121.5 -361t-121.5 -360.5t-310.5 -140.5q-44 0 -76 3.5t-86.5 23t-112.5 55.5v-457q0 -12 -9.5 -21.5t-21.5 -9.5h-41q-12 0 -21 9.5t-9 21.5z M260 180q131 -98 275 -98q145 0 234 111t89 292q0 180 -89 292t-234 112q-169 0 -275 -137v-572z" />
<glyph unicode="q" horiz-adv-x="1050" d="M92 485q0 220 121.5 361t310.5 141q195 0 357 -43q9 -3 15.5 -12.5t6.5 -18.5v-1245h180q12 0 21.5 -9t9.5 -21v-29q0 -12 -9.5 -21.5t-21.5 -9.5h-252q-12 0 -21 9.5t-9 21.5v491h-8q-50 -59 -127 -87.5t-142 -28.5q-190 0 -311 140.5t-121 360.5zM201 485 q0 -180 89 -291.5t234 -111.5q164 0 277 135v643q-122 29 -277 29q-145 0 -234 -112t-89 -292z" />
<glyph unicode="r" horiz-adv-x="745" d="M158 31v909q0 12 9 21.5t21 9.5h23q12 0 20.5 -8.5t10.5 -20.5l8 -72h14q53 56 126.5 86.5t141.5 30.5q50 0 170 -35q21 -7 21 -28q0 -3 -6.5 -27t-7.5 -25q-6 -20 -21 -20q-6 0 -18 4q-101 33 -150 33q-154 0 -260 -137v-721q0 -12 -9.5 -21.5t-21.5 -9.5h-41 q-12 0 -21 9.5t-9 21.5z" />
<glyph unicode="s" horiz-adv-x="759" d="M57 82q0 6 4 14l23 39q10 17 25 17q1 0 16 -5q58 -33 107.5 -49t115.5 -16q94 0 153.5 43t59.5 133q0 32 -16 59.5t-51.5 53t-62.5 40.5t-79 40q-47 23 -77 40t-69.5 44.5t-62 54t-39 63.5t-16.5 78q0 119 81.5 187.5t195.5 68.5q110 0 243 -65q19 -10 19 -27 q0 -6 -4 -14l-19 -37q-10 -17 -26 -17q-7 0 -15 4q-113 58 -190 58q-73 0 -124.5 -41.5t-51.5 -108.5q0 -27 13 -51t29 -41t51 -38.5t57 -33t69 -34.5q62 -32 100.5 -55.5t77.5 -57.5t57.5 -74.5t18.5 -89.5q0 -137 -86.5 -208.5t-218.5 -71.5q-149 0 -291 71q-17 10 -17 27 z" />
<glyph unicode="t" horiz-adv-x="698" d="M70 909v31q0 12 9 21.5t21 9.5h70v119q0 20 20 28l41 17q4 2 11 2q12 0 21 -9t9 -22v-135h248q12 0 21.5 -9.5t9.5 -21.5v-31q0 -12 -9.5 -21t-21.5 -9h-248v-631q0 -76 44 -121t130 -45q74 0 166 29q4 2 11 2q20 0 28 -23l13 -35v-4q0 -9 -6 -18.5t-15 -12.5 q-101 -36 -217 -36q-256 0 -256 249v646h-70q-12 0 -21 9t-9 21z" />
<glyph unicode="u" d="M158 313v627q0 12 9 21.5t21 9.5h41q12 0 21.5 -9.5t9.5 -21.5v-610q0 -120 55.5 -184t196.5 -64q145 0 260 137v721q0 12 9.5 21.5t21.5 9.5h41q12 0 21 -9.5t9 -21.5v-909q0 -12 -9 -21.5t-21 -9.5h-25q-12 0 -20.5 8.5t-10.5 20.5l-8 69h-12q-57 -55 -128.5 -84.5 t-141.5 -29.5q-173 0 -256.5 89.5t-83.5 239.5z" />
<glyph unicode="v" horiz-adv-x="909" d="M57 942q0 12 9 20.5t22 8.5h47q7 0 16.5 -6.5t12.5 -14.5l285 -794h12l282 794q3 8 11.5 14.5t17.5 6.5h49q13 0 22 -8.5t9 -20.5q0 -8 -2 -12q-272 -726 -340 -910q-3 -7 -12 -13.5t-17 -6.5h-55q-8 0 -17 6.5t-12 13.5q-287 773 -338 910q-2 4 -2 12z" />
<glyph unicode="w" horiz-adv-x="1388" d="M51 940q0 12 10 21.5t23 9.5h45q21 0 29 -23q136 -510 200 -798h13q22 84 103.5 329.5t162.5 470.5q3 8 12.5 14.5t16.5 6.5h53q9 0 17.5 -6.5t11.5 -14.5q244 -691 268 -800h10q54 242 203 798q8 23 28 23h46q12 0 21 -8.5t9 -20.5q0 -2 -1 -5t-1 -5 q-203 -749 -244 -909q-3 -9 -12 -16t-18 -7h-60q-28 0 -34 25q-19 56 -95 278t-121 357t-49 157h-12q-11 -42 -52 -167t-78.5 -235.5t-85 -251t-48.5 -143.5q-8 -20 -29 -20h-65q-9 0 -18.5 7t-12.5 16l-244 909q0 1 -1 4t-1 4z" />
<glyph unicode="x" horiz-adv-x="964" d="M45 29q0 11 6 20l371 479l-301 394q-6 9 -6 20q0 12 8.5 20.5t21.5 8.5h52q15 0 24 -13l256 -335h10l258 335q3 4 11.5 8.5t13.5 4.5h49q13 0 22 -8.5t9 -20.5q0 -11 -6 -20l-301 -394l370 -479q7 -11 7 -20q0 -12 -9 -20.5t-22 -8.5h-51q-13 0 -25 12l-326 420h-10 l-129 -166q-119 -151 -196 -254q-8 -12 -25 -12h-51q-13 0 -22 8.5t-9 20.5z" />
<glyph unicode="y" horiz-adv-x="927" d="M51 -375q0 28 21 35q118 38 191 101.5t114 164.5q5 15 18 44t19 48l-338 912q-2 4 -2 12q0 12 8.5 20.5t21.5 8.5h48q7 0 16 -6.5t12 -14.5q230 -591 287 -794h10q40 149 285 794q3 8 12.5 14.5t16.5 6.5h45q13 0 21.5 -8.5t8.5 -20.5q0 -8 -2 -12l-393 -1045 q-83 -226 -369 -315q-4 -2 -10 -2q-24 0 -31 24z" />
<glyph unicode="z" horiz-adv-x="897" d="M86 35q0 9 6 18l529 826h-486q-12 0 -21.5 9t-9.5 21v31q0 12 9.5 21.5t21.5 9.5h610q13 0 23 -10.5t10 -24.5q0 -9 -6 -18l-534 -826h536q12 0 21.5 -9.5t9.5 -21.5v-30q0 -12 -9.5 -21.5t-21.5 -9.5h-655q-13 0 -23 10.5t-10 24.5z" />
<glyph unicode="{" horiz-adv-x="604" d="M63 473v33q0 9 7 19.5t16 11.5q94 23 142 89t48 148q0 16 -8 90l-6 60q-12 122 -12 149q0 142 67.5 220t180.5 100q2 0 6 1t6 1q16 0 22 -21l7 -26q0 -22 -23 -29q-88 -21 -127 -81t-39 -167q0 -36 8 -82l7 -53q14 -96 14 -139q0 -229 -205 -301v-9q205 -75 205 -303 q0 -43 -14 -139l-7 -51q-8 -62 -8 -86q0 -105 40 -164t124 -84q23 -8 23 -29v-8q0 -8 -7 -22q-11 -15 -28 -15q-114 20 -183 100.5t-69 227.5q0 25 12 143l6 56q8 74 8 94q0 82 -49.5 149.5t-140.5 85.5q-10 1 -16.5 11t-6.5 20z" />
<glyph unicode="|" horiz-adv-x="485" d="M199 -391v1753q0 12 9 21.5t21 9.5h29q12 0 21.5 -9.5t9.5 -21.5v-1753q0 -12 -9.5 -21.5t-21.5 -9.5h-29q-12 0 -21 9.5t-9 21.5z" />
<glyph unicode="}" horiz-adv-x="606" d="M66 1348l6 26q6 21 22 21q2 0 6 -1t6 -1q113 -22 180.5 -100t67.5 -220q0 -27 -12 -149l-6 -60q-8 -74 -8 -90q0 -82 48 -148t142 -89q9 -1 16 -11.5t7 -19.5v-33q0 -10 -6.5 -20t-16.5 -11q-91 -18 -140.5 -85.5t-49.5 -149.5q0 -20 8 -94l6 -56q12 -118 12 -143 q0 -147 -69 -227.5t-183 -100.5q-17 0 -28 15q-6 12 -6 22v8q0 9 6.5 17.5t15.5 11.5q84 25 124 84t40 164q0 24 -8 86l-6 51q-15 103 -15 139q0 228 205 303v9q-205 72 -205 301q0 36 15 139l6 53q8 46 8 82q0 107 -39 167t-127 81q-22 6 -22 29z" />
<glyph unicode="~" horiz-adv-x="1204" d="M68 551q0 103 67.5 177.5t165.5 74.5q57 0 117.5 -19t191.5 -73q107 -45 175 -66.5t128 -21.5t92.5 39.5t38.5 109.5q2 12 10.5 20.5t20.5 8.5h27q13 0 22 -9.5t9 -23.5q-7 -129 -69.5 -187.5t-158.5 -58.5q-99 0 -346 103q-185 77 -262 77q-61 0 -97 -40.5t-42 -110.5 q-2 -11 -11 -20t-20 -9h-29q-12 0 -21 8.5t-9 20.5z" />
<glyph horiz-adv-x="0" />
<glyph unicode="&#xa1;" horiz-adv-x="542" d="M174 905q0 39 23.5 60.5t62.5 21.5t62.5 -21.5t23.5 -60.5q0 -36 -23.5 -58t-62.5 -22t-62.5 22t-23.5 58zM207 -391l20 979q0 30 33 30q30 0 33 -30l20 -979q0 -12 -9 -21.5t-21 -9.5h-45q-12 0 -21.5 9.5t-9.5 21.5z" />
<glyph unicode="&#xa2;" horiz-adv-x="886" d="M90 696q0 130 45 236t131.5 175t202.5 85v170q0 12 9.5 21.5t21.5 9.5h45q12 0 21 -9.5t9 -21.5v-168q130 -12 216 -51q18 -6 18 -29q0 -8 -2 -12q-3 -5 -8 -27q-7 -26 -31 -26q-8 0 -12 2q-62 26 -111 37.5t-113 11.5q-149 0 -241 -110t-92 -294q0 -186 92 -295.5 t241 -109.5q64 0 111.5 11.5t112.5 39.5q4 2 12 2q24 0 31 -27l10 -34q0 -1 1 -4.5t1 -4.5q0 -19 -18 -26q-94 -43 -218 -53v-164q0 -12 -9 -21.5t-21 -9.5h-45q-12 0 -21.5 9.5t-9.5 21.5v168q-176 24 -277.5 160.5t-101.5 336.5z" />
<glyph unicode="&#xa3;" horiz-adv-x="1089" d="M100 31v37q0 12 9.5 21t21.5 9h76l94 545h-170q-12 0 -21.5 9.5t-9.5 21.5v31q0 12 9.5 21t21.5 9h184l58 342q28 162 149.5 247t286.5 85q78 0 160 -29q20 -8 20 -28l-10 -41q-3 -11 -12 -19t-19 -8q-6 0 -10 2q-74 23 -147 23q-114 0 -204 -61.5t-108 -170.5l-55 -342 h391q12 0 21.5 -9t9.5 -21v-31q0 -12 -9.5 -21.5t-21.5 -9.5h-407l-95 -545h623q12 0 21.5 -9t9.5 -21v-37q0 -12 -9.5 -21.5t-21.5 -9.5h-805q-12 0 -21.5 9.5t-9.5 21.5z" />
<glyph unicode="&#xa4;" horiz-adv-x="1196" d="M125 170q0 13 10 23l103 104q-105 127 -105 293q0 168 105 295l-103 102q-10 10 -10 23q0 12 10 22l21 21q8 8 22 8q15 0 23 -8l102 -103q127 105 295 105q166 0 293 -105l104 103q8 8 23 8q14 0 22 -8l21 -21q8 -8 8 -22q0 -15 -8 -23l-105 -102q105 -127 105 -295 q0 -166 -105 -293l105 -104q8 -8 8 -23t-8 -23l-21 -20q-10 -10 -22 -10q-13 0 -23 10l-104 104q-126 -104 -293 -104q-169 0 -295 104l-102 -104q-10 -10 -23 -10q-12 0 -22 10l-21 20q-10 10 -10 23zM225 590q0 -154 109.5 -262.5t263.5 -108.5t262.5 108.5t108.5 262.5 q0 155 -108.5 264t-262.5 109t-263.5 -109t-109.5 -264z" />
<glyph unicode="&#xa5;" horiz-adv-x="1122" d="M70 1364q0 29 30 29h54q6 0 14.5 -6t11.5 -11q318 -513 373 -637h12q55 124 373 637q3 5 12 11t15 6h53q14 0 22.5 -8.5t8.5 -20.5q0 -10 -5 -18l-278 -463h162q12 0 21 -9.5t9 -21.5v-31q0 -12 -9 -21t-21 -9h-219q-13 -23 -46.5 -79t-50.5 -87v-37h316q12 0 21 -9.5 t9 -21.5v-31q0 -12 -9 -21t-21 -9h-316v-465q0 -12 -9 -21.5t-21 -9.5h-45q-12 0 -21.5 9.5t-9.5 21.5v465h-313q-12 0 -21.5 9t-9.5 21v31q0 12 9.5 21.5t21.5 9.5h313v41q-56 91 -98 162h-215q-12 0 -21.5 9t-9.5 21v31q0 12 9.5 21.5t21.5 9.5h161l-280 463q-4 6 -4 18z " />
<glyph unicode="&#xa6;" horiz-adv-x="493" d="M203 299q0 12 9 21.5t21 9.5h29q12 0 21.5 -9.5t9.5 -21.5v-690q0 -12 -9.5 -21.5t-21.5 -9.5h-29q-12 0 -21 9.5t-9 21.5v690zM203 670v692q0 12 9 21.5t21 9.5h29q12 0 21.5 -9.5t9.5 -21.5v-692q0 -12 -9.5 -21.5t-21.5 -9.5h-29q-12 0 -21 9.5t-9 21.5z" />
<glyph unicode="&#xa7;" horiz-adv-x="917" d="M115 670q0 72 33.5 142.5t91.5 125.5q-64 92 -64 178q0 127 95.5 210t226.5 83q86 0 225 -49q7 -3 13.5 -11.5t6.5 -15.5l-14 -57q-3 -14 -20 -14q-7 0 -19 4q-97 45 -192 45q-84 0 -148.5 -52t-64.5 -143q0 -34 14.5 -65t32.5 -52.5t55.5 -48t62 -40.5t72.5 -40 q41 -22 59.5 -32.5t55.5 -33t55 -38.5t44 -42t38 -50t21.5 -56t9.5 -67q0 -151 -117 -268v-9q76 -83 76 -180q0 -146 -97 -236.5t-251 -90.5q-135 0 -254 67q-17 13 -17 29l11 41q3 24 28 24q5 0 17 -4q111 -59 215 -59q109 0 173 61t64 168q0 29 -8.5 55t-28 49t-37 39.5 t-51.5 37t-54.5 31.5t-61.5 32q-38 19 -60.5 31t-58.5 33.5t-57 39t-47 44t-40 52.5t-24 60.5t-10 71.5zM217 680q0 -30 7.5 -56.5t25.5 -49t35 -39t49 -36.5t54 -32t63 -33q123 -65 165 -92q84 93 84 195q0 39 -15 73.5t-36 59t-59 51t-69.5 43t-82.5 41.5q-60 31 -131 72 q-38 -42 -64 -95t-26 -102z" />
<glyph unicode="&#xa8;" horiz-adv-x="671" d="M57 1268q0 34 25.5 58t60.5 24q34 0 59 -24t25 -58q0 -33 -26 -57.5t-60 -24.5q-36 0 -60 24t-24 58zM440 1268q0 34 25.5 58t60.5 24q34 0 59 -24t25 -58t-26.5 -58t-61.5 -24q-34 0 -58 24t-24 58z" />
<glyph unicode="&#xa9;" horiz-adv-x="1609" d="M90 696q0 146 56.5 278t151.5 227t227 151.5t278 56.5q145 0 277 -56.5t227.5 -151.5t152 -227.5t56.5 -277.5q0 -193 -95.5 -357t-260 -259.5t-357.5 -95.5q-145 0 -277.5 56.5t-227.5 152t-151.5 227t-56.5 276.5zM184 696q0 -168 83 -310.5t225.5 -225t310.5 -82.5 q126 0 240.5 49t197 131.5t131.5 197t49 240.5q0 169 -82.5 311.5t-225 225t-310.5 82.5t-310.5 -82.5t-225.5 -225t-83 -311.5zM446 696q0 189 102 295.5t267 106.5q112 0 225 -45q8 -3 14.5 -12t6.5 -19l-8 -35q-6 -24 -31 -24q-8 0 -12 2q-100 43 -189 43 q-116 0 -194 -87.5t-78 -224.5q0 -136 78 -223.5t194 -87.5q81 0 191 47q4 2 12 2q25 0 31 -24l8 -31q0 -24 -19 -35q-120 -49 -229 -49q-165 0 -267 106.5t-102 294.5z" />
<glyph unicode="&#xaa;" horiz-adv-x="780" d="M94 868q0 142 81 235t206 93q95 0 174 -47q0 1 1 7t1 9q3 14 11.5 17.5t31.5 3.5q19 0 26 -10t7 -33v-473q0 -34 29 -49q18 -11 18 -23t-8 -25q-15 -30 -35 -30q-6 0 -10 2q-56 22 -70 67q-32 -31 -84.5 -51t-91.5 -20q-125 0 -206 91.5t-81 235.5zM184 868 q0 -112 54.5 -181t142.5 -69q104 0 166 80v361q-86 57 -166 57q-88 0 -142.5 -68t-54.5 -180z" />
<glyph unicode="&#xab;" horiz-adv-x="1128" d="M61 485q0 7 5 17l274 454q3 5 12 10t15 5h43q13 0 22.5 -8.5t9.5 -20.5q0 -9 -6 -18l-266 -439l266 -438q6 -9 6 -18q0 -12 -9.5 -20.5t-22.5 -8.5h-43q-6 0 -15 4.5t-12 9.5l-274 455q-5 10 -5 16zM670 485q0 9 4 17l274 454q3 5 12 10t15 5h43q14 0 23.5 -8.5 t9.5 -20.5q0 -8 -7 -18l-266 -439l266 -438q7 -11 7 -18q0 -12 -9.5 -20.5t-23.5 -8.5h-43q-6 0 -15 4.5t-12 9.5l-274 455q-4 8 -4 16z" />
<glyph unicode="&#xac;" horiz-adv-x="1196" d="M113 672v33q0 12 9 21t21 9h910q12 0 21 -9t9 -21v-392q0 -12 -9 -21t-21 -9h-33q-12 0 -21.5 9t-9.5 21v328h-846q-12 0 -21 9.5t-9 21.5z" />
<glyph unicode="&#xad;" horiz-adv-x="831" d="M115 455v39q0 12 9 21t21 9h543q12 0 21.5 -9t9.5 -21v-39q0 -12 -9.5 -21.5t-21.5 -9.5h-543q-12 0 -21 9.5t-9 21.5z" />
<glyph unicode="&#xae;" horiz-adv-x="1093" d="M90 954q0 93 36 177t97.5 145t145.5 97t176 36q189 0 321.5 -133t132.5 -322q0 -123 -60.5 -227.5t-165 -165.5t-228.5 -61q-188 0 -321.5 133.5t-133.5 320.5zM172 954q0 -154 109.5 -263t263.5 -109q155 0 264 109t109 263q0 155 -109 264t-264 109q-154 0 -263.5 -109 t-109.5 -264zM385 707v495q0 12 8.5 20.5t20.5 10.5q25 2 106 2q55 0 95.5 -15t61.5 -40.5t30.5 -53t9.5 -57.5q0 -90 -103 -137v-10q52 -35 111 -144l27 -53q4 -12 4 -16q0 -12 -9 -20.5t-22 -8.5h-20q-6 0 -15 5t-12 11q-19 33 -35 72q-32 66 -72 106q-37 29 -53 29h-55 v-196q0 -12 -9.5 -21.5t-21.5 -9.5h-16q-12 0 -21.5 9.5t-9.5 21.5zM463 969h59q54 0 85.5 27t31.5 71q0 40 -26.5 70t-84.5 30q-29 0 -65 -4v-194z" />
<glyph unicode="&#xaf;" horiz-adv-x="675" d="M94 1243v27q0 12 9.5 21t21.5 9h424q12 0 21.5 -9t9.5 -21v-27q0 -12 -9.5 -21.5t-21.5 -9.5h-424q-12 0 -21.5 9.5t-9.5 21.5z" />
<glyph unicode="&#xb0;" horiz-adv-x="782" d="M86 1104q0 127 89.5 216t215.5 89t215.5 -89t89.5 -216q0 -126 -89.5 -215.5t-215.5 -89.5t-215.5 89.5t-89.5 215.5zM176 1104q0 -89 63 -151t152 -62t152 62t63 151t-63 152t-152 63t-152 -63t-63 -152z" />
<glyph unicode="&#xb1;" horiz-adv-x="1196" d="M113 31v32q0 12 9 21.5t21 9.5h910q12 0 21 -9.5t9 -21.5v-32q0 -12 -9 -21.5t-21 -9.5h-910q-12 0 -21 9.5t-9 21.5zM113 584v32q0 12 9 21.5t21 9.5h408v293q0 12 9.5 21.5t21.5 9.5h32q12 0 21.5 -9.5t9.5 -21.5v-293h408q12 0 21 -9.5t9 -21.5v-32q0 -12 -9 -21.5 t-21 -9.5h-408v-295q0 -12 -9.5 -21.5t-21.5 -9.5h-32q-12 0 -21.5 9.5t-9.5 21.5v295h-408q-12 0 -21 9.5t-9 21.5z" />
<glyph unicode="&#xb2;" horiz-adv-x="708" d="M100 993q0 17 5.5 27.5t23.5 28.5l193 211q79 87 125 159t56.5 103t10.5 53q0 73 -45.5 114.5t-116.5 41.5q-61 0 -178 -84q-10 -7 -18 -7q-22 0 -29 23q-8 27 -8 35q0 15 14 24q134 86 219 86q114 0 184 -62t70 -175q0 -55 -56.5 -153.5t-158.5 -211.5l-155 -172h339 q12 0 21.5 -9t9.5 -21v-15q0 -12 -9.5 -21.5t-21.5 -9.5h-444q-13 0 -22 9.5t-9 25.5z" />
<glyph unicode="&#xb3;" horiz-adv-x="737" d="M104 1042q0 1 1 4.5t1 4.5l7 20q7 21 28 21q5 0 15 -5q115 -59 194 -59q86 0 138.5 43.5t52.5 120.5q0 148 -244 158q-12 0 -20.5 9t-8.5 21v17q0 11 9 19.5t20 10.5q97 8 156 59.5t59 106.5q0 59 -39 97.5t-113 38.5q-64 0 -180 -58q-9 -6 -18 -6q-19 0 -25 23l-6 24 q0 22 16 33q126 63 213 63q109 0 179.5 -61.5t70.5 -153.5q0 -55 -32 -106t-99 -84v-8q79 -21 117.5 -76.5t38.5 -126.5q0 -111 -79 -177.5t-206 -66.5q-100 0 -229 66q-17 7 -17 28z" />
<glyph unicode="&#xb4;" horiz-adv-x="688" d="M209 1171q0 12 6 21l147 190q13 13 25 13h53q13 0 22 -8.5t9 -20.5q0 -15 -8 -23q-12 -14 -86.5 -93.5t-93.5 -98.5q-8 -8 -23 -8h-20q-13 0 -22 8t-9 20z" />
<glyph unicode="&#xb5;" d="M158 -391v1331q0 12 9 21.5t21 9.5h41q12 0 21.5 -9.5t9.5 -21.5v-610q0 -120 55.5 -185t196.5 -65q139 0 260 135v725q0 12 9.5 21.5t21.5 9.5h41q12 0 21 -9.5t9 -21.5v-909q0 -12 -9 -21.5t-21 -9.5h-25q-12 0 -20.5 8.5t-10.5 20.5l-8 69h-12q-57 -55 -128.5 -84.5 t-141.5 -29.5q-131 0 -248 90q0 -43 10 -465q0 -12 -9.5 -21.5t-21.5 -9.5h-41q-12 0 -21 9.5t-9 21.5z" />
<glyph unicode="&#xb6;" horiz-adv-x="1054" d="M109 1006q0 160 113.5 273.5t273.5 113.5h442q12 0 21.5 -9.5t9.5 -21.5v-1557q0 -12 -9.5 -21t-21.5 -9h-45q-12 0 -21.5 9t-9.5 21v1491h-260v-1491q0 -12 -9.5 -21t-21.5 -9h-45q-12 0 -21 9t-9 21v811q-164 0 -275.5 116t-111.5 274z" />
<glyph unicode="&#xb7;" horiz-adv-x="1341" d="M586 696q0 37 23 58.5t63 21.5q39 0 62.5 -22t23.5 -58q0 -39 -23.5 -60.5t-62.5 -21.5q-40 0 -63 21.5t-23 60.5z" />
<glyph unicode="&#xb8;" horiz-adv-x="692" d="M193 -406l8 35q4 15 18 15q1 0 4.5 -1t5.5 -1q34 -11 76 -11q38 0 73.5 25t35.5 61q0 62 -109 76q-29 3 -29 23q0 1 5 16l43 88q7 17 28 17h21q16 0 16 -15q0 -4 -4 -16l-27 -58q65 -9 105.5 -46.5t40.5 -84.5q0 -78 -61.5 -116.5t-151.5 -38.5q-38 0 -82 12 q-16 4 -16 20z" />
<glyph unicode="&#xb9;" horiz-adv-x="731" d="M104 1616q0 16 17 24l205 134q27 18 47 18h20q12 0 21.5 -9.5t9.5 -21.5v-729h174q12 0 21.5 -9.5t9.5 -21.5v-14q0 -12 -9.5 -21.5t-21.5 -9.5h-444q-12 0 -21.5 9.5t-9.5 21.5v14q0 12 9.5 21.5t21.5 9.5h172v654l-172 -119q-8 -8 -19 -8q-17 0 -20 20l-9 27 q-2 4 -2 10z" />
<glyph unicode="&#xba;" horiz-adv-x="776" d="M94 868q0 143 82 235.5t211 92.5q130 0 212.5 -92.5t82.5 -235.5q0 -145 -82 -236t-213 -91q-129 0 -211 91t-82 236zM184 868q0 -114 56.5 -182t146.5 -68q92 0 148.5 68t56.5 182t-56.5 181t-148.5 67q-91 0 -147 -67t-56 -181z" />
<glyph unicode="&#xbb;" horiz-adv-x="1128" d="M76 29q0 9 6 18l270 438l-268 439q-6 9 -6 18q0 12 9.5 20.5t23.5 8.5h41q18 0 26 -15q61 -105 277 -454q2 -4 2 -17q0 -12 -2 -16l-275 -455q-3 -5 -11.5 -9.5t-14.5 -4.5h-45q-14 0 -23.5 8.5t-9.5 20.5zM684 29q0 9 6 18l271 438l-269 439q-6 9 -6 18q0 12 9.5 20.5 t23.5 8.5h41q18 0 26 -15q61 -105 277 -454q2 -4 2 -17q0 -12 -2 -16l-275 -455q-3 -5 -11.5 -9.5t-14.5 -4.5h-45q-14 0 -23.5 8.5t-9.5 20.5z" />
<glyph unicode="&#xbc;" horiz-adv-x="1597" d="M106 1217q0 15 17 24l205 135q29 19 47 19h20q12 0 21.5 -9.5t9.5 -21.5v-729h174q12 0 21.5 -9.5t9.5 -21.5v-14q0 -12 -9.5 -21.5t-21.5 -9.5h-444q-12 0 -21.5 9.5t-9.5 21.5v14q0 12 9.5 21.5t21.5 9.5h172v653l-172 -121q-9 -6 -19 -6q-17 0 -20 21l-8 26q-3 3 -3 9 zM449 25q0 10 4 16l804 1325q6 9 20 17t26 8h22q13 0 21 -7t8 -18q0 -7 -6 -16l-803 -1325q-6 -9 -19.5 -17t-23.5 -8h-25q-28 0 -28 25zM932 254v10q0 27 22 55l361 504q9 13 24 13h35q12 0 21.5 -9.5t9.5 -21.5v-506h59q12 0 21.5 -9.5t9.5 -21.5v-14q0 -12 -9.5 -21.5 t-21.5 -9.5h-59v-192q0 -12 -9.5 -21.5t-21.5 -9.5h-28q-12 0 -21.5 9.5t-9.5 21.5v192h-352q-12 0 -21.5 9.5t-9.5 21.5zM1040 297q47 2 78 2h197v293l2 104q-69 -100 -100 -143z" />
<glyph unicode="&#xbd;" horiz-adv-x="1746" d="M106 1217q0 16 19 24l203 133q32 19 49 19h18q12 0 21.5 -9.5t9.5 -21.5v-729h174q12 0 21.5 -9.5t9.5 -21.5v-14q0 -12 -9.5 -21.5t-21.5 -9.5h-444q-12 0 -21.5 9.5t-9.5 21.5v14q0 12 9.5 21.5t21.5 9.5h172v653l-170 -119q-11 -8 -21 -8q-17 0 -20 21l-8 26 q-3 6 -3 11zM455 25q0 9 6 18l805 1323q4 10 18 17.5t25 7.5h22q13 0 21 -7t8 -18q0 -10 -4 -16l-803 -1323q-6 -11 -19.5 -19t-25.5 -8h-25q-28 0 -28 25zM1139 35q0 17 4.5 26.5t21.5 30.5l195 209q62 66 103.5 123t59 95t23.5 59.5t6 37.5q0 73 -45 114.5t-116 41.5 q-62 0 -179 -84q-9 -6 -18 -6q-22 0 -29 23q-8 27 -8 34q0 16 14 25q134 86 220 86q113 0 183.5 -62.5t70.5 -175.5q0 -54 -56.5 -151.5t-158.5 -212.5l-156 -172h340q12 0 21.5 -9.5t9.5 -21.5v-14q0 -12 -9.5 -21.5t-21.5 -9.5h-445q-13 0 -21.5 9t-8.5 26z" />
<glyph unicode="&#xbe;" horiz-adv-x="1546" d="M106 643q0 1 1.5 4t1.5 4l6 21q7 20 28 20q7 0 15 -4q115 -59 194 -59q86 0 138.5 43.5t52.5 120.5q0 147 -244 157q-12 0 -20.5 9.5t-8.5 21.5v16q0 11 9 20t20 11q97 8 156 59.5t59 106.5q0 59 -39 97t-113 38q-66 0 -180 -57q-9 -6 -20 -6q-20 0 -25 22l-6 25 q0 3 6.5 16t12.5 17q126 63 212 63q109 0 179.5 -61.5t70.5 -153.5q0 -54 -32.5 -105.5t-98.5 -84.5v-9q78 -21 117 -76t39 -126q0 -111 -79 -177.5t-206 -66.5q-102 0 -229 65q-17 7 -17 29zM403 27q0 9 5 16l804 1323q6 10 19.5 17.5t25.5 7.5h23q29 0 29 -23q0 -9 -6 -18 l-803 -1323q-4 -10 -18 -18.5t-25 -8.5h-25q-13 0 -21 8t-8 19zM879 254v12q0 25 22 56l361 501q9 13 24 13h35q12 0 21.5 -9.5t9.5 -21.5v-506h59q12 0 21.5 -9.5t9.5 -21.5v-14q0 -12 -9.5 -21.5t-21.5 -9.5h-59v-192q0 -12 -9.5 -21.5t-21.5 -9.5h-29q-12 0 -21 9.5 t-9 21.5v192h-353q-12 0 -21 9.5t-9 21.5zM987 297q47 2 78 2h197v293l2 104q-72 -100 -101 -143z" />
<glyph unicode="&#xbf;" horiz-adv-x="1107" d="M303 -162q0 41 12.5 83t27 72.5t48 76.5t52.5 69t65 76q75 88 101 165.5t26 207.5q0 12 9.5 21t21.5 9h24q11 0 20.5 -8t10.5 -18v-37q0 -119 -32.5 -216t-88.5 -159q-97 -107 -141.5 -181.5t-44.5 -154.5q0 -96 67 -141t183 -45q98 0 178 39q8 4 14 4q11 0 20.5 -7.5 t10.5 -17.5l8 -32q0 -25 -16 -35q-106 -47 -238 -47q-162 0 -250 79.5t-88 196.5zM580 905q0 39 23.5 60.5t62.5 21.5q40 0 63 -21.5t23 -60.5q0 -37 -23 -58.5t-63 -21.5q-39 0 -62.5 22t-23.5 58z" />
<glyph unicode="&#xc0;" horiz-adv-x="1267" d="M53 29q0 8 2 12l523 1331q3 8 12 14.5t16 6.5h56q7 0 16 -6.5t12 -14.5l522 -1331q2 -4 2 -12q0 -12 -8.5 -20.5t-21.5 -8.5h-56q-8 0 -16.5 6.5t-11.5 13.5l-142 361h-651q-10 -25 -33.5 -85.5t-35.5 -92.5l-70 -183q-3 -7 -12 -13.5t-17 -6.5h-55q-13 0 -22 8.5 t-9 20.5zM346 479h576q-238 625 -281 766h-12q-21 -65 -91.5 -256.5t-131.5 -350.5zM422 1784q0 9 8 15.5t21 6.5h51q31 0 47 -24q120 -162 143 -191q6 -9 6 -18q0 -19 -22 -19h-21q-31 0 -51 23l-172 184q-10 13 -10 23z" />
<glyph unicode="&#xc1;" horiz-adv-x="1267" d="M53 29q0 -12 9 -20.5t22 -8.5h55q8 0 17 6.5t12 13.5l70 183q12 32 35.5 92.5t33.5 85.5h651l142 -361q3 -7 11.5 -13.5t16.5 -6.5h56q13 0 21.5 8.5t8.5 20.5q0 8 -2 12l-522 1331q-3 8 -12 14.5t-16 6.5h-56q-7 0 -16 -6.5t-12 -14.5l-523 -1331q-2 -4 -2 -12zM346 479 q236 619 283 766h12q19 -63 89.5 -254.5t131.5 -351.5l60 -160h-576zM539 1583q0 12 6 21l147 190q12 12 25 12h53q13 0 22 -8t9 -20q0 -15 -8 -23q-11 -13 -86.5 -93t-94.5 -99q-9 -9 -22 -9h-21q-13 0 -21.5 8.5t-8.5 20.5z" />
<glyph unicode="&#xc2;" horiz-adv-x="1267" d="M53 29q0 -12 9 -20.5t22 -8.5h55q8 0 17 6.5t12 13.5l70 183q12 32 35.5 92.5t33.5 85.5h651l142 -361q3 -7 11.5 -13.5t16.5 -6.5h56q13 0 21.5 8.5t8.5 20.5q0 8 -2 12l-522 1331q-3 8 -12 14.5t-16 6.5h-56q-7 0 -16 -6.5t-12 -14.5l-523 -1331q-2 -4 -2 -12zM346 479 q236 619 283 766h12q19 -63 89.5 -254.5t131.5 -351.5l60 -160h-576zM399 1573q0 8 9 20l145 199q8 12 25 12h81q17 0 25 -12l143 -199q9 -12 9 -22q0 -19 -23 -19h-49q-16 0 -33 17l-113 174q-3 -1 -64 -100l-48 -76q-15 -15 -35 -15h-47q-11 0 -18 6t-7 15z" />
<glyph unicode="&#xc3;" horiz-adv-x="1267" d="M53 29q0 -12 9 -20.5t22 -8.5h55q8 0 17 6.5t12 13.5l70 183q12 32 35.5 92.5t33.5 85.5h651l142 -361q3 -7 11.5 -13.5t16.5 -6.5h56q13 0 21.5 8.5t8.5 20.5q0 8 -2 12l-522 1331q-3 8 -12 14.5t-16 6.5h-56q-7 0 -16 -6.5t-12 -14.5l-523 -1331q-2 -4 -2 -12zM346 479 q236 619 283 766h12q19 -63 89.5 -254.5t131.5 -351.5l60 -160h-576zM358 1636q11 59 47 94t87 35q70 0 141 -47q64 -43 106 -43q17 0 32.5 17t23.5 43q7 22 28 22h25q14 0 22.5 -10t8.5 -27q-10 -58 -47 -93.5t-87 -35.5q-75 0 -145 45q-68 45 -104 45q-19 0 -33.5 -16 t-20.5 -43q-3 -9 -12 -16t-18 -7h-23q-14 0 -22.5 10t-8.5 27z" />
<glyph unicode="&#xc4;" horiz-adv-x="1267" d="M53 29q0 -12 9 -20.5t22 -8.5h55q8 0 17 6.5t12 13.5l70 183q12 32 35.5 92.5t33.5 85.5h651l142 -361q3 -7 11.5 -13.5t16.5 -6.5h56q13 0 21.5 8.5t8.5 20.5q0 8 -2 12l-522 1331q-3 8 -12 14.5t-16 6.5h-56q-7 0 -16 -6.5t-12 -14.5l-523 -1331q-2 -4 -2 -12z M342 1675q0 34 25.5 58t60.5 24q34 0 59 -24t25 -58q0 -33 -26 -57.5t-60 -24.5q-36 0 -60 24t-24 58zM346 479q236 619 283 766h12q19 -63 89.5 -254.5t131.5 -351.5l60 -160h-576zM725 1675q0 34 25.5 58t60.5 24q34 0 59 -24t25 -58t-26.5 -58t-61.5 -24q-34 0 -58 24 t-24 58z" />
<glyph unicode="&#xc5;" horiz-adv-x="1267" d="M53 29q0 8 2 12l523 1331q3 8 12 14.5t16 6.5h56q7 0 16 -6.5t12 -14.5l522 -1331q2 -4 2 -12q0 -12 -8.5 -20.5t-21.5 -8.5h-56q-8 0 -16.5 6.5t-11.5 13.5l-142 361h-651q-10 -25 -33.5 -85.5t-35.5 -92.5l-70 -183q-3 -7 -12 -13.5t-17 -6.5h-55q-13 0 -22 8.5 t-9 20.5zM346 479h576q-238 625 -281 766h-12q-21 -65 -91.5 -256.5t-131.5 -350.5zM426 1677q0 72 56 124t134 52t134.5 -52t56.5 -124t-56.5 -123t-134.5 -51t-134 51t-56 123zM498 1677q0 -48 33.5 -81t84.5 -33q48 0 84.5 33.5t36.5 80.5t-36.5 81t-84.5 34 q-50 0 -84 -33.5t-34 -81.5z" />
<glyph unicode="&#xc6;" horiz-adv-x="1712" d="M63 31q0 9 5 16l770 1331q8 15 26 15h701q12 0 21 -9.5t9 -21.5v-37q0 -12 -9 -21.5t-21 -9.5h-578v-520h514q12 0 21.5 -9.5t9.5 -21.5v-34q0 -12 -9.5 -21.5t-21.5 -9.5h-514v-582h590q12 0 21.5 -9t9.5 -21v-35q0 -12 -9.5 -21.5t-21.5 -9.5h-668q-12 0 -21 9.5 t-9 21.5v350h-500l-78 -137q-13 -22 -60.5 -103.5t-70.5 -124.5q-3 -6 -12 -11t-15 -5h-49q-13 0 -22 9.5t-9 21.5zM436 479h443v766h-15q-24 -53 -131 -244.5t-202 -356.5z" />
<glyph unicode="&#xc7;" horiz-adv-x="1261" d="M113 696q0 201 88 364t245.5 256t352.5 93q197 0 368 -84q19 -11 19 -29l-10 -45q-1 -12 -9.5 -19t-19.5 -7q-5 0 -17 4q-161 80 -331 80q-160 0 -291.5 -81t-206 -221.5t-74.5 -310.5t74.5 -310t206 -221t291.5 -81q163 0 329 78q12 4 17 4q19 0 26 -21l15 -39 q0 -1 1 -4t1 -4q0 -21 -17 -28q-180 -86 -372 -86q-195 0 -352.5 93t-245.5 255.5t-88 363.5zM604 -387l8 35q4 14 19 14q1 0 4.5 -1t5.5 -1q31 -10 76 -10q38 0 73 25t35 61q0 62 -108 76q-29 3 -29 22q0 4 4 16l43 89q6 16 29 16h20q17 0 17 -14q0 -5 -4 -17l-27 -57 q65 -9 105 -46.5t40 -84.5q0 -78 -61.5 -117t-151.5 -39q-38 0 -81 12q-6 2 -11.5 8t-5.5 13z" />
<glyph unicode="&#xc8;" horiz-adv-x="1026" d="M197 31v1331q0 12 9 21.5t21 9.5h654q12 0 21 -9.5t9 -21.5v-37q0 -12 -9 -21.5t-21 -9.5h-578v-520h514q12 0 21.5 -9.5t9.5 -21.5v-34q0 -12 -9.5 -21.5t-21.5 -9.5h-514v-580h588q12 0 21.5 -9t9.5 -21v-37q0 -12 -9.5 -21.5t-21.5 -9.5h-664q-12 0 -21 9.5t-9 21.5z M406 1782q0 22 28 22h51q31 0 47 -24q121 -162 144 -191q6 -9 6 -18q0 -8 -6.5 -13.5t-16.5 -5.5h-20q-31 0 -51 23l-172 184q-10 13 -10 23z" />
<glyph unicode="&#xc9;" horiz-adv-x="1026" d="M197 31v1331q0 12 9 21.5t21 9.5h654q12 0 21 -9.5t9 -21.5v-37q0 -12 -9 -21.5t-21 -9.5h-578v-520h514q12 0 21.5 -9.5t9.5 -21.5v-34q0 -12 -9.5 -21.5t-21.5 -9.5h-514v-580h588q12 0 21.5 -9t9.5 -21v-37q0 -12 -9.5 -21.5t-21.5 -9.5h-664q-12 0 -21 9.5t-9 21.5z M416 1583q0 12 6 21l147 190q12 12 25 12h53q13 0 22 -8t9 -20q0 -15 -8 -23q-11 -13 -86.5 -93t-94.5 -99q-9 -9 -22 -9h-21q-13 0 -21.5 8.5t-8.5 20.5z" />
<glyph unicode="&#xca;" horiz-adv-x="1026" d="M197 31v1331q0 12 9 21.5t21 9.5h654q12 0 21 -9.5t9 -21.5v-37q0 -12 -9 -21.5t-21 -9.5h-578v-520h514q12 0 21.5 -9.5t9.5 -21.5v-34q0 -12 -9.5 -21.5t-21.5 -9.5h-514v-580h588q12 0 21.5 -9t9.5 -21v-37q0 -12 -9.5 -21.5t-21.5 -9.5h-664q-12 0 -21 9.5t-9 21.5z M326 1573q0 9 8 20l145 199q8 12 25 12h82q16 0 24 -12l144 -199q8 -11 8 -22q0 -19 -23 -19h-49q-16 0 -33 17l-112 174q-3 -1 -64 -99l-49 -77q-15 -15 -35 -15h-47q-10 0 -17 6t-7 15z" />
<glyph unicode="&#xcb;" horiz-adv-x="1026" d="M197 31v1331q0 12 9 21.5t21 9.5h654q12 0 21 -9.5t9 -21.5v-37q0 -12 -9 -21.5t-21 -9.5h-578v-520h514q12 0 21.5 -9.5t9.5 -21.5v-34q0 -12 -9.5 -21.5t-21.5 -9.5h-514v-580h588q12 0 21.5 -9t9.5 -21v-37q0 -12 -9.5 -21.5t-21.5 -9.5h-664q-12 0 -21 9.5t-9 21.5z M260 1679q0 34 25.5 58t60.5 24q34 0 59 -24t25 -58q0 -33 -26 -57.5t-60 -24.5q-36 0 -60 24t-24 58zM643 1679q0 34 25.5 58t60.5 24q34 0 59 -24t25 -58t-26.5 -58t-61.5 -24q-34 0 -58 24t-24 58z" />
<glyph unicode="&#xcc;" horiz-adv-x="860" d="M131 31v32q0 12 9.5 21.5t21.5 9.5h213v1204h-213q-12 0 -21.5 9.5t-9.5 21.5v33q0 12 9.5 21.5t21.5 9.5h532q12 0 21.5 -9.5t9.5 -21.5v-33q0 -12 -9.5 -21.5t-21.5 -9.5h-213v-1204h213q12 0 21.5 -9.5t9.5 -21.5v-32q0 -12 -9.5 -21.5t-21.5 -9.5h-532 q-12 0 -21.5 9.5t-9.5 21.5zM240 1784q0 22 28 22h51q32 0 48 -24q120 -162 143 -191q6 -9 6 -18q0 -19 -22 -19h-21q-31 0 -51 23l-172 184q-10 13 -10 23z" />
<glyph unicode="&#xcd;" horiz-adv-x="860" d="M131 31v32q0 12 9.5 21.5t21.5 9.5h213v1204h-213q-12 0 -21.5 9.5t-9.5 21.5v33q0 12 9.5 21.5t21.5 9.5h532q12 0 21.5 -9.5t9.5 -21.5v-33q0 -12 -9.5 -21.5t-21.5 -9.5h-213v-1204h213q12 0 21.5 -9.5t9.5 -21.5v-32q0 -12 -9.5 -21.5t-21.5 -9.5h-532 q-12 0 -21.5 9.5t-9.5 21.5zM342 1583q0 12 6 21l148 190q12 12 24 12h53q13 0 22 -8t9 -20q0 -15 -8 -23q-12 -14 -86.5 -93.5t-93.5 -98.5q-9 -9 -23 -9h-20q-13 0 -22 8.5t-9 20.5z" />
<glyph unicode="&#xce;" horiz-adv-x="856" d="M131 31v32q0 12 9.5 21.5t21.5 9.5h213v1204h-213q-12 0 -21.5 9.5t-9.5 21.5v33q0 12 9.5 21.5t21.5 9.5h532q12 0 21.5 -9.5t9.5 -21.5v-33q0 -12 -9.5 -21.5t-21.5 -9.5h-213v-1204h213q12 0 21.5 -9.5t9.5 -21.5v-32q0 -12 -9.5 -21.5t-21.5 -9.5h-532 q-12 0 -21.5 9.5t-9.5 21.5zM205 1573q0 9 8 20l145 199q8 12 25 12h82q16 0 24 -12l144 -199q8 -11 8 -22q0 -19 -23 -19h-49q-15 0 -32 17l-113 174q-3 -1 -64 -99l-49 -77q-15 -15 -35 -15h-47q-10 0 -17 6t-7 15z" />
<glyph unicode="&#xcf;" horiz-adv-x="856" d="M131 31v32q0 12 9.5 21.5t21.5 9.5h213v1204h-213q-12 0 -21.5 9.5t-9.5 21.5v33q0 12 9.5 21.5t21.5 9.5h532q12 0 21.5 -9.5t9.5 -21.5v-33q0 -12 -9.5 -21.5t-21.5 -9.5h-213v-1204h213q12 0 21.5 -9.5t9.5 -21.5v-32q0 -12 -9.5 -21.5t-21.5 -9.5h-532 q-12 0 -21.5 9.5t-9.5 21.5zM147 1679q0 34 25.5 58t60.5 24q34 0 59 -24t25 -58q0 -33 -26 -57.5t-60 -24.5q-36 0 -60 24t-24 58zM530 1679q0 34 25.5 58t60.5 24q34 0 59 -24t25 -58t-26.5 -58t-61.5 -24q-34 0 -58 24t-24 58z" />
<glyph unicode="&#xd0;" horiz-adv-x="1452" d="M43 700v37q0 12 9.5 21.5t21.5 9.5h137v598q0 10 9 19.5t20 11.5q126 12 313 12q162 0 294.5 -39t222.5 -105.5t151.5 -158t89.5 -194.5t28 -216t-28 -216t-89.5 -194t-151.5 -157.5t-222.5 -105.5t-294.5 -39q-187 0 -313 12q-11 2 -20 11.5t-9 19.5v643h-137 q-12 0 -21.5 9t-9.5 21zM317 88h25q98 -8 225 -8q304 0 480 159.5t176 456.5q0 296 -176.5 455.5t-479.5 159.5q-127 0 -225 -8q-1 0 -10 -1.5t-15 -1.5v-532h150q12 0 21.5 -9.5t9.5 -21.5v-37q0 -12 -9.5 -21t-21.5 -9h-150v-582z" />
<glyph unicode="&#xd1;" horiz-adv-x="1478" d="M197 31q0 -12 9 -21.5t21 -9.5h45q12 0 21.5 9.5t9.5 21.5v1007q0 44 -8 232h8l819 -1256q3 -5 12 -9.5t15 -4.5h102q12 0 21.5 9.5t9.5 21.5v1331q0 12 -9.5 21.5t-21.5 9.5h-45q-12 0 -21 -9.5t-9 -21.5v-1010q0 -45 8 -233h-8l-820 1259q-3 5 -11.5 10t-14.5 5h-103 q-12 0 -21 -9.5t-9 -21.5v-1331zM451 1640q11 59 46.5 94t86.5 35q70 0 141 -47q64 -43 106 -43q17 0 32.5 17t23.5 43q7 22 28 22h25q14 0 22.5 -10t8.5 -27q-10 -58 -46.5 -93.5t-86.5 -35.5q-76 0 -146 45q-70 46 -104 46q-18 0 -32.5 -16.5t-20.5 -43.5 q-3 -9 -12.5 -15.5t-18.5 -6.5h-23q-30 0 -30 36z" />
<glyph unicode="&#xd2;" horiz-adv-x="1554" d="M113 694q0 150 47.5 281.5t133 227.5t210.5 151t272 55q199 0 352 -94.5t234.5 -257t81.5 -363.5q0 -203 -80.5 -363.5t-233.5 -253.5t-354 -93q-200 0 -352 93t-231.5 253.5t-79.5 363.5zM227 694q0 -270 151.5 -440t397.5 -170q249 0 401 170t152 440q0 179 -68 318.5 t-194.5 218t-290.5 78.5q-246 0 -397.5 -171.5t-151.5 -443.5zM561 1786q0 9 8 15.5t21 6.5h51q31 0 47 -24q120 -162 143 -191q7 -11 7 -18q0 -8 -6.5 -13.5t-16.5 -5.5h-20q-32 0 -52 23l-172 184q-10 13 -10 23z" />
<glyph unicode="&#xd3;" horiz-adv-x="1554" d="M113 694q0 150 47.5 281.5t133 227.5t210.5 151t272 55q199 0 352 -94.5t234.5 -257t81.5 -363.5q0 -203 -80.5 -363.5t-233.5 -253.5t-354 -93q-200 0 -352 93t-231.5 253.5t-79.5 363.5zM227 694q0 -270 151.5 -440t397.5 -170q249 0 401 170t152 440q0 179 -68 318.5 t-194.5 218t-290.5 78.5q-246 0 -397.5 -171.5t-151.5 -443.5zM670 1585q0 12 6 21l147 190q12 12 25 12h53q13 0 22 -8t9 -20q0 -15 -8 -23q-11 -13 -86.5 -93t-94.5 -99q-9 -9 -22 -9h-21q-13 0 -21.5 8.5t-8.5 20.5z" />
<glyph unicode="&#xd4;" horiz-adv-x="1554" d="M113 694q0 150 47.5 281.5t133 227.5t210.5 151t272 55q199 0 352 -94.5t234.5 -257t81.5 -363.5q0 -203 -80.5 -363.5t-233.5 -253.5t-354 -93q-200 0 -352 93t-231.5 253.5t-79.5 363.5zM227 694q0 -270 151.5 -440t397.5 -170q249 0 401 170t152 440q0 179 -68 318.5 t-194.5 218t-290.5 78.5q-246 0 -397.5 -171.5t-151.5 -443.5zM537 1571q0 9 8 20l145 199q8 12 25 12h82q16 0 24 -12l144 -199q8 -11 8 -22q0 -19 -23 -19h-49q-16 0 -33 17l-112 174q-3 -1 -64 -99l-49 -77q-15 -15 -35 -15h-47q-10 0 -17 6t-7 15z" />
<glyph unicode="&#xd5;" horiz-adv-x="1554" d="M113 694q0 150 47.5 281.5t133 227.5t210.5 151t272 55q199 0 352 -94.5t234.5 -257t81.5 -363.5q0 -203 -80.5 -363.5t-233.5 -253.5t-354 -93q-200 0 -352 93t-231.5 253.5t-79.5 363.5zM227 694q0 -270 151.5 -440t397.5 -170q249 0 401 170t152 440q0 179 -68 318.5 t-194.5 218t-290.5 78.5q-246 0 -397.5 -171.5t-151.5 -443.5zM496 1638q11 59 46.5 94t86.5 35q70 0 141 -47q64 -43 107 -43q17 0 32 16.5t23 43.5q3 9 11.5 15.5t17.5 6.5h24q14 0 22.5 -10t8.5 -27q-10 -58 -46.5 -93.5t-86.5 -35.5q-76 0 -146 45q-68 45 -104 45 q-18 0 -32.5 -16t-20.5 -43q-3 -9 -12.5 -15.5t-18.5 -6.5h-23q-30 0 -30 36z" />
<glyph unicode="&#xd6;" horiz-adv-x="1554" d="M113 694q0 150 47.5 281.5t133 227.5t210.5 151t272 55q199 0 352 -94.5t234.5 -257t81.5 -363.5q0 -203 -80.5 -363.5t-233.5 -253.5t-354 -93q-200 0 -352 93t-231.5 253.5t-79.5 363.5zM227 694q0 -270 151.5 -440t397.5 -170q249 0 401 170t152 440q0 179 -68 318.5 t-194.5 218t-290.5 78.5q-246 0 -397.5 -171.5t-151.5 -443.5zM481 1669q0 34 25.5 58t60.5 24q34 0 59 -24t25 -58q0 -33 -26 -57.5t-60 -24.5q-36 0 -60 24t-24 58zM864 1669q0 34 25.5 58t60.5 24q34 0 59 -24t25 -58t-26.5 -58t-61.5 -24q-34 0 -58 24t-24 58z" />
<glyph unicode="&#xd7;" horiz-adv-x="1218" d="M152 905q0 13 10 23l20 20q8 8 23 8q14 0 22 -8l398 -397l397 397q10 10 22 10q18 0 27 -18q4 -4 12 -10q13 -10 13 -27q0 -11 -9 -20l-395 -398l395 -397q9 -9 9 -22q0 -14 -9 -23l-20 -20q-11 -11 -23 -11q-11 0 -22 11l-397 395l-398 -395q-9 -9 -22 -9q-7 0 -15 4 q-14 7 -30 29q-6 9 -6 19q0 14 8 22l397 397l-397 398q-10 10 -10 22z" />
<glyph unicode="&#xd8;" horiz-adv-x="1554" d="M113 694q0 150 47.5 281.5t133 227.5t210.5 151t272 55q170 0 322 -78l84 133q8 15 22 15q12 0 21 -6l37 -21q20 -9 20 -27q0 -10 -4 -16l-84 -143q118 -94 184 -244.5t66 -327.5q0 -203 -80.5 -363.5t-233.5 -253.5t-354 -93q-178 0 -321 75l-82 -131q-8 -14 -25 -14 q-8 0 -16 4l-43 25q-17 10 -17 26q0 7 4 15l82 139q-122 99 -183.5 247t-61.5 324zM227 694q0 -301 191 -475q74 120 290.5 473.5t335.5 548.5q-126 66 -268 66q-246 0 -397.5 -170.5t-151.5 -442.5zM510 152q128 -68 266 -68q164 0 290 77.5t193.5 216t67.5 316.5 q0 296 -192 480z" />
<glyph unicode="&#xd9;" horiz-adv-x="1454" d="M197 436v926q0 12 9 21.5t21 9.5h45q12 0 21.5 -9.5t9.5 -21.5v-928q0 -143 125 -246.5t301 -103.5q174 0 298 103.5t124 246.5v928q0 12 9.5 21.5t21.5 9.5h45q12 0 21 -9.5t9 -21.5v-926q0 -187 -155 -319.5t-373 -132.5q-144 0 -266.5 60.5t-194 164.5t-71.5 227z M530 1784q0 9 8 15.5t21 6.5h51q31 0 47 -24q121 -162 144 -191q6 -9 6 -18q0 -8 -6.5 -13.5t-16.5 -5.5h-20q-31 0 -51 23l-172 184q-11 15 -11 23z" />
<glyph unicode="&#xda;" horiz-adv-x="1454" d="M197 436v926q0 12 9 21.5t21 9.5h45q12 0 21.5 -9.5t9.5 -21.5v-928q0 -143 125 -246.5t301 -103.5q174 0 298 103.5t124 246.5v928q0 12 9.5 21.5t21.5 9.5h45q12 0 21 -9.5t9 -21.5v-926q0 -187 -155 -319.5t-373 -132.5q-144 0 -266.5 60.5t-194 164.5t-71.5 227z M627 1587q0 12 6 21l147 190q12 12 25 12h53q13 0 22 -8t9 -20q0 -15 -8 -23q-11 -13 -86.5 -93t-94.5 -99q-8 -8 -22 -8h-21q-13 0 -21.5 8t-8.5 20z" />
<glyph unicode="&#xdb;" horiz-adv-x="1454" d="M197 436v926q0 12 9 21.5t21 9.5h45q12 0 21.5 -9.5t9.5 -21.5v-928q0 -143 125 -246.5t301 -103.5q174 0 298 103.5t124 246.5v928q0 12 9.5 21.5t21.5 9.5h45q12 0 21 -9.5t9 -21.5v-926q0 -187 -155 -319.5t-373 -132.5q-144 0 -266.5 60.5t-194 164.5t-71.5 227z M489 1573q0 8 9 20l145 199q8 12 25 12h82q16 0 24 -12l144 -199q8 -11 8 -22q0 -19 -23 -19h-49q-16 0 -33 17l-112 174q-3 -1 -64 -99l-49 -77q-15 -15 -35 -15h-47q-11 0 -18 6t-7 15z" />
<glyph unicode="&#xdc;" horiz-adv-x="1454" d="M197 436v926q0 12 9 21.5t21 9.5h45q12 0 21.5 -9.5t9.5 -21.5v-928q0 -143 125 -246.5t301 -103.5q174 0 298 103.5t124 246.5v928q0 12 9.5 21.5t21.5 9.5h45q12 0 21 -9.5t9 -21.5v-926q0 -187 -155 -319.5t-373 -132.5q-144 0 -266.5 60.5t-194 164.5t-71.5 227z M438 1667q0 34 25.5 58t60.5 24q34 0 59 -24t25 -58q0 -33 -26 -57.5t-60 -24.5q-36 0 -60 24t-24 58zM821 1667q0 34 25.5 58t60.5 24q34 0 59 -24t25 -58t-26.5 -58t-61.5 -24q-34 0 -58 24t-24 58z" />
<glyph unicode="&#xdd;" horiz-adv-x="1122" d="M70 1364q0 -12 4 -18l432 -717v-598q0 -12 9.5 -21.5t21.5 -9.5h45q12 0 21 9.5t9 21.5v594l432 721q5 8 5 18q0 12 -8.5 20.5t-22.5 8.5h-53q-6 0 -15 -5t-12 -10q-314 -508 -373 -639h-12q-59 131 -373 639q-3 5 -11.5 10t-14.5 5h-54q-30 0 -30 -29zM465 1573 q0 11 6 20l147 191q12 12 25 12h53q13 0 22 -8.5t9 -20.5q0 -14 -8 -22q-13 -14 -87.5 -94.5t-92.5 -98.5q-8 -8 -23 -8h-20q-13 0 -22 8.5t-9 20.5z" />
<glyph unicode="&#xde;" horiz-adv-x="1118" d="M197 31v1331q0 12 9 21.5t21 9.5h45q12 0 21.5 -9.5t9.5 -21.5v-244q86 6 186 6q119 0 211.5 -26.5t149 -68.5t92.5 -101.5t50 -119t14 -126.5q0 -103 -36.5 -184.5t-100 -131.5t-143.5 -76t-171 -26h-252v-233q0 -12 -9.5 -21.5t-21.5 -9.5h-45q-12 0 -21 9.5t-9 21.5z M303 362h264q148 0 237 89t89 235q0 66 -19 123t-60.5 107.5t-118.5 80t-181 29.5q-211 0 -211 -10v-654z" />
<glyph unicode="&#xdf;" horiz-adv-x="1103" d="M158 31v1067q0 82 29.5 144t80 97.5t112.5 52.5t134 17q44 0 93.5 -11t100.5 -36t92 -59.5t67 -87t26 -113.5q0 -34 -17 -66.5t-38.5 -56t-61.5 -51.5t-65 -42.5t-70 -39.5q-72 -40 -109.5 -71.5t-37.5 -61.5q0 -41 64.5 -82t191.5 -90q264 -85 264 -277 q0 -137 -86.5 -208.5t-218.5 -71.5q-149 0 -291 71q-17 10 -17 27q0 4 5 14l22 41q10 17 25 17q4 0 16 -4q43 -24 67.5 -36t67 -23t88.5 -11q93 0 152 44t59 134q0 62 -51.5 113t-142.5 86q-85 32 -137 56t-98.5 55t-67.5 67t-21 80q0 24 13 48t29 41t49.5 39.5t54.5 34.5 t63 35q48 26 75.5 43t61 42.5t49 51t15.5 52.5q0 68 -45.5 120.5t-108 76.5t-127.5 24q-122 0 -188 -51t-66 -174v-1067q0 -12 -9.5 -21.5t-21.5 -9.5h-41q-12 0 -21 9.5t-9 21.5z" />
<glyph unicode="&#xe0;" horiz-adv-x="1056" d="M92 485q0 220 121.5 361t310.5 141q76 0 143 -18t148 -72l8 47q1 10 10.5 18.5t20.5 8.5h16q12 0 21.5 -9.5t9.5 -21.5v-573q0 -143 31 -332q0 -35 -31 -35h-31q-11 0 -20 7.5t-10 17.5l-23 94h-14q-114 -135 -293 -135q-188 0 -303 140t-115 361zM201 485 q0 -182 84 -292.5t229 -110.5q110 0 186 62t99 151v498q-62 47 -135 71.5t-140 24.5q-145 0 -234 -112t-89 -292zM342 1372q0 9 8 16t21 7h51q30 0 47 -25q120 -161 143 -190q6 -9 6 -19q0 -18 -22 -18h-21q-32 0 -51 22l-172 185q-10 13 -10 22z" />
<glyph unicode="&#xe1;" horiz-adv-x="1056" d="M92 485q0 -221 115 -361t303 -140q179 0 293 135h14l23 -94q1 -10 10 -17.5t20 -7.5h31q31 0 31 35q-31 189 -31 332v573q0 12 -9.5 21.5t-21.5 9.5h-16q-11 0 -20.5 -8.5t-10.5 -18.5l-8 -47q-81 54 -148 72t-143 18q-189 0 -310.5 -141t-121.5 -361zM201 485 q0 180 89 292t234 112q67 0 140 -24.5t135 -71.5v-498q-23 -89 -99 -151t-186 -62q-145 0 -229 110.5t-84 292.5zM426 1171q0 12 6 21l148 190q13 13 24 13h53q13 0 22 -8.5t9 -20.5q0 -15 -8 -23q-12 -14 -86.5 -93.5t-93.5 -98.5q-8 -8 -23 -8h-20q-13 0 -22 8t-9 20z" />
<glyph unicode="&#xe2;" horiz-adv-x="1056" d="M92 485q0 -221 115 -361t303 -140q179 0 293 135h14l23 -94q1 -10 10 -17.5t20 -7.5h31q31 0 31 35q-31 189 -31 332v573q0 12 -9.5 21.5t-21.5 9.5h-16q-11 0 -20.5 -8.5t-10.5 -18.5l-8 -47q-81 54 -148 72t-143 18q-189 0 -310.5 -141t-121.5 -361zM201 485 q0 180 89 292t234 112q67 0 140 -24.5t135 -71.5v-498q-23 -89 -99 -151t-186 -62q-145 0 -229 110.5t-84 292.5zM303 1161q0 10 8 21l146 198q9 13 24 13h82q5 0 13.5 -4.5t11.5 -8.5l143 -198q8 -11 8 -23q0 -18 -22 -18h-49q-17 0 -33 16l-113 174q-3 -1 -64 -100 l-48 -76q-14 -14 -35 -14h-47q-25 0 -25 20z" />
<glyph unicode="&#xe3;" horiz-adv-x="1056" d="M92 485q0 -221 115 -361t303 -140q179 0 293 135h14l23 -94q1 -10 10 -17.5t20 -7.5h31q31 0 31 35q-31 189 -31 332v573q0 12 -9.5 21.5t-21.5 9.5h-16q-11 0 -20.5 -8.5t-10.5 -18.5l-8 -47q-81 54 -148 72t-143 18q-189 0 -310.5 -141t-121.5 -361zM201 485 q0 180 89 292t234 112q67 0 140 -24.5t135 -71.5v-498q-23 -89 -99 -151t-186 -62q-145 0 -229 110.5t-84 292.5zM264 1227q11 59 46.5 94t86.5 35q71 0 142 -47q64 -43 106 -43q17 0 32 16.5t23 42.5q8 23 29 23h25q30 0 30 -37q-10 -58 -46.5 -93.5t-86.5 -35.5 q-75 0 -145 45q-68 45 -105 45q-18 0 -32.5 -16.5t-20.5 -43.5q-3 -9 -12.5 -15.5t-18.5 -6.5h-22q-14 0 -22.5 10t-8.5 27z" />
<glyph unicode="&#xe4;" horiz-adv-x="1056" d="M92 485q0 -221 115 -361t303 -140q179 0 293 135h14l23 -94q1 -10 10 -17.5t20 -7.5h31q31 0 31 35q-31 189 -31 332v573q0 12 -9.5 21.5t-21.5 9.5h-16q-11 0 -20.5 -8.5t-10.5 -18.5l-8 -47q-81 54 -148 72t-143 18q-189 0 -310.5 -141t-121.5 -361zM201 485 q0 180 89 292t234 112q67 0 140 -24.5t135 -71.5v-498q-23 -89 -99 -151t-186 -62q-145 0 -229 110.5t-84 292.5zM246 1268q0 34 25.5 58t60.5 24q34 0 59 -24t25 -58q0 -33 -26 -57.5t-60 -24.5q-36 0 -60 24t-24 58zM629 1268q0 34 25.5 58t60.5 24q34 0 59 -24t25 -58 t-26.5 -58t-61.5 -24q-34 0 -58 24t-24 58z" />
<glyph unicode="&#xe5;" horiz-adv-x="1056" d="M92 485q0 220 121.5 361t310.5 141q76 0 143 -18t148 -72l8 47q1 10 10.5 18.5t20.5 8.5h16q12 0 21.5 -9.5t9.5 -21.5v-573q0 -143 31 -332q0 -35 -31 -35h-31q-11 0 -20 7.5t-10 17.5l-23 94h-14q-114 -135 -293 -135q-188 0 -303 140t-115 361zM201 485 q0 -182 84 -292.5t229 -110.5q110 0 186 62t99 151v498q-62 47 -135 71.5t-140 24.5q-145 0 -234 -112t-89 -292zM334 1266q0 72 56 124t134 52t134.5 -52t56.5 -124t-56.5 -123t-134.5 -51t-134 51t-56 123zM406 1266q0 -48 34 -81.5t84 -33.5q48 0 84.5 34t36.5 81 t-36.5 80.5t-84.5 33.5q-51 0 -84.5 -33t-33.5 -81z" />
<glyph unicode="&#xe6;" horiz-adv-x="1687" d="M92 485q0 220 121.5 361t310.5 141q188 0 350 -39q19 -4 19 -26l8 -105h8q48 86 135.5 128t182.5 42q168 0 269 -110t101 -297q0 -59 -2 -88q-2 -12 -10 -20.5t-20 -8.5h-668q0 -171 95.5 -277t238.5 -106q128 0 274 61q12 4 17 4q21 0 24 -22l10 -33q3 -9 3 -16 q0 -16 -17 -25q-159 -65 -311 -65q-112 0 -204.5 50t-145.5 142h-13q-46 -77 -135 -134.5t-209 -57.5q-189 0 -310.5 140.5t-121.5 360.5zM201 485q0 -180 89.5 -292.5t233.5 -112.5q167 0 273 137v643q-122 29 -273 29q-145 0 -234 -112t-89 -292zM901 555h594 q0 165 -72 249.5t-192 84.5q-132 0 -224.5 -88t-105.5 -246z" />
<glyph unicode="&#xe7;" horiz-adv-x="888" d="M92 485q0 221 122.5 361.5t320.5 140.5q137 0 256 -53q20 -7 20 -29q0 -6 -2 -10q-3 -7 -8 -33q-6 -24 -31 -24q-8 0 -12 2q-63 27 -111 38t-112 11q-149 0 -241.5 -110t-92.5 -294q0 -185 92 -294t242 -109q65 0 111 11t112 38q4 2 12 2q25 0 31 -24l12 -37v-4 q0 -20 -18 -31q-116 -53 -260 -53q-198 0 -320.5 140t-122.5 361zM358 -385l9 35q3 14 18 14q1 0 4.5 -1t5.5 -1q31 -10 76 -10q38 0 73.5 25t35.5 61q0 62 -109 76q-29 3 -29 22q0 5 4 17l43 88q6 16 29 16h21q16 0 16 -14q0 -5 -4 -17l-27 -57q65 -9 105.5 -46.5 t40.5 -84.5q0 -78 -61.5 -117t-151.5 -39q-39 0 -82 12q-6 2 -11.5 8t-5.5 13z" />
<glyph unicode="&#xe8;" horiz-adv-x="993" d="M92 485q0 221 121.5 361.5t318.5 140.5q167 0 269 -110.5t102 -296.5q0 -59 -2 -88q-2 -12 -10.5 -20.5t-20.5 -8.5h-669q6 -177 98 -279t236 -102q136 0 274 59q8 4 12 4q23 0 29 -22l12 -37q0 -1 1 -3.5t1 -4.5q0 -25 -18 -31q-60 -26 -145 -44.5t-166 -18.5 q-197 0 -320 140t-123 361zM205 555h592v31q0 142 -72 222.5t-190 80.5q-136 0 -224.5 -89t-105.5 -245zM344 1372q0 9 8 16t21 7h51q30 0 47 -25q120 -161 143 -190q7 -11 7 -19q0 -18 -23 -18h-20q-33 0 -52 22l-172 185q-10 13 -10 22z" />
<glyph unicode="&#xe9;" horiz-adv-x="993" d="M92 485q0 -221 123 -361t320 -140q81 0 166 18.5t145 44.5q18 6 18 31q0 2 -1 4.5t-1 3.5l-12 37q-6 22 -29 22q-4 0 -12 -4q-138 -59 -274 -59q-144 0 -236 102t-98 279h669q12 0 20.5 8.5t10.5 20.5q2 29 2 88q0 186 -102 296.5t-269 110.5q-197 0 -318.5 -140.5 t-121.5 -361.5zM205 555q17 156 105.5 245t224.5 89q118 0 190 -80.5t72 -222.5v-31h-592zM436 1171q0 12 6 21l148 190q13 13 24 13h54q13 0 21.5 -8.5t8.5 -20.5q0 -15 -8 -23q-12 -14 -86.5 -93.5t-93.5 -98.5q-8 -8 -23 -8h-20q-13 0 -22 8t-9 20z" />
<glyph unicode="&#xea;" horiz-adv-x="993" d="M92 485q0 -221 123 -361t320 -140q81 0 166 18.5t145 44.5q18 6 18 31q0 2 -1 4.5t-1 3.5l-12 37q-6 22 -29 22q-4 0 -12 -4q-138 -59 -274 -59q-144 0 -236 102t-98 279h669q12 0 20.5 8.5t10.5 20.5q2 29 2 88q0 186 -102 296.5t-269 110.5q-197 0 -318.5 -140.5 t-121.5 -361.5zM205 555q17 156 105.5 245t224.5 89q118 0 190 -80.5t72 -222.5v-31h-592zM309 1161q0 10 8 21l146 198q9 13 24 13h82q5 0 13.5 -4.5t11.5 -8.5l143 -198q8 -11 8 -23q0 -18 -22 -18h-49q-17 0 -33 16l-113 174q-3 -1 -64 -100l-48 -76q-14 -14 -35 -14h-47 q-25 0 -25 20z" />
<glyph unicode="&#xeb;" horiz-adv-x="993" d="M92 485q0 -221 123 -361t320 -140q81 0 166 18.5t145 44.5q18 6 18 31q0 2 -1 4.5t-1 3.5l-12 37q-6 22 -29 22q-4 0 -12 -4q-138 -59 -274 -59q-144 0 -236 102t-98 279h669q12 0 20.5 8.5t10.5 20.5q2 29 2 88q0 186 -102 296.5t-269 110.5q-197 0 -318.5 -140.5 t-121.5 -361.5zM205 555q17 156 105.5 245t224.5 89q118 0 190 -80.5t72 -222.5v-31h-592zM252 1268q0 34 25.5 58t60.5 24q34 0 59 -24t25 -58q0 -33 -26 -57.5t-60 -24.5q-36 0 -60 24t-24 58zM635 1268q0 34 25.5 58t60.5 24q34 0 59 -24t25 -58t-26.5 -58t-61.5 -24 q-34 0 -58 24t-24 58z" />
<glyph unicode="&#xec;" horiz-adv-x="413" d="M-4 1372q0 9 8 16t21 7h51q30 0 47 -25q120 -161 143 -190q6 -9 6 -19q0 -18 -22 -18h-21q-32 0 -51 22l-172 185q-10 13 -10 22zM156 31v909q0 12 9 21.5t21 9.5h41q12 0 21.5 -9.5t9.5 -21.5v-909q0 -12 -9.5 -21.5t-21.5 -9.5h-41q-12 0 -21 9.5t-9 21.5z" />
<glyph unicode="&#xed;" horiz-adv-x="413" d="M156 31v909q0 12 9 21.5t21 9.5h41q12 0 21.5 -9.5t9.5 -21.5v-909q0 -12 -9.5 -21.5t-21.5 -9.5h-41q-12 0 -21 9.5t-9 21.5zM160 1171q0 12 6 21l147 190q13 13 25 13h53q13 0 22 -8.5t9 -20.5q0 -15 -8 -23q-11 -13 -86.5 -93t-94.5 -99q-8 -8 -22 -8h-21 q-13 0 -21.5 8t-8.5 20z" />
<glyph unicode="&#xee;" horiz-adv-x="413" d="M-6 1161q0 10 8 21l145 198q3 4 11.5 8.5t13.5 4.5h82q5 0 13.5 -4.5t11.5 -8.5l143 -198q8 -11 8 -23q0 -18 -22 -18h-50q-16 0 -32 16l-113 174q-3 -1 -64 -99l-49 -77q-14 -14 -34 -14h-48q-24 0 -24 20zM156 31v909q0 12 9 21.5t21 9.5h41q12 0 21.5 -9.5t9.5 -21.5 v-909q0 -12 -9.5 -21.5t-21.5 -9.5h-41q-12 0 -21 9.5t-9 21.5z" />
<glyph unicode="&#xef;" horiz-adv-x="413" d="M-63 1268q0 34 25.5 58t60.5 24q34 0 58.5 -24t24.5 -58q0 -33 -26 -57.5t-60 -24.5q-36 0 -59.5 24t-23.5 58zM156 31v909q0 12 9 21.5t21 9.5h41q12 0 21.5 -9.5t9.5 -21.5v-909q0 -12 -9.5 -21.5t-21.5 -9.5h-41q-12 0 -21 9.5t-9 21.5zM319 1268q0 34 26 58t61 24 q34 0 58.5 -24t24.5 -58t-26.5 -58t-61.5 -24q-34 0 -58 24t-24 58z" />
<glyph unicode="&#xf0;" horiz-adv-x="1089" d="M90 485q0 212 124 357t310 145q156 0 267 -100l10 6q-73 155 -191 276l-223 -98q-8 -4 -14 -4q-20 0 -27 20l-4 9q-4 12 -4 18q0 17 16 25l197 88q-83 74 -195 137q-20 10 -20 29q0 8 8 16l25 21q8 8 26 8q9 0 13 -2q129 -74 231 -170l223 102q12 4 17 4q14 0 22 -16 l10 -21q2 -4 2 -10q0 -20 -18 -27l-199 -90q281 -291 281 -721q0 -214 -126.5 -358.5t-318.5 -144.5q-127 0 -228.5 66.5t-157.5 181t-56 253.5zM199 485q0 -178 94.5 -291.5t238.5 -113.5q146 0 240 113t94 292q0 184 -88 294t-246 110q-144 0 -238.5 -113.5t-94.5 -290.5z " />
<glyph unicode="&#xf1;" d="M158 31q0 -12 9 -21.5t21 -9.5h41q12 0 21.5 9.5t9.5 21.5v721q114 137 260 137q141 0 196.5 -65t55.5 -185v-608q0 -12 9.5 -21.5t21.5 -9.5h41q12 0 21 9.5t9 21.5v624q0 152 -83 242t-256 90q-69 0 -141.5 -29.5t-129.5 -87.5h-12l-8 72q-2 12 -10.5 20.5t-20.5 8.5 h-25q-12 0 -21 -9.5t-9 -21.5v-909zM264 1227q11 59 46.5 94t86.5 35q71 0 142 -47q64 -43 106 -43q17 0 32 16.5t23 42.5q8 23 29 23h25q30 0 30 -37q-10 -58 -46.5 -93.5t-86.5 -35.5q-75 0 -145 45q-68 45 -105 45q-18 0 -32.5 -16.5t-20.5 -43.5q-3 -9 -12.5 -15.5 t-18.5 -6.5h-22q-14 0 -22.5 10t-8.5 27z" />
<glyph unicode="&#xf2;" horiz-adv-x="1069" d="M92 485q0 221 123 361.5t320 140.5q198 0 321 -140.5t123 -361.5t-123 -361t-321 -140q-197 0 -320 140t-123 361zM201 485q0 -185 92 -294t242 -109t242.5 109t92.5 294q0 184 -93 294t-242 110t-241.5 -110t-92.5 -294zM346 1360q0 9 8 15.5t21 6.5h51q31 0 47 -24 q120 -162 143 -191q7 -11 7 -18q0 -8 -6.5 -13.5t-16.5 -5.5h-20q-32 0 -52 23l-172 184q-10 13 -10 23z" />
<glyph unicode="&#xf3;" horiz-adv-x="1069" d="M92 485q0 221 123 361.5t320 140.5q198 0 321 -140.5t123 -361.5t-123 -361t-321 -140q-197 0 -320 140t-123 361zM201 485q0 -185 92 -294t242 -109t242.5 109t92.5 294q0 184 -93 294t-242 110t-241.5 -110t-92.5 -294zM444 1174q0 10 7 20l147 190q13 13 25 13h53 q13 0 22 -8.5t9 -20.5q0 -13 -9 -22q-13 -14 -87.5 -94.5t-92.5 -98.5q-8 -8 -22 -8h-21q-13 0 -22 8.5t-9 20.5z" />
<glyph unicode="&#xf4;" horiz-adv-x="1069" d="M92 485q0 221 123 361.5t320 140.5q198 0 321 -140.5t123 -361.5t-123 -361t-321 -140q-197 0 -320 140t-123 361zM201 485q0 -185 92 -294t242 -109t242.5 109t92.5 294q0 184 -93 294t-242 110t-241.5 -110t-92.5 -294zM309 1161q0 10 8 21l146 198q9 13 24 13h82 q5 0 13.5 -4.5t11.5 -8.5l143 -198q8 -11 8 -23q0 -18 -22 -18h-49q-17 0 -33 16l-113 174q-3 -1 -64 -100l-48 -76q-14 -14 -35 -14h-47q-25 0 -25 20z" />
<glyph unicode="&#xf5;" horiz-adv-x="1069" d="M92 485q0 221 123 361.5t320 140.5q198 0 321 -140.5t123 -361.5t-123 -361t-321 -140q-197 0 -320 140t-123 361zM201 485q0 -185 92 -294t242 -109t242.5 109t92.5 294q0 184 -93 294t-242 110t-241.5 -110t-92.5 -294zM268 1227q11 59 46.5 94t86.5 35q71 0 142 -47 q64 -43 106 -43q17 0 33 16.5t23 42.5q8 23 28 23h25q30 0 30 -37q-10 -58 -46.5 -93.5t-86.5 -35.5q-75 0 -145 45q-68 45 -104 45q-18 0 -33 -16.5t-21 -43.5q-3 -9 -12 -15.5t-18 -6.5h-23q-14 0 -22.5 10t-8.5 27z" />
<glyph unicode="&#xf6;" horiz-adv-x="1069" d="M92 485q0 221 123 361.5t320 140.5q198 0 321 -140.5t123 -361.5t-123 -361t-321 -140q-197 0 -320 140t-123 361zM201 485q0 -185 92 -294t242 -109t242.5 109t92.5 294q0 184 -93 294t-242 110t-241.5 -110t-92.5 -294zM252 1268q0 34 25.5 58t60.5 24q34 0 59 -24 t25 -58q0 -33 -26 -57.5t-60 -24.5q-36 0 -60 24t-24 58zM635 1268q0 34 25.5 58t60.5 24q34 0 59 -24t25 -58t-26.5 -58t-61.5 -24q-34 0 -58 24t-24 58z" />
<glyph unicode="&#xf7;" horiz-adv-x="1196" d="M113 469v33q0 12 9 21t21 9h910q12 0 21 -9t9 -21v-33q0 -12 -9 -21.5t-21 -9.5h-910q-12 0 -21 9.5t-9 21.5zM518 139q0 36 22 58t58 22q37 0 58.5 -21.5t21.5 -58.5q0 -35 -21.5 -56.5t-58.5 -21.5q-35 0 -57.5 21.5t-22.5 56.5zM518 829q0 35 22.5 57.5t57.5 22.5 q36 0 58 -22t22 -58q0 -35 -21.5 -56t-58.5 -21q-36 0 -58 21t-22 56z" />
<glyph unicode="&#xf8;" horiz-adv-x="1069" d="M92 487q0 221 123 361.5t320 140.5q115 0 210 -55l70 115q10 16 27 16q6 0 14 -4l39 -21q16 -10 16 -26q0 -11 -4 -17l-73 -125q69 -69 107 -169t38 -216q0 -221 -123 -362t-321 -141q-111 0 -218 55l-69 -117q-9 -12 -27 -12q-8 0 -12 2l-39 20q-16 10 -16 27q0 4 4 16 l78 127q-144 150 -144 385zM201 487q0 -173 92 -292l401 657q-74 39 -159 39q-149 0 -241.5 -110t-92.5 -294zM375 127q83 -47 160 -47q149 0 241 110.5t92 296.5q0 174 -94 293q-75 -124 -210 -344t-189 -309z" />
<glyph unicode="&#xf9;" d="M158 313v627q0 12 9 21.5t21 9.5h41q12 0 21.5 -9.5t9.5 -21.5v-610q0 -120 55.5 -184t196.5 -64q145 0 260 137v721q0 12 9.5 21.5t21.5 9.5h41q12 0 21 -9.5t9 -21.5v-909q0 -12 -9 -21.5t-21 -9.5h-25q-12 0 -20.5 8.5t-10.5 20.5l-8 69h-12q-57 -55 -128.5 -84.5 t-141.5 -29.5q-173 0 -256.5 89.5t-83.5 239.5zM303 1372q0 9 8 16t21 7h51q30 0 47 -25q120 -161 143 -190q7 -11 7 -19q0 -18 -23 -18h-20q-33 0 -52 22l-172 185q-10 13 -10 22z" />
<glyph unicode="&#xfa;" d="M158 313q0 -150 83.5 -239.5t256.5 -89.5q70 0 141.5 29.5t128.5 84.5h12l8 -69q2 -12 10.5 -20.5t20.5 -8.5h25q12 0 21 9.5t9 21.5v909q0 12 -9 21.5t-21 9.5h-41q-12 0 -21.5 -9.5t-9.5 -21.5v-721q-115 -137 -260 -137q-141 0 -196.5 64t-55.5 184v610 q0 12 -9.5 21.5t-21.5 9.5h-41q-12 0 -21 -9.5t-9 -21.5v-627zM395 1178q0 11 6 20l148 191q12 12 24 12h54q13 0 21.5 -8.5t8.5 -20.5q0 -14 -8 -22q-13 -14 -87.5 -94.5t-92.5 -98.5q-8 -8 -23 -8h-20q-13 0 -22 8.5t-9 20.5z" />
<glyph unicode="&#xfb;" d="M158 313q0 -150 83.5 -239.5t256.5 -89.5q70 0 141.5 29.5t128.5 84.5h12l8 -69q2 -12 10.5 -20.5t20.5 -8.5h25q12 0 21 9.5t9 21.5v909q0 12 -9 21.5t-21 9.5h-41q-12 0 -21.5 -9.5t-9.5 -21.5v-721q-115 -137 -260 -137q-141 0 -196.5 64t-55.5 184v610 q0 12 -9.5 21.5t-21.5 9.5h-41q-12 0 -21 -9.5t-9 -21.5v-627zM270 1161q0 9 9 21l145 198q3 4 11.5 8.5t13.5 4.5h81q5 0 13.5 -4.5t11.5 -8.5l143 -198q9 -12 9 -23q0 -18 -23 -18h-49q-17 0 -33 16l-113 174q-3 -1 -64 -100l-48 -76q-14 -14 -35 -14h-47q-25 0 -25 20z " />
<glyph unicode="&#xfc;" d="M158 313q0 -150 83.5 -239.5t256.5 -89.5q70 0 141.5 29.5t128.5 84.5h12l8 -69q2 -12 10.5 -20.5t20.5 -8.5h25q12 0 21 9.5t9 21.5v909q0 12 -9 21.5t-21 9.5h-41q-12 0 -21.5 -9.5t-9.5 -21.5v-721q-115 -137 -260 -137q-141 0 -196.5 64t-55.5 184v610 q0 12 -9.5 21.5t-21.5 9.5h-41q-12 0 -21 -9.5t-9 -21.5v-627zM215 1268q0 34 25.5 58t60.5 24q34 0 59 -24t25 -58q0 -33 -26 -57.5t-60 -24.5q-36 0 -60 24t-24 58zM598 1268q0 34 25.5 58t60.5 24q34 0 59 -24t25 -58t-26.5 -58t-61.5 -24q-34 0 -58 24t-24 58z" />
<glyph unicode="&#xfd;" horiz-adv-x="927" d="M51 -375l10 -33q7 -24 31 -24q6 0 10 2q286 89 369 315l393 1045q2 4 2 12q0 12 -8.5 20.5t-21.5 8.5h-45q-7 0 -16.5 -6.5t-12.5 -14.5q-245 -645 -285 -794h-10q-57 203 -287 794q-3 8 -12 14.5t-16 6.5h-48q-13 0 -21.5 -8.5t-8.5 -20.5q0 -8 2 -12l338 -912 q-6 -19 -19 -48t-18 -44q-41 -101 -114 -164.5t-191 -101.5q-21 -7 -21 -35zM381 1171q0 12 6 21l148 190q13 13 24 13h53q13 0 22 -8.5t9 -20.5q0 -15 -8 -23q-12 -14 -86.5 -93.5t-93.5 -98.5q-8 -8 -23 -8h-20q-13 0 -22 8t-9 20z" />
<glyph unicode="&#xfe;" horiz-adv-x="1058" d="M158 -391v1753q0 12 9 21.5t21 9.5h41q12 0 21.5 -9.5t9.5 -21.5v-485h8q118 110 267 110q189 0 310.5 -141t121.5 -361t-121.5 -360.5t-310.5 -140.5q-68 0 -132.5 15.5t-142.5 66.5v-457q0 -12 -9.5 -21.5t-21.5 -9.5h-41q-12 0 -21 9.5t-9 21.5zM260 178 q137 -98 275 -98q145 0 234 112t89 293q0 180 -89 292t-234 112q-169 0 -275 -137v-574z" />
<glyph unicode="&#xff;" horiz-adv-x="927" d="M51 -375l10 -33q7 -24 31 -24q6 0 10 2q286 89 369 315l393 1045q2 4 2 12q0 12 -8.5 20.5t-21.5 8.5h-45q-7 0 -16.5 -6.5t-12.5 -14.5q-245 -645 -285 -794h-10q-57 203 -287 794q-3 8 -12 14.5t-16 6.5h-48q-13 0 -21.5 -8.5t-8.5 -20.5q0 -8 2 -12l338 -912 q-6 -19 -19 -48t-18 -44q-41 -101 -114 -164.5t-191 -101.5q-21 -7 -21 -35zM188 1268q0 34 25.5 58t60.5 24q34 0 59 -24t25 -58q0 -33 -26 -57.5t-60 -24.5q-36 0 -60 24t-24 58zM571 1268q0 34 25.5 58t60.5 24q34 0 59 -24t25 -58t-26.5 -58t-61.5 -24q-34 0 -58 24 t-24 58z" />
<glyph unicode="&#x152;" horiz-adv-x="2160" d="M113 694q0 150 47.5 281.5t133 227.5t210.5 151t272 55q188 0 330.5 -83.5t220.5 -233.5v270q0 12 9.5 21.5t21.5 9.5h653q12 0 21.5 -9.5t9.5 -21.5v-35q0 -12 -9.5 -21.5t-21.5 -9.5h-577v-524h514q12 0 21 -9.5t9 -21.5v-34q0 -12 -9 -21.5t-21 -9.5h-514v-580h591 q12 0 21.5 -9t9.5 -21v-35q0 -12 -9.5 -21.5t-21.5 -9.5h-667q-12 0 -21.5 9.5t-9.5 21.5v262q-1 0 -3 1t-3 1q-80 -148 -218.5 -229.5t-326.5 -81.5q-200 0 -352 93t-231.5 253.5t-79.5 363.5zM227 694q0 -270 151.5 -440t397.5 -170q164 0 290 77.5t193.5 216t67.5 316.5 q0 272 -151 442.5t-400 170.5q-246 0 -397.5 -170.5t-151.5 -442.5z" />
<glyph unicode="&#x153;" horiz-adv-x="1771" d="M92 485q0 221 123 361.5t320 140.5q134 0 231.5 -70.5t148.5 -201.5h9q49 131 150 201.5t235 70.5q169 0 270.5 -110t101.5 -297q0 -30 -4 -76v-10q0 -12 -9 -21.5t-21 -9.5h-666q0 -174 94 -278.5t236 -104.5q128 0 278 61q4 2 13 2q24 0 30 -22q7 -30 8 -31 q5 -10 5 -14q0 -20 -19 -29q-137 -63 -315 -63q-133 0 -237 73.5t-150 198.5h-9q-52 -130 -149 -201t-231 -71q-197 0 -320 140t-123 361zM201 485q0 -186 92.5 -295.5t241.5 -109.5t241 109.5t92 295.5q0 184 -92 294t-241 110t-241.5 -110t-92.5 -294zM983 555h594 q-1 160 -72.5 247t-193.5 87q-129 0 -219.5 -86t-108.5 -248z" />
<glyph unicode="&#x178;" horiz-adv-x="1122" d="M70 1364q0 -12 4 -18l432 -717v-598q0 -12 9.5 -21.5t21.5 -9.5h45q12 0 21 9.5t9 21.5v594l432 721q5 8 5 18q0 12 -8.5 20.5t-22.5 8.5h-53q-6 0 -15 -5t-12 -10q-314 -508 -373 -639h-12q-59 131 -373 639q-3 5 -11.5 10t-14.5 5h-54q-30 0 -30 -29zM272 1675 q0 34 25.5 58t60.5 24q34 0 59 -24t25 -58q0 -33 -26 -57.5t-60 -24.5q-36 0 -60 24t-24 58zM655 1675q0 34 25.5 58t60.5 24q34 0 59 -24t25 -58t-26.5 -58t-61.5 -24q-34 0 -58 24t-24 58z" />
<glyph unicode="&#x2c6;" horiz-adv-x="679" d="M121 1161q0 10 8 21l145 198q3 4 11.5 8.5t13.5 4.5h82q5 0 13.5 -4.5t11.5 -8.5l143 -198q8 -11 8 -23q0 -18 -22 -18h-50q-16 0 -32 16l-113 174q-3 -1 -64 -99l-49 -77q-14 -14 -34 -14h-48q-24 0 -24 20z" />
<glyph unicode="&#x2dc;" horiz-adv-x="671" d="M76 1227q11 59 46.5 94t86.5 35q70 0 141 -47q64 -43 107 -43q17 0 32 16.5t23 42.5q8 23 29 23h24q14 0 22.5 -10t8.5 -27q-10 -58 -46.5 -93.5t-86.5 -35.5q-76 0 -146 45q-68 45 -104 45q-18 0 -32.5 -16.5t-20.5 -43.5q-3 -9 -12.5 -15.5t-18.5 -6.5h-23 q-30 0 -30 37z" />
<glyph unicode="&#x2000;" horiz-adv-x="926" />
<glyph unicode="&#x2001;" horiz-adv-x="1853" />
<glyph unicode="&#x2002;" horiz-adv-x="926" />
<glyph unicode="&#x2003;" horiz-adv-x="1853" />
<glyph unicode="&#x2004;" horiz-adv-x="617" />
<glyph unicode="&#x2005;" horiz-adv-x="463" />
<glyph unicode="&#x2006;" horiz-adv-x="308" />
<glyph unicode="&#x2007;" horiz-adv-x="308" />
<glyph unicode="&#x2008;" horiz-adv-x="231" />
<glyph unicode="&#x2009;" horiz-adv-x="370" />
<glyph unicode="&#x200a;" horiz-adv-x="102" />
<glyph unicode="&#x2010;" horiz-adv-x="831" d="M115 455v39q0 12 9 21t21 9h543q12 0 21.5 -9t9.5 -21v-39q0 -12 -9.5 -21.5t-21.5 -9.5h-543q-12 0 -21 9.5t-9 21.5z" />
<glyph unicode="&#x2011;" horiz-adv-x="831" d="M115 455v39q0 12 9 21t21 9h543q12 0 21.5 -9t9.5 -21v-39q0 -12 -9.5 -21.5t-21.5 -9.5h-543q-12 0 -21 9.5t-9 21.5z" />
<glyph unicode="&#x2012;" horiz-adv-x="831" d="M115 455v39q0 12 9 21t21 9h543q12 0 21.5 -9t9.5 -21v-39q0 -12 -9.5 -21.5t-21.5 -9.5h-543q-12 0 -21 9.5t-9 21.5z" />
<glyph unicode="&#x2013;" horiz-adv-x="1083" d="M55 469v31q0 12 9.5 21t21.5 9h911q12 0 21.5 -9t9.5 -21v-31q0 -12 -9.5 -21.5t-21.5 -9.5h-911q-12 0 -21.5 9.5t-9.5 21.5z" />
<glyph unicode="&#x2014;" horiz-adv-x="1908" d="M55 469v31q0 12 9.5 21t21.5 9h1737q12 0 21 -9t9 -21v-31q0 -12 -9 -21.5t-21 -9.5h-1737q-12 0 -21.5 9.5t-9.5 21.5z" />
<glyph unicode="&#x2018;" horiz-adv-x="432" d="M74 985q0 147 180 401q10 13 25 13q6 0 14 -4l24 -13q17 -10 17 -24q0 -5 -4 -17q-103 -184 -103 -356q0 -32 -22 -53t-55 -21q-31 0 -53.5 21t-22.5 53z" />
<glyph unicode="&#x2019;" horiz-adv-x="421" d="M111 963q0 4 4 16q104 186 104 356q0 32 22.5 53t53.5 21q33 0 55.5 -21t22.5 -53q0 -147 -180 -401q-9 -12 -25 -12q-8 0 -12 2l-29 14q-16 10 -16 25z" />
<glyph unicode="&#x201a;" horiz-adv-x="432" d="M98 -301q0 4 4 16q105 188 105 357q0 32 22.5 52.5t53.5 20.5q33 0 55 -20.5t22 -52.5q0 -148 -180 -402q-9 -12 -24 -12q-9 0 -13 2l-28 14q-17 10 -17 25z" />
<glyph unicode="&#x201c;" horiz-adv-x="718" d="M51 985q0 147 180 401q10 13 25 13q6 0 14 -4l25 -13q16 -10 16 -24q0 -5 -4 -17q-102 -183 -102 -356q0 -32 -22.5 -53t-55.5 -21q-31 0 -53.5 21t-22.5 53zM356 985q0 146 181 401q10 13 24 13q8 0 12 -2l29 -15q16 -10 16 -24q0 -5 -4 -17q-104 -186 -104 -356 q0 -32 -22.5 -53t-53.5 -21q-33 0 -55.5 21t-22.5 53z" />
<glyph unicode="&#x201d;" horiz-adv-x="718" d="M98 963q0 4 4 16q105 188 105 356q0 32 22.5 53t53.5 21q33 0 55 -21t22 -53q0 -147 -180 -401q-9 -12 -24 -12q-9 0 -13 2l-28 14q-17 10 -17 25zM406 963q0 4 4 16q102 183 102 356q0 32 22.5 53t55.5 21q31 0 53.5 -21t22.5 -53q0 -146 -181 -401q-9 -12 -24 -12 q-7 0 -15 4l-24 12q-16 10 -16 25z" />
<glyph unicode="&#x201e;" horiz-adv-x="718" d="M98 -301q0 4 4 16q105 188 105 357q0 32 22.5 52.5t53.5 20.5q33 0 55 -20.5t22 -52.5q0 -148 -180 -402q-9 -12 -24 -12q-9 0 -13 2l-28 14q-17 10 -17 25zM406 -301q0 4 4 16q102 183 102 357q0 32 22.5 52.5t55.5 20.5q31 0 53.5 -20.5t22.5 -52.5q0 -147 -181 -402 q-9 -12 -24 -12q-7 0 -15 4l-24 12q-16 10 -16 25z" />
<glyph unicode="&#x2022;" horiz-adv-x="696" d="M121 696q0 94 65.5 162t157.5 68q96 0 163.5 -68t67.5 -162q0 -96 -67.5 -163.5t-163.5 -67.5q-92 0 -157.5 68t-65.5 163z" />
<glyph unicode="&#x2026;" horiz-adv-x="1599" d="M199 66q0 37 23 58t63 21q39 0 62.5 -21.5t23.5 -57.5q0 -39 -23.5 -60.5t-62.5 -21.5q-40 0 -63 21.5t-23 60.5zM715 66q0 37 23 58t63 21q39 0 62.5 -21.5t23.5 -57.5q0 -39 -23.5 -60.5t-62.5 -21.5q-40 0 -63 21.5t-23 60.5zM1231 66q0 37 23 58t63 21 q39 0 62.5 -21.5t23.5 -57.5q0 -39 -23.5 -60.5t-62.5 -21.5q-40 0 -63 21.5t-23 60.5z" />
<glyph unicode="&#x202f;" horiz-adv-x="370" />
<glyph unicode="&#x2039;" horiz-adv-x="520" d="M61 485q0 7 5 17l274 454q3 5 12 10t15 5h43q13 0 22.5 -8.5t9.5 -20.5q0 -9 -6 -18l-266 -439l266 -438q6 -9 6 -18q0 -12 -9.5 -20.5t-22.5 -8.5h-43q-6 0 -15 4.5t-12 9.5l-274 455q-5 10 -5 16z" />
<glyph unicode="&#x203a;" horiz-adv-x="520" d="M76 29q0 9 6 18l270 438l-268 439q-6 9 -6 18q0 12 9.5 20.5t23.5 8.5h41q18 0 26 -15q61 -105 277 -454q2 -4 2 -17q0 -12 -2 -16l-275 -455q-3 -5 -11.5 -9.5t-14.5 -4.5h-45q-14 0 -23.5 8.5t-9.5 20.5z" />
<glyph unicode="&#x205f;" horiz-adv-x="463" />
<glyph unicode="&#x20ac;" horiz-adv-x="1171" d="M90 532v31q0 12 9.5 21.5t21.5 9.5h69q-4 70 -4 102q0 38 7 103h-72q-12 0 -21.5 9t-9.5 21v29q0 12 9.5 21.5t21.5 9.5h84q25 132 81.5 232.5t138 163t181.5 93.5t217 31q131 0 252 -43q9 -3 15 -12.5t6 -18.5q0 -6 -2 -10l-13 -35q-7 -18 -28 -18q-7 0 -11 2 q-107 37 -219 37q-186 0 -319.5 -105.5t-177.5 -316.5h589q12 0 21.5 -9.5t9.5 -21.5v-29q0 -12 -9.5 -21t-21.5 -9h-606q-4 -66 -4 -103q0 -36 4 -102h606q12 0 21.5 -9.5t9.5 -21.5v-31q0 -12 -9.5 -21t-21.5 -9h-589q43 -209 176 -314.5t321 -105.5q112 0 219 37 q4 2 11 2q21 0 28 -21l13 -32q2 -4 2 -11q0 -23 -21 -30q-121 -43 -252 -43q-240 0 -404.5 129.5t-213.5 388.5h-84q-12 0 -21.5 9t-9.5 21z" />
<glyph unicode="&#x2122;" horiz-adv-x="1214" d="M47 1350v22q0 8 6 14.5t15 6.5h397q7 0 13.5 -6.5t6.5 -14.5v-22q0 -9 -6 -15t-14 -6h-158v-495q0 -9 -6 -15t-14 -6h-39q-9 0 -15 6t-6 15v495h-159q-9 0 -15 6t-6 15zM518 834l17 538q0 8 5.5 14.5t14.5 6.5h47q18 0 31 -17l170 -254q119 183 178 271h68q7 0 13.5 -6.5 t6.5 -14.5l18 -538q0 -9 -6 -15t-14 -6h-29q-18 0 -22 21q-19 360 -23 444l-166 -256q-9 -12 -24 -12q-16 0 -25 12l-168 250l-12 -438q0 -9 -6 -15t-14 -6h-39q-9 0 -15 6t-6 15z" />
<glyph unicode="&#x25fc;" horiz-adv-x="972" d="M0 0v973h973v-973h-973z" />
<glyph unicode="&#xfb01;" horiz-adv-x="921" d="M70 909v31q0 12 9 21.5t21 9.5h70v180q0 126 71 192t197 66q119 0 219 -35q21 -7 21 -35q-6 -28 -12 -41q-6 -18 -31 -18q-2 0 -4.5 1t-3.5 1q-102 31 -172 31q-91 0 -137 -43t-46 -119v-180h463q12 0 21.5 -9.5t9.5 -21.5v-909q0 -12 -9.5 -21.5t-21.5 -9.5h-45 q-12 0 -21.5 9.5t-9.5 21.5v848h-387v-848q0 -12 -9 -21.5t-21 -9.5h-41q-12 0 -21.5 9.5t-9.5 21.5v848h-70q-12 0 -21 9t-9 21z" />
<glyph unicode="&#xfb02;" horiz-adv-x="1067" d="M70 909v31q0 12 9 21.5t21 9.5h70v180q0 123 78 190.5t209 67.5q157 0 291 -66q6 -3 12 -12t6 -16v-1071q0 -80 33.5 -123t113.5 -43q42 0 91 12q29 0 36 -22l11 -33q0 -30 -25 -37q-66 -14 -123 -14q-117 0 -180.5 61t-63.5 186v1045q-112 37 -194 37q-93 0 -143 -43.5 t-50 -118.5v-180h197q12 0 21.5 -9.5t9.5 -21.5v-31q0 -12 -9.5 -21t-21.5 -9h-197v-848q0 -12 -9 -21.5t-21 -9.5h-41q-12 0 -21.5 9.5t-9.5 21.5v848h-70q-12 0 -21 9t-9 21z" />
<hkern u1="&#x20;" u2="&#xc5;" k="10" />
<hkern u1="&#x20;" u2="&#xc4;" k="10" />
<hkern u1="&#x20;" u2="&#xc3;" k="10" />
<hkern u1="&#x20;" u2="&#xc2;" k="10" />
<hkern u1="&#x20;" u2="&#xc1;" k="10" />
<hkern u1="&#x20;" u2="&#xc0;" k="10" />
<hkern u1="&#x20;" u2="A" k="10" />
<hkern u1="&#x20;" u2="T" k="137" />
<hkern u1="&#x22;" u2="x" k="31" />
<hkern u1="&#x27;" u2="x" k="31" />
<hkern u1="A" u2="w" k="39" />
<hkern u1="A" u2="t" k="10" />
<hkern u1="A" u2="l" k="10" />
<hkern u1="A" u2="j" k="-37" />
<hkern u1="A" u2="f" k="16" />
<hkern u1="A" u2="Y" k="166" />
<hkern u1="A" u2="W" k="96" />
<hkern u1="A" u2="V" k="143" />
<hkern u1="A" u2="T" k="184" />
<hkern u1="A" u2="D" k="39" />
<hkern u1="B" u2="&#x201e;" k="10" />
<hkern u1="B" u2="&#x201a;" k="10" />
<hkern u1="B" u2="&#x2e;" k="10" />
<hkern u1="B" u2="&#x2c;" k="10" />
<hkern u1="B" u2="Y" k="109" />
<hkern u1="B" u2="X" k="10" />
<hkern u1="B" u2="W" k="31" />
<hkern u1="B" u2="V" k="57" />
<hkern u1="B" u2="T" k="109" />
<hkern u1="D" u2="Z" k="57" />
<hkern u1="D" u2="Y" k="117" />
<hkern u1="D" u2="X" k="70" />
<hkern u1="D" u2="V" k="39" />
<hkern u1="D" u2="T" k="117" />
<hkern u1="D" u2="J" k="70" />
<hkern u1="F" u2="&#x201e;" k="184" />
<hkern u1="F" u2="&#x201a;" k="184" />
<hkern u1="F" u2="&#x153;" k="70" />
<hkern u1="F" u2="&#x152;" k="31" />
<hkern u1="F" u2="&#xfc;" k="49" />
<hkern u1="F" u2="&#xfb;" k="49" />
<hkern u1="F" u2="&#xfa;" k="49" />
<hkern u1="F" u2="&#xf9;" k="49" />
<hkern u1="F" u2="&#xf8;" k="70" />
<hkern u1="F" u2="&#xf6;" k="70" />
<hkern u1="F" u2="&#xf5;" k="70" />
<hkern u1="F" u2="&#xf4;" k="70" />
<hkern u1="F" u2="&#xf3;" k="70" />
<hkern u1="F" u2="&#xf2;" k="70" />
<hkern u1="F" u2="&#xf0;" k="70" />
<hkern u1="F" u2="&#xeb;" k="70" />
<hkern u1="F" u2="&#xea;" k="70" />
<hkern u1="F" u2="&#xe9;" k="70" />
<hkern u1="F" u2="&#xe8;" k="70" />
<hkern u1="F" u2="&#xe7;" k="70" />
<hkern u1="F" u2="&#xe6;" k="70" />
<hkern u1="F" u2="&#xe5;" k="70" />
<hkern u1="F" u2="&#xe4;" k="70" />
<hkern u1="F" u2="&#xe3;" k="70" />
<hkern u1="F" u2="&#xe2;" k="70" />
<hkern u1="F" u2="&#xe1;" k="70" />
<hkern u1="F" u2="&#xe0;" k="70" />
<hkern u1="F" u2="&#xd8;" k="31" />
<hkern u1="F" u2="&#xd6;" k="31" />
<hkern u1="F" u2="&#xd5;" k="31" />
<hkern u1="F" u2="&#xd4;" k="31" />
<hkern u1="F" u2="&#xd3;" k="31" />
<hkern u1="F" u2="&#xd2;" k="31" />
<hkern u1="F" u2="&#xc7;" k="31" />
<hkern u1="F" u2="&#xc5;" k="135" />
<hkern u1="F" u2="&#xc4;" k="135" />
<hkern u1="F" u2="&#xc3;" k="135" />
<hkern u1="F" u2="&#xc2;" k="135" />
<hkern u1="F" u2="&#xc1;" k="135" />
<hkern u1="F" u2="&#xc0;" k="135" />
<hkern u1="F" u2="u" k="49" />
<hkern u1="F" u2="q" k="70" />
<hkern u1="F" u2="o" k="70" />
<hkern u1="F" u2="g" k="70" />
<hkern u1="F" u2="e" k="70" />
<hkern u1="F" u2="d" k="70" />
<hkern u1="F" u2="c" k="70" />
<hkern u1="F" u2="a" k="70" />
<hkern u1="F" u2="Q" k="31" />
<hkern u1="F" u2="O" k="31" />
<hkern u1="F" u2="G" k="31" />
<hkern u1="F" u2="C" k="31" />
<hkern u1="F" u2="A" k="135" />
<hkern u1="F" u2="&#x2e;" k="184" />
<hkern u1="F" u2="&#x2c;" k="184" />
<hkern u1="F" u2="&#xf1;" k="10" />
<hkern u1="F" u2="r" k="49" />
<hkern u1="F" u2="n" k="14" />
<hkern u1="F" u2="m" k="14" />
<hkern u1="F" u2="J" k="31" />
<hkern u1="G" u2="Y" k="88" />
<hkern u1="G" u2="W" k="49" />
<hkern u1="G" u2="V" k="78" />
<hkern u1="G" u2="T" k="47" />
<hkern u1="H" u2="&#xef;" k="-84" />
<hkern u1="J" u2="&#x201e;" k="49" />
<hkern u1="J" u2="&#x201a;" k="49" />
<hkern u1="J" u2="&#xc5;" k="49" />
<hkern u1="J" u2="&#xc4;" k="49" />
<hkern u1="J" u2="&#xc3;" k="49" />
<hkern u1="J" u2="&#xc2;" k="49" />
<hkern u1="J" u2="&#xc1;" k="49" />
<hkern u1="J" u2="&#xc0;" k="49" />
<hkern u1="J" u2="A" k="49" />
<hkern u1="J" u2="&#x2e;" k="49" />
<hkern u1="J" u2="&#x2c;" k="49" />
<hkern u1="K" u2="&#x153;" k="49" />
<hkern u1="K" u2="&#x152;" k="113" />
<hkern u1="K" u2="&#xff;" k="80" />
<hkern u1="K" u2="&#xfd;" k="80" />
<hkern u1="K" u2="&#xfc;" k="39" />
<hkern u1="K" u2="&#xfb;" k="39" />
<hkern u1="K" u2="&#xfa;" k="39" />
<hkern u1="K" u2="&#xf9;" k="39" />
<hkern u1="K" u2="&#xf8;" k="49" />
<hkern u1="K" u2="&#xf6;" k="49" />
<hkern u1="K" u2="&#xf5;" k="49" />
<hkern u1="K" u2="&#xf4;" k="49" />
<hkern u1="K" u2="&#xf3;" k="49" />
<hkern u1="K" u2="&#xf2;" k="49" />
<hkern u1="K" u2="&#xf0;" k="49" />
<hkern u1="K" u2="&#xeb;" k="49" />
<hkern u1="K" u2="&#xea;" k="49" />
<hkern u1="K" u2="&#xe9;" k="49" />
<hkern u1="K" u2="&#xe8;" k="49" />
<hkern u1="K" u2="&#xe7;" k="49" />
<hkern u1="K" u2="&#xe6;" k="49" />
<hkern u1="K" u2="&#xe5;" k="49" />
<hkern u1="K" u2="&#xe4;" k="49" />
<hkern u1="K" u2="&#xe3;" k="49" />
<hkern u1="K" u2="&#xe2;" k="49" />
<hkern u1="K" u2="&#xe1;" k="49" />
<hkern u1="K" u2="&#xe0;" k="49" />
<hkern u1="K" u2="&#xd8;" k="113" />
<hkern u1="K" u2="&#xd6;" k="113" />
<hkern u1="K" u2="&#xd5;" k="113" />
<hkern u1="K" u2="&#xd4;" k="113" />
<hkern u1="K" u2="&#xd3;" k="113" />
<hkern u1="K" u2="&#xd2;" k="113" />
<hkern u1="K" u2="&#xc7;" k="113" />
<hkern u1="K" u2="y" k="80" />
<hkern u1="K" u2="v" k="80" />
<hkern u1="K" u2="u" k="39" />
<hkern u1="K" u2="q" k="49" />
<hkern u1="K" u2="o" k="49" />
<hkern u1="K" u2="l" k="49" />
<hkern u1="K" u2="k" k="49" />
<hkern u1="K" u2="h" k="49" />
<hkern u1="K" u2="g" k="49" />
<hkern u1="K" u2="e" k="49" />
<hkern u1="K" u2="d" k="49" />
<hkern u1="K" u2="c" k="49" />
<hkern u1="K" u2="b" k="49" />
<hkern u1="K" u2="a" k="49" />
<hkern u1="K" u2="U" k="49" />
<hkern u1="K" u2="R" k="49" />
<hkern u1="K" u2="Q" k="113" />
<hkern u1="K" u2="P" k="49" />
<hkern u1="K" u2="O" k="113" />
<hkern u1="K" u2="N" k="49" />
<hkern u1="K" u2="M" k="49" />
<hkern u1="K" u2="L" k="49" />
<hkern u1="K" u2="K" k="49" />
<hkern u1="K" u2="H" k="49" />
<hkern u1="K" u2="G" k="113" />
<hkern u1="K" u2="F" k="49" />
<hkern u1="K" u2="E" k="49" />
<hkern u1="K" u2="C" k="113" />
<hkern u1="K" u2="B" k="49" />
<hkern u1="K" u2="w" k="88" />
<hkern u1="K" u2="Y" k="127" />
<hkern u1="K" u2="W" k="96" />
<hkern u1="K" u2="V" k="109" />
<hkern u1="K" u2="T" k="88" />
<hkern u1="K" u2="D" k="51" />
<hkern u1="L" u2="&#x201d;" k="109" />
<hkern u1="L" u2="&#x2019;" k="109" />
<hkern u1="L" u2="&#x153;" k="96" />
<hkern u1="L" u2="&#x152;" k="127" />
<hkern u1="L" u2="&#xfc;" k="70" />
<hkern u1="L" u2="&#xfb;" k="70" />
<hkern u1="L" u2="&#xfa;" k="70" />
<hkern u1="L" u2="&#xf9;" k="70" />
<hkern u1="L" u2="&#xf8;" k="96" />
<hkern u1="L" u2="&#xf6;" k="96" />
<hkern u1="L" u2="&#xf5;" k="96" />
<hkern u1="L" u2="&#xf4;" k="96" />
<hkern u1="L" u2="&#xf3;" k="96" />
<hkern u1="L" u2="&#xf2;" k="96" />
<hkern u1="L" u2="&#xf0;" k="96" />
<hkern u1="L" u2="&#xeb;" k="96" />
<hkern u1="L" u2="&#xea;" k="96" />
<hkern u1="L" u2="&#xe9;" k="96" />
<hkern u1="L" u2="&#xe8;" k="96" />
<hkern u1="L" u2="&#xe7;" k="96" />
<hkern u1="L" u2="&#xe6;" k="96" />
<hkern u1="L" u2="&#xe5;" k="96" />
<hkern u1="L" u2="&#xe4;" k="96" />
<hkern u1="L" u2="&#xe3;" k="96" />
<hkern u1="L" u2="&#xe2;" k="96" />
<hkern u1="L" u2="&#xe1;" k="96" />
<hkern u1="L" u2="&#xe0;" k="96" />
<hkern u1="L" u2="&#xdc;" k="88" />
<hkern u1="L" u2="&#xdb;" k="88" />
<hkern u1="L" u2="&#xda;" k="88" />
<hkern u1="L" u2="&#xd9;" k="88" />
<hkern u1="L" u2="&#xd8;" k="127" />
<hkern u1="L" u2="&#xd6;" k="127" />
<hkern u1="L" u2="&#xd5;" k="127" />
<hkern u1="L" u2="&#xd4;" k="127" />
<hkern u1="L" u2="&#xd3;" k="127" />
<hkern u1="L" u2="&#xd2;" k="127" />
<hkern u1="L" u2="&#xc7;" k="127" />
<hkern u1="L" u2="u" k="70" />
<hkern u1="L" u2="q" k="96" />
<hkern u1="L" u2="o" k="96" />
<hkern u1="L" u2="g" k="96" />
<hkern u1="L" u2="e" k="96" />
<hkern u1="L" u2="d" k="96" />
<hkern u1="L" u2="c" k="96" />
<hkern u1="L" u2="a" k="96" />
<hkern u1="L" u2="U" k="88" />
<hkern u1="L" u2="Q" k="127" />
<hkern u1="L" u2="O" k="127" />
<hkern u1="L" u2="G" k="127" />
<hkern u1="L" u2="C" k="127" />
<hkern u1="L" u2="&#x27;" k="172" />
<hkern u1="L" u2="&#x22;" k="172" />
<hkern u1="L" u2="Y" k="213" />
<hkern u1="L" u2="W" k="135" />
<hkern u1="L" u2="V" k="205" />
<hkern u1="L" u2="T" k="188" />
<hkern u1="L" u2="D" k="10" />
<hkern u1="M" u2="&#xef;" k="-84" />
<hkern u1="M" u2="Y" k="31" />
<hkern u1="M" u2="W" k="10" />
<hkern u1="M" u2="V" k="31" />
<hkern u1="M" u2="T" k="10" />
<hkern u1="N" u2="&#xef;" k="-84" />
<hkern u1="O" u2="&#xcf;" k="57" />
<hkern u1="O" u2="&#xce;" k="57" />
<hkern u1="O" u2="&#xcd;" k="57" />
<hkern u1="O" u2="&#xcc;" k="57" />
<hkern u1="O" u2="I" k="57" />
<hkern u1="O" u2="Z" k="57" />
<hkern u1="O" u2="Y" k="117" />
<hkern u1="O" u2="X" k="70" />
<hkern u1="O" u2="V" k="39" />
<hkern u1="O" u2="T" k="117" />
<hkern u1="O" u2="J" k="70" />
<hkern u1="P" u2="&#x201e;" k="223" />
<hkern u1="P" u2="&#x201a;" k="223" />
<hkern u1="P" u2="&#x153;" k="49" />
<hkern u1="P" u2="&#xf8;" k="49" />
<hkern u1="P" u2="&#xf6;" k="49" />
<hkern u1="P" u2="&#xf5;" k="49" />
<hkern u1="P" u2="&#xf4;" k="49" />
<hkern u1="P" u2="&#xf3;" k="49" />
<hkern u1="P" u2="&#xf2;" k="49" />
<hkern u1="P" u2="&#xf0;" k="49" />
<hkern u1="P" u2="&#xeb;" k="49" />
<hkern u1="P" u2="&#xea;" k="49" />
<hkern u1="P" u2="&#xe9;" k="49" />
<hkern u1="P" u2="&#xe8;" k="49" />
<hkern u1="P" u2="&#xe7;" k="49" />
<hkern u1="P" u2="&#xe6;" k="49" />
<hkern u1="P" u2="&#xe5;" k="49" />
<hkern u1="P" u2="&#xe4;" k="49" />
<hkern u1="P" u2="&#xe3;" k="49" />
<hkern u1="P" u2="&#xe2;" k="49" />
<hkern u1="P" u2="&#xe1;" k="49" />
<hkern u1="P" u2="&#xe0;" k="49" />
<hkern u1="P" u2="&#xc5;" k="154" />
<hkern u1="P" u2="&#xc4;" k="154" />
<hkern u1="P" u2="&#xc3;" k="154" />
<hkern u1="P" u2="&#xc2;" k="154" />
<hkern u1="P" u2="&#xc1;" k="154" />
<hkern u1="P" u2="&#xc0;" k="154" />
<hkern u1="P" u2="q" k="49" />
<hkern u1="P" u2="o" k="49" />
<hkern u1="P" u2="g" k="49" />
<hkern u1="P" u2="e" k="49" />
<hkern u1="P" u2="d" k="49" />
<hkern u1="P" u2="c" k="49" />
<hkern u1="P" u2="a" k="49" />
<hkern u1="P" u2="A" k="154" />
<hkern u1="P" u2="&#x2e;" k="223" />
<hkern u1="P" u2="&#x2c;" k="223" />
<hkern u1="P" u2="Z" k="49" />
<hkern u1="P" u2="Y" k="49" />
<hkern u1="P" u2="X" k="70" />
<hkern u1="P" u2="M" k="10" />
<hkern u1="P" u2="J" k="109" />
<hkern u1="Q" u2="Z" k="57" />
<hkern u1="Q" u2="Y" k="117" />
<hkern u1="Q" u2="X" k="70" />
<hkern u1="Q" u2="V" k="39" />
<hkern u1="Q" u2="T" k="117" />
<hkern u1="Q" u2="J" k="70" />
<hkern u1="R" u2="&#x153;" k="18" />
<hkern u1="R" u2="&#x152;" k="49" />
<hkern u1="R" u2="&#xff;" k="49" />
<hkern u1="R" u2="&#xfd;" k="49" />
<hkern u1="R" u2="&#xfc;" k="10" />
<hkern u1="R" u2="&#xfb;" k="10" />
<hkern u1="R" u2="&#xfa;" k="10" />
<hkern u1="R" u2="&#xf9;" k="10" />
<hkern u1="R" u2="&#xf8;" k="18" />
<hkern u1="R" u2="&#xf6;" k="18" />
<hkern u1="R" u2="&#xf5;" k="18" />
<hkern u1="R" u2="&#xf4;" k="18" />
<hkern u1="R" u2="&#xf3;" k="18" />
<hkern u1="R" u2="&#xf2;" k="18" />
<hkern u1="R" u2="&#xf0;" k="18" />
<hkern u1="R" u2="&#xeb;" k="18" />
<hkern u1="R" u2="&#xea;" k="18" />
<hkern u1="R" u2="&#xe9;" k="18" />
<hkern u1="R" u2="&#xe8;" k="18" />
<hkern u1="R" u2="&#xe7;" k="18" />
<hkern u1="R" u2="&#xe6;" k="18" />
<hkern u1="R" u2="&#xe5;" k="18" />
<hkern u1="R" u2="&#xe4;" k="18" />
<hkern u1="R" u2="&#xe3;" k="18" />
<hkern u1="R" u2="&#xe2;" k="18" />
<hkern u1="R" u2="&#xe1;" k="18" />
<hkern u1="R" u2="&#xe0;" k="18" />
<hkern u1="R" u2="&#xdc;" k="49" />
<hkern u1="R" u2="&#xdb;" k="49" />
<hkern u1="R" u2="&#xda;" k="49" />
<hkern u1="R" u2="&#xd9;" k="49" />
<hkern u1="R" u2="&#xd8;" k="49" />
<hkern u1="R" u2="&#xd6;" k="49" />
<hkern u1="R" u2="&#xd5;" k="49" />
<hkern u1="R" u2="&#xd4;" k="49" />
<hkern u1="R" u2="&#xd3;" k="49" />
<hkern u1="R" u2="&#xd2;" k="49" />
<hkern u1="R" u2="&#xc7;" k="49" />
<hkern u1="R" u2="y" k="49" />
<hkern u1="R" u2="v" k="49" />
<hkern u1="R" u2="u" k="10" />
<hkern u1="R" u2="q" k="18" />
<hkern u1="R" u2="o" k="18" />
<hkern u1="R" u2="g" k="18" />
<hkern u1="R" u2="e" k="18" />
<hkern u1="R" u2="d" k="18" />
<hkern u1="R" u2="c" k="18" />
<hkern u1="R" u2="a" k="18" />
<hkern u1="R" u2="U" k="49" />
<hkern u1="R" u2="Q" k="49" />
<hkern u1="R" u2="O" k="49" />
<hkern u1="R" u2="G" k="49" />
<hkern u1="R" u2="C" k="49" />
<hkern u1="R" u2="w" k="31" />
<hkern u1="R" u2="t" k="31" />
<hkern u1="R" u2="Y" k="109" />
<hkern u1="R" u2="W" k="57" />
<hkern u1="R" u2="V" k="78" />
<hkern u1="R" u2="T" k="109" />
<hkern u1="S" u2="w" k="31" />
<hkern u1="S" u2="t" k="10" />
<hkern u1="S" u2="Y" k="88" />
<hkern u1="S" u2="W" k="10" />
<hkern u1="S" u2="V" k="49" />
<hkern u1="T" u2="&#x201e;" k="184" />
<hkern u1="T" u2="&#x201a;" k="184" />
<hkern u1="T" u2="&#x153;" k="209" />
<hkern u1="T" u2="&#x152;" k="113" />
<hkern u1="T" u2="&#xff;" k="111" />
<hkern u1="T" u2="&#xfd;" k="111" />
<hkern u1="T" u2="&#xfc;" k="143" />
<hkern u1="T" u2="&#xfb;" k="143" />
<hkern u1="T" u2="&#xfa;" k="143" />
<hkern u1="T" u2="&#xf9;" k="143" />
<hkern u1="T" u2="&#xf8;" k="209" />
<hkern u1="T" u2="&#xf6;" k="209" />
<hkern u1="T" u2="&#xf5;" k="209" />
<hkern u1="T" u2="&#xf4;" k="209" />
<hkern u1="T" u2="&#xf3;" k="209" />
<hkern u1="T" u2="&#xf2;" k="209" />
<hkern u1="T" u2="&#xf0;" k="209" />
<hkern u1="T" u2="&#xeb;" k="209" />
<hkern u1="T" u2="&#xea;" k="209" />
<hkern u1="T" u2="&#xe9;" k="209" />
<hkern u1="T" u2="&#xe8;" k="209" />
<hkern u1="T" u2="&#xe7;" k="209" />
<hkern u1="T" u2="&#xe6;" k="209" />
<hkern u1="T" u2="&#xe5;" k="209" />
<hkern u1="T" u2="&#xe4;" k="209" />
<hkern u1="T" u2="&#xe3;" k="209" />
<hkern u1="T" u2="&#xe2;" k="209" />
<hkern u1="T" u2="&#xe1;" k="209" />
<hkern u1="T" u2="&#xe0;" k="209" />
<hkern u1="T" u2="&#xd8;" k="113" />
<hkern u1="T" u2="&#xd6;" k="113" />
<hkern u1="T" u2="&#xd5;" k="113" />
<hkern u1="T" u2="&#xd4;" k="113" />
<hkern u1="T" u2="&#xd3;" k="113" />
<hkern u1="T" u2="&#xd2;" k="113" />
<hkern u1="T" u2="&#xc7;" k="113" />
<hkern u1="T" u2="&#xc5;" k="182" />
<hkern u1="T" u2="&#xc4;" k="182" />
<hkern u1="T" u2="&#xc3;" k="182" />
<hkern u1="T" u2="&#xc2;" k="182" />
<hkern u1="T" u2="&#xc1;" k="182" />
<hkern u1="T" u2="&#xc0;" k="182" />
<hkern u1="T" u2="y" k="111" />
<hkern u1="T" u2="v" k="111" />
<hkern u1="T" u2="u" k="143" />
<hkern u1="T" u2="s" k="109" />
<hkern u1="T" u2="q" k="209" />
<hkern u1="T" u2="o" k="209" />
<hkern u1="T" u2="g" k="209" />
<hkern u1="T" u2="e" k="209" />
<hkern u1="T" u2="d" k="209" />
<hkern u1="T" u2="c" k="209" />
<hkern u1="T" u2="a" k="209" />
<hkern u1="T" u2="Q" k="113" />
<hkern u1="T" u2="O" k="113" />
<hkern u1="T" u2="G" k="113" />
<hkern u1="T" u2="C" k="113" />
<hkern u1="T" u2="A" k="182" />
<hkern u1="T" u2="&#x3b;" k="143" />
<hkern u1="T" u2="&#x3a;" k="143" />
<hkern u1="T" u2="&#x2e;" k="184" />
<hkern u1="T" u2="&#x2c;" k="184" />
<hkern u1="T" u2="x" k="166" />
<hkern u1="T" u2="w" k="143" />
<hkern u1="T" u2="r" k="143" />
<hkern u1="T" u2="X" k="39" />
<hkern u1="T" u2="T" k="49" />
<hkern u1="T" u2="M" k="10" />
<hkern u1="T" u2="J" k="49" />
<hkern u1="T" u2="D" k="61" />
<hkern u1="T" u2="&#x2d;" k="205" />
<hkern u1="U" u2="&#xef;" k="-84" />
<hkern u1="U" u2="Z" k="31" />
<hkern u1="U" u2="X" k="31" />
<hkern u1="U" u2="J" k="39" />
<hkern u1="V" u2="&#x201e;" k="184" />
<hkern u1="V" u2="&#x201a;" k="184" />
<hkern u1="V" u2="&#x153;" k="109" />
<hkern u1="V" u2="&#x152;" k="39" />
<hkern u1="V" u2="&#xfc;" k="49" />
<hkern u1="V" u2="&#xfb;" k="49" />
<hkern u1="V" u2="&#xfa;" k="49" />
<hkern u1="V" u2="&#xf9;" k="49" />
<hkern u1="V" u2="&#xf8;" k="109" />
<hkern u1="V" u2="&#xf6;" k="109" />
<hkern u1="V" u2="&#xf5;" k="109" />
<hkern u1="V" u2="&#xf4;" k="109" />
<hkern u1="V" u2="&#xf3;" k="109" />
<hkern u1="V" u2="&#xf2;" k="109" />
<hkern u1="V" u2="&#xf0;" k="109" />
<hkern u1="V" u2="&#xeb;" k="109" />
<hkern u1="V" u2="&#xea;" k="109" />
<hkern u1="V" u2="&#xe9;" k="109" />
<hkern u1="V" u2="&#xe8;" k="109" />
<hkern u1="V" u2="&#xe7;" k="109" />
<hkern u1="V" u2="&#xe6;" k="109" />
<hkern u1="V" u2="&#xe5;" k="109" />
<hkern u1="V" u2="&#xe4;" k="109" />
<hkern u1="V" u2="&#xe3;" k="109" />
<hkern u1="V" u2="&#xe2;" k="109" />
<hkern u1="V" u2="&#xe1;" k="109" />
<hkern u1="V" u2="&#xe0;" k="109" />
<hkern u1="V" u2="&#xd8;" k="39" />
<hkern u1="V" u2="&#xd6;" k="39" />
<hkern u1="V" u2="&#xd5;" k="39" />
<hkern u1="V" u2="&#xd4;" k="39" />
<hkern u1="V" u2="&#xd3;" k="39" />
<hkern u1="V" u2="&#xd2;" k="39" />
<hkern u1="V" u2="&#xc7;" k="39" />
<hkern u1="V" u2="&#xc5;" k="143" />
<hkern u1="V" u2="&#xc4;" k="143" />
<hkern u1="V" u2="&#xc3;" k="143" />
<hkern u1="V" u2="&#xc2;" k="143" />
<hkern u1="V" u2="&#xc1;" k="143" />
<hkern u1="V" u2="&#xc0;" k="143" />
<hkern u1="V" u2="u" k="49" />
<hkern u1="V" u2="q" k="109" />
<hkern u1="V" u2="o" k="109" />
<hkern u1="V" u2="g" k="109" />
<hkern u1="V" u2="e" k="109" />
<hkern u1="V" u2="d" k="109" />
<hkern u1="V" u2="c" k="109" />
<hkern u1="V" u2="a" k="109" />
<hkern u1="V" u2="Q" k="39" />
<hkern u1="V" u2="O" k="39" />
<hkern u1="V" u2="C" k="39" />
<hkern u1="V" u2="A" k="143" />
<hkern u1="V" u2="&#x3b;" k="49" />
<hkern u1="V" u2="&#x3a;" k="49" />
<hkern u1="V" u2="&#x2e;" k="184" />
<hkern u1="V" u2="&#x2c;" k="184" />
<hkern u1="V" u2="r" k="49" />
<hkern u1="V" u2="X" k="31" />
<hkern u1="V" u2="M" k="31" />
<hkern u1="V" u2="J" k="70" />
<hkern u1="V" u2="G" k="49" />
<hkern u1="V" u2="&#x2d;" k="174" />
<hkern u1="W" u2="&#x201e;" k="184" />
<hkern u1="W" u2="&#x201a;" k="184" />
<hkern u1="W" u2="&#x153;" k="49" />
<hkern u1="W" u2="&#x152;" k="18" />
<hkern u1="W" u2="&#xfc;" k="31" />
<hkern u1="W" u2="&#xfb;" k="31" />
<hkern u1="W" u2="&#xfa;" k="31" />
<hkern u1="W" u2="&#xf9;" k="31" />
<hkern u1="W" u2="&#xf8;" k="49" />
<hkern u1="W" u2="&#xf6;" k="49" />
<hkern u1="W" u2="&#xf5;" k="49" />
<hkern u1="W" u2="&#xf4;" k="49" />
<hkern u1="W" u2="&#xf3;" k="49" />
<hkern u1="W" u2="&#xf2;" k="49" />
<hkern u1="W" u2="&#xf0;" k="49" />
<hkern u1="W" u2="&#xeb;" k="49" />
<hkern u1="W" u2="&#xea;" k="49" />
<hkern u1="W" u2="&#xe9;" k="49" />
<hkern u1="W" u2="&#xe8;" k="49" />
<hkern u1="W" u2="&#xe7;" k="49" />
<hkern u1="W" u2="&#xe6;" k="49" />
<hkern u1="W" u2="&#xe5;" k="49" />
<hkern u1="W" u2="&#xe4;" k="49" />
<hkern u1="W" u2="&#xe3;" k="49" />
<hkern u1="W" u2="&#xe2;" k="49" />
<hkern u1="W" u2="&#xe1;" k="49" />
<hkern u1="W" u2="&#xe0;" k="49" />
<hkern u1="W" u2="&#xd8;" k="18" />
<hkern u1="W" u2="&#xd6;" k="18" />
<hkern u1="W" u2="&#xd5;" k="18" />
<hkern u1="W" u2="&#xd4;" k="18" />
<hkern u1="W" u2="&#xd3;" k="18" />
<hkern u1="W" u2="&#xd2;" k="18" />
<hkern u1="W" u2="&#xc7;" k="18" />
<hkern u1="W" u2="&#xc5;" k="109" />
<hkern u1="W" u2="&#xc4;" k="109" />
<hkern u1="W" u2="&#xc3;" k="109" />
<hkern u1="W" u2="&#xc2;" k="109" />
<hkern u1="W" u2="&#xc1;" k="109" />
<hkern u1="W" u2="&#xc0;" k="109" />
<hkern u1="W" u2="u" k="31" />
<hkern u1="W" u2="q" k="49" />
<hkern u1="W" u2="o" k="49" />
<hkern u1="W" u2="g" k="49" />
<hkern u1="W" u2="e" k="49" />
<hkern u1="W" u2="d" k="49" />
<hkern u1="W" u2="c" k="49" />
<hkern u1="W" u2="a" k="49" />
<hkern u1="W" u2="Q" k="18" />
<hkern u1="W" u2="O" k="18" />
<hkern u1="W" u2="G" k="18" />
<hkern u1="W" u2="C" k="18" />
<hkern u1="W" u2="A" k="109" />
<hkern u1="W" u2="&#x3b;" k="49" />
<hkern u1="W" u2="&#x3a;" k="49" />
<hkern u1="W" u2="&#x2e;" k="184" />
<hkern u1="W" u2="&#x2c;" k="184" />
<hkern u1="W" u2="r" k="31" />
<hkern u1="W" u2="X" k="10" />
<hkern u1="W" u2="M" k="10" />
<hkern u1="W" u2="J" k="49" />
<hkern u1="W" u2="D" k="39" />
<hkern u1="W" u2="&#x2d;" k="88" />
<hkern u1="X" u2="&#x153;" k="31" />
<hkern u1="X" u2="&#x152;" k="49" />
<hkern u1="X" u2="&#xfc;" k="49" />
<hkern u1="X" u2="&#xfb;" k="49" />
<hkern u1="X" u2="&#xfa;" k="49" />
<hkern u1="X" u2="&#xf9;" k="49" />
<hkern u1="X" u2="&#xf8;" k="31" />
<hkern u1="X" u2="&#xf6;" k="31" />
<hkern u1="X" u2="&#xf5;" k="31" />
<hkern u1="X" u2="&#xf4;" k="31" />
<hkern u1="X" u2="&#xf3;" k="31" />
<hkern u1="X" u2="&#xf2;" k="31" />
<hkern u1="X" u2="&#xf0;" k="31" />
<hkern u1="X" u2="&#xeb;" k="31" />
<hkern u1="X" u2="&#xea;" k="31" />
<hkern u1="X" u2="&#xe9;" k="31" />
<hkern u1="X" u2="&#xe8;" k="31" />
<hkern u1="X" u2="&#xe7;" k="31" />
<hkern u1="X" u2="&#xe6;" k="31" />
<hkern u1="X" u2="&#xe5;" k="31" />
<hkern u1="X" u2="&#xe4;" k="31" />
<hkern u1="X" u2="&#xe3;" k="31" />
<hkern u1="X" u2="&#xe2;" k="31" />
<hkern u1="X" u2="&#xe1;" k="31" />
<hkern u1="X" u2="&#xe0;" k="31" />
<hkern u1="X" u2="&#xdc;" k="31" />
<hkern u1="X" u2="&#xdb;" k="31" />
<hkern u1="X" u2="&#xda;" k="31" />
<hkern u1="X" u2="&#xd9;" k="31" />
<hkern u1="X" u2="&#xd8;" k="49" />
<hkern u1="X" u2="&#xd6;" k="49" />
<hkern u1="X" u2="&#xd5;" k="49" />
<hkern u1="X" u2="&#xd4;" k="49" />
<hkern u1="X" u2="&#xd3;" k="49" />
<hkern u1="X" u2="&#xd2;" k="49" />
<hkern u1="X" u2="&#xc7;" k="49" />
<hkern u1="X" u2="u" k="49" />
<hkern u1="X" u2="q" k="31" />
<hkern u1="X" u2="o" k="31" />
<hkern u1="X" u2="g" k="31" />
<hkern u1="X" u2="e" k="31" />
<hkern u1="X" u2="d" k="31" />
<hkern u1="X" u2="c" k="31" />
<hkern u1="X" u2="a" k="31" />
<hkern u1="X" u2="U" k="31" />
<hkern u1="X" u2="Q" k="49" />
<hkern u1="X" u2="O" k="49" />
<hkern u1="X" u2="G" k="49" />
<hkern u1="X" u2="C" k="49" />
<hkern u1="X" u2="Y" k="31" />
<hkern u1="X" u2="W" k="10" />
<hkern u1="X" u2="V" k="31" />
<hkern u1="X" u2="T" k="49" />
<hkern u1="Y" u2="&#x201e;" k="223" />
<hkern u1="Y" u2="&#x201a;" k="223" />
<hkern u1="Y" u2="&#x153;" k="174" />
<hkern u1="Y" u2="&#x152;" k="88" />
<hkern u1="Y" u2="&#xfc;" k="109" />
<hkern u1="Y" u2="&#xfb;" k="109" />
<hkern u1="Y" u2="&#xfa;" k="109" />
<hkern u1="Y" u2="&#xf9;" k="109" />
<hkern u1="Y" u2="&#xf8;" k="174" />
<hkern u1="Y" u2="&#xf6;" k="174" />
<hkern u1="Y" u2="&#xf5;" k="174" />
<hkern u1="Y" u2="&#xf4;" k="174" />
<hkern u1="Y" u2="&#xf3;" k="174" />
<hkern u1="Y" u2="&#xf2;" k="174" />
<hkern u1="Y" u2="&#xf0;" k="174" />
<hkern u1="Y" u2="&#xeb;" k="174" />
<hkern u1="Y" u2="&#xea;" k="174" />
<hkern u1="Y" u2="&#xe9;" k="174" />
<hkern u1="Y" u2="&#xe8;" k="174" />
<hkern u1="Y" u2="&#xe7;" k="174" />
<hkern u1="Y" u2="&#xe6;" k="174" />
<hkern u1="Y" u2="&#xe5;" k="174" />
<hkern u1="Y" u2="&#xe4;" k="174" />
<hkern u1="Y" u2="&#xe3;" k="174" />
<hkern u1="Y" u2="&#xe2;" k="174" />
<hkern u1="Y" u2="&#xe1;" k="174" />
<hkern u1="Y" u2="&#xe0;" k="174" />
<hkern u1="Y" u2="&#xd8;" k="88" />
<hkern u1="Y" u2="&#xd6;" k="88" />
<hkern u1="Y" u2="&#xd5;" k="88" />
<hkern u1="Y" u2="&#xd4;" k="88" />
<hkern u1="Y" u2="&#xd3;" k="88" />
<hkern u1="Y" u2="&#xd2;" k="88" />
<hkern u1="Y" u2="&#xc7;" k="88" />
<hkern u1="Y" u2="&#xc5;" k="184" />
<hkern u1="Y" u2="&#xc4;" k="184" />
<hkern u1="Y" u2="&#xc3;" k="184" />
<hkern u1="Y" u2="&#xc2;" k="184" />
<hkern u1="Y" u2="&#xc1;" k="184" />
<hkern u1="Y" u2="&#xc0;" k="184" />
<hkern u1="Y" u2="u" k="109" />
<hkern u1="Y" u2="q" k="174" />
<hkern u1="Y" u2="o" k="174" />
<hkern u1="Y" u2="g" k="174" />
<hkern u1="Y" u2="e" k="174" />
<hkern u1="Y" u2="d" k="174" />
<hkern u1="Y" u2="c" k="174" />
<hkern u1="Y" u2="a" k="174" />
<hkern u1="Y" u2="S" k="31" />
<hkern u1="Y" u2="Q" k="88" />
<hkern u1="Y" u2="O" k="88" />
<hkern u1="Y" u2="G" k="88" />
<hkern u1="Y" u2="C" k="88" />
<hkern u1="Y" u2="A" k="184" />
<hkern u1="Y" u2="&#x3b;" k="70" />
<hkern u1="Y" u2="&#x3a;" k="70" />
<hkern u1="Y" u2="&#x2e;" k="223" />
<hkern u1="Y" u2="&#x2c;" k="223" />
<hkern u1="Y" u2="Z" k="70" />
<hkern u1="Y" u2="X" k="31" />
<hkern u1="Y" u2="M" k="31" />
<hkern u1="Y" u2="J" k="49" />
<hkern u1="Y" u2="D" k="109" />
<hkern u1="Y" u2="&#x2d;" k="166" />
<hkern u1="Z" u2="&#x153;" k="57" />
<hkern u1="Z" u2="&#x152;" k="78" />
<hkern u1="Z" u2="&#xff;" k="49" />
<hkern u1="Z" u2="&#xfd;" k="49" />
<hkern u1="Z" u2="&#xfc;" k="57" />
<hkern u1="Z" u2="&#xfb;" k="57" />
<hkern u1="Z" u2="&#xfa;" k="57" />
<hkern u1="Z" u2="&#xf9;" k="57" />
<hkern u1="Z" u2="&#xf8;" k="57" />
<hkern u1="Z" u2="&#xf6;" k="57" />
<hkern u1="Z" u2="&#xf5;" k="57" />
<hkern u1="Z" u2="&#xf4;" k="57" />
<hkern u1="Z" u2="&#xf3;" k="57" />
<hkern u1="Z" u2="&#xf2;" k="57" />
<hkern u1="Z" u2="&#xf0;" k="57" />
<hkern u1="Z" u2="&#xeb;" k="57" />
<hkern u1="Z" u2="&#xea;" k="57" />
<hkern u1="Z" u2="&#xe9;" k="57" />
<hkern u1="Z" u2="&#xe8;" k="57" />
<hkern u1="Z" u2="&#xe7;" k="57" />
<hkern u1="Z" u2="&#xe6;" k="57" />
<hkern u1="Z" u2="&#xe5;" k="57" />
<hkern u1="Z" u2="&#xe4;" k="57" />
<hkern u1="Z" u2="&#xe3;" k="57" />
<hkern u1="Z" u2="&#xe2;" k="57" />
<hkern u1="Z" u2="&#xe1;" k="57" />
<hkern u1="Z" u2="&#xe0;" k="57" />
<hkern u1="Z" u2="&#xd8;" k="78" />
<hkern u1="Z" u2="&#xd6;" k="78" />
<hkern u1="Z" u2="&#xd5;" k="78" />
<hkern u1="Z" u2="&#xd4;" k="78" />
<hkern u1="Z" u2="&#xd3;" k="78" />
<hkern u1="Z" u2="&#xd2;" k="78" />
<hkern u1="Z" u2="&#xc7;" k="78" />
<hkern u1="Z" u2="y" k="49" />
<hkern u1="Z" u2="v" k="49" />
<hkern u1="Z" u2="u" k="57" />
<hkern u1="Z" u2="q" k="57" />
<hkern u1="Z" u2="o" k="57" />
<hkern u1="Z" u2="g" k="57" />
<hkern u1="Z" u2="e" k="57" />
<hkern u1="Z" u2="d" k="57" />
<hkern u1="Z" u2="c" k="57" />
<hkern u1="Z" u2="a" k="57" />
<hkern u1="Z" u2="Q" k="78" />
<hkern u1="Z" u2="O" k="78" />
<hkern u1="Z" u2="G" k="78" />
<hkern u1="Z" u2="C" k="78" />
<hkern u1="Z" u2="Y" k="49" />
<hkern u1="b" u2="x" k="41" />
<hkern u1="b" u2="w" k="10" />
<hkern u1="b" u2="t" k="4" />
<hkern u1="b" u2="f" k="10" />
<hkern u1="d" u2="&#xef;" k="-84" />
<hkern u1="e" u2="x" k="31" />
<hkern u1="e" u2="w" k="4" />
<hkern u1="f" u2="&#x201e;" k="70" />
<hkern u1="f" u2="&#x201d;" k="-131" />
<hkern u1="f" u2="&#x201a;" k="70" />
<hkern u1="f" u2="&#x2019;" k="-131" />
<hkern u1="f" u2="&#x153;" k="61" />
<hkern u1="f" u2="&#xfc;" k="25" />
<hkern u1="f" u2="&#xfb;" k="25" />
<hkern u1="f" u2="&#xfa;" k="25" />
<hkern u1="f" u2="&#xf9;" k="25" />
<hkern u1="f" u2="&#xf8;" k="61" />
<hkern u1="f" u2="&#xf6;" k="61" />
<hkern u1="f" u2="&#xf5;" k="61" />
<hkern u1="f" u2="&#xf4;" k="61" />
<hkern u1="f" u2="&#xf3;" k="61" />
<hkern u1="f" u2="&#xf2;" k="61" />
<hkern u1="f" u2="&#xf0;" k="61" />
<hkern u1="f" u2="&#xeb;" k="61" />
<hkern u1="f" u2="&#xea;" k="61" />
<hkern u1="f" u2="&#xe9;" k="61" />
<hkern u1="f" u2="&#xe8;" k="61" />
<hkern u1="f" u2="&#xe7;" k="61" />
<hkern u1="f" u2="&#xe6;" k="61" />
<hkern u1="f" u2="&#xe5;" k="61" />
<hkern u1="f" u2="&#xe4;" k="61" />
<hkern u1="f" u2="&#xe3;" k="61" />
<hkern u1="f" u2="&#xe2;" k="61" />
<hkern u1="f" u2="&#xe1;" k="61" />
<hkern u1="f" u2="&#xe0;" k="61" />
<hkern u1="f" u2="u" k="25" />
<hkern u1="f" u2="s" k="18" />
<hkern u1="f" u2="q" k="61" />
<hkern u1="f" u2="o" k="61" />
<hkern u1="f" u2="g" k="61" />
<hkern u1="f" u2="e" k="61" />
<hkern u1="f" u2="d" k="61" />
<hkern u1="f" u2="c" k="61" />
<hkern u1="f" u2="a" k="61" />
<hkern u1="f" u2="&#x2e;" k="70" />
<hkern u1="f" u2="&#x2c;" k="70" />
<hkern u1="f" u2="&#x27;" k="-106" />
<hkern u1="f" u2="&#x22;" k="-106" />
<hkern u1="f" u2="x" k="18" />
<hkern u1="f" u2="r" k="25" />
<hkern u1="f" u2="p" k="25" />
<hkern u1="f" u2="n" k="25" />
<hkern u1="f" u2="m" k="25" />
<hkern u1="f" u2="&#x3f;" k="-156" />
<hkern u1="g" u2="j" k="-47" />
<hkern u1="h" u2="w" k="10" />
<hkern u1="h" u2="t" k="10" />
<hkern u1="k" u2="&#x153;" k="57" />
<hkern u1="k" u2="&#xfc;" k="57" />
<hkern u1="k" u2="&#xfb;" k="57" />
<hkern u1="k" u2="&#xfa;" k="57" />
<hkern u1="k" u2="&#xf9;" k="57" />
<hkern u1="k" u2="&#xf8;" k="57" />
<hkern u1="k" u2="&#xf6;" k="57" />
<hkern u1="k" u2="&#xf5;" k="57" />
<hkern u1="k" u2="&#xf4;" k="57" />
<hkern u1="k" u2="&#xf3;" k="57" />
<hkern u1="k" u2="&#xf2;" k="57" />
<hkern u1="k" u2="&#xf0;" k="57" />
<hkern u1="k" u2="&#xeb;" k="57" />
<hkern u1="k" u2="&#xea;" k="57" />
<hkern u1="k" u2="&#xe9;" k="57" />
<hkern u1="k" u2="&#xe8;" k="57" />
<hkern u1="k" u2="&#xe7;" k="57" />
<hkern u1="k" u2="&#xe6;" k="57" />
<hkern u1="k" u2="&#xe5;" k="57" />
<hkern u1="k" u2="&#xe4;" k="57" />
<hkern u1="k" u2="&#xe3;" k="57" />
<hkern u1="k" u2="&#xe2;" k="57" />
<hkern u1="k" u2="&#xe1;" k="57" />
<hkern u1="k" u2="&#xe0;" k="57" />
<hkern u1="k" u2="u" k="57" />
<hkern u1="k" u2="q" k="57" />
<hkern u1="k" u2="o" k="57" />
<hkern u1="k" u2="g" k="57" />
<hkern u1="k" u2="e" k="57" />
<hkern u1="k" u2="d" k="57" />
<hkern u1="k" u2="c" k="57" />
<hkern u1="k" u2="a" k="57" />
<hkern u1="k" u2="w" k="10" />
<hkern u1="k" u2="l" k="41" />
<hkern u1="l" u2="w" k="43" />
<hkern u1="l" u2="l" k="57" />
<hkern u1="l" u2="f" k="31" />
<hkern u1="m" u2="w" k="10" />
<hkern u1="m" u2="t" k="10" />
<hkern u1="n" u2="w" k="10" />
<hkern u1="n" u2="t" k="10" />
<hkern u1="o" u2="x" k="41" />
<hkern u1="o" u2="w" k="10" />
<hkern u1="o" u2="t" k="4" />
<hkern u1="o" u2="f" k="10" />
<hkern u1="p" u2="x" k="41" />
<hkern u1="p" u2="w" k="10" />
<hkern u1="p" u2="t" k="4" />
<hkern u1="p" u2="f" k="10" />
<hkern u1="q" u2="&#x201e;" k="-131" />
<hkern u1="q" u2="&#x201a;" k="-131" />
<hkern u1="q" u2="&#x2e;" k="-131" />
<hkern u1="q" u2="&#x2c;" k="-131" />
<hkern u1="q" u2="&#x27;" k="49" />
<hkern u1="q" u2="&#x22;" k="49" />
<hkern u1="q" u2="j" k="-131" />
<hkern u1="r" u2="&#x201e;" k="184" />
<hkern u1="r" u2="&#x201d;" k="-106" />
<hkern u1="r" u2="&#x201a;" k="184" />
<hkern u1="r" u2="&#x2019;" k="-106" />
<hkern u1="r" u2="&#x153;" k="78" />
<hkern u1="r" u2="&#xf8;" k="78" />
<hkern u1="r" u2="&#xf6;" k="78" />
<hkern u1="r" u2="&#xf5;" k="78" />
<hkern u1="r" u2="&#xf4;" k="78" />
<hkern u1="r" u2="&#xf3;" k="78" />
<hkern u1="r" u2="&#xf2;" k="78" />
<hkern u1="r" u2="&#xf0;" k="78" />
<hkern u1="r" u2="&#xeb;" k="78" />
<hkern u1="r" u2="&#xea;" k="78" />
<hkern u1="r" u2="&#xe9;" k="78" />
<hkern u1="r" u2="&#xe8;" k="78" />
<hkern u1="r" u2="&#xe7;" k="78" />
<hkern u1="r" u2="&#xe6;" k="78" />
<hkern u1="r" u2="&#xe5;" k="78" />
<hkern u1="r" u2="&#xe4;" k="78" />
<hkern u1="r" u2="&#xe3;" k="78" />
<hkern u1="r" u2="&#xe2;" k="78" />
<hkern u1="r" u2="&#xe1;" k="78" />
<hkern u1="r" u2="&#xe0;" k="78" />
<hkern u1="r" u2="s" k="18" />
<hkern u1="r" u2="q" k="78" />
<hkern u1="r" u2="o" k="78" />
<hkern u1="r" u2="g" k="78" />
<hkern u1="r" u2="e" k="78" />
<hkern u1="r" u2="d" k="78" />
<hkern u1="r" u2="c" k="78" />
<hkern u1="r" u2="a" k="78" />
<hkern u1="r" u2="&#x2e;" k="184" />
<hkern u1="r" u2="&#x2c;" k="184" />
<hkern u1="r" u2="x" k="18" />
<hkern u1="r" u2="&#x2d;" k="78" />
<hkern u1="s" u2="y" k="25" />
<hkern u1="s" u2="T" k="184" />
<hkern u1="t" u2="&#x201d;" k="31" />
<hkern u1="t" u2="&#x2019;" k="31" />
<hkern u1="t" u2="&#x153;" k="70" />
<hkern u1="t" u2="&#xff;" k="78" />
<hkern u1="t" u2="&#xfd;" k="78" />
<hkern u1="t" u2="&#xfc;" k="39" />
<hkern u1="t" u2="&#xfb;" k="39" />
<hkern u1="t" u2="&#xfa;" k="39" />
<hkern u1="t" u2="&#xf9;" k="39" />
<hkern u1="t" u2="&#xf8;" k="70" />
<hkern u1="t" u2="&#xf6;" k="70" />
<hkern u1="t" u2="&#xf5;" k="70" />
<hkern u1="t" u2="&#xf4;" k="70" />
<hkern u1="t" u2="&#xf3;" k="70" />
<hkern u1="t" u2="&#xf2;" k="70" />
<hkern u1="t" u2="&#xf0;" k="70" />
<hkern u1="t" u2="&#xeb;" k="70" />
<hkern u1="t" u2="&#xea;" k="70" />
<hkern u1="t" u2="&#xe9;" k="70" />
<hkern u1="t" u2="&#xe8;" k="70" />
<hkern u1="t" u2="&#xe7;" k="70" />
<hkern u1="t" u2="&#xe6;" k="70" />
<hkern u1="t" u2="&#xe5;" k="70" />
<hkern u1="t" u2="&#xe4;" k="70" />
<hkern u1="t" u2="&#xe3;" k="70" />
<hkern u1="t" u2="&#xe2;" k="70" />
<hkern u1="t" u2="&#xe1;" k="70" />
<hkern u1="t" u2="&#xe0;" k="70" />
<hkern u1="t" u2="y" k="78" />
<hkern u1="t" u2="v" k="78" />
<hkern u1="t" u2="u" k="39" />
<hkern u1="t" u2="q" k="70" />
<hkern u1="t" u2="o" k="70" />
<hkern u1="t" u2="g" k="70" />
<hkern u1="t" u2="e" k="70" />
<hkern u1="t" u2="d" k="70" />
<hkern u1="t" u2="c" k="70" />
<hkern u1="t" u2="a" k="70" />
<hkern u1="t" u2="w" k="57" />
<hkern u1="t" u2="t" k="31" />
<hkern u1="t" u2="f" k="41" />
<hkern u1="u" u2="&#xff;" k="18" />
<hkern u1="u" u2="&#xfd;" k="18" />
<hkern u1="u" u2="y" k="18" />
<hkern u1="u" u2="v" k="18" />
<hkern u1="v" u2="x" k="31" />
<hkern u1="w" u2="&#x201e;" k="109" />
<hkern u1="w" u2="&#x201d;" k="-59" />
<hkern u1="w" u2="&#x201a;" k="109" />
<hkern u1="w" u2="&#x2019;" k="-59" />
<hkern u1="w" u2="&#x153;" k="10" />
<hkern u1="w" u2="&#xf8;" k="10" />
<hkern u1="w" u2="&#xf6;" k="10" />
<hkern u1="w" u2="&#xf5;" k="10" />
<hkern u1="w" u2="&#xf4;" k="10" />
<hkern u1="w" u2="&#xf3;" k="10" />
<hkern u1="w" u2="&#xf2;" k="10" />
<hkern u1="w" u2="&#xf0;" k="10" />
<hkern u1="w" u2="&#xeb;" k="10" />
<hkern u1="w" u2="&#xea;" k="10" />
<hkern u1="w" u2="&#xe9;" k="10" />
<hkern u1="w" u2="&#xe8;" k="10" />
<hkern u1="w" u2="&#xe7;" k="10" />
<hkern u1="w" u2="&#xe6;" k="10" />
<hkern u1="w" u2="&#xe5;" k="10" />
<hkern u1="w" u2="&#xe4;" k="10" />
<hkern u1="w" u2="&#xe3;" k="10" />
<hkern u1="w" u2="&#xe2;" k="10" />
<hkern u1="w" u2="&#xe1;" k="10" />
<hkern u1="w" u2="&#xe0;" k="10" />
<hkern u1="w" u2="z" k="10" />
<hkern u1="w" u2="q" k="10" />
<hkern u1="w" u2="o" k="10" />
<hkern u1="w" u2="g" k="10" />
<hkern u1="w" u2="e" k="10" />
<hkern u1="w" u2="d" k="10" />
<hkern u1="w" u2="c" k="10" />
<hkern u1="w" u2="a" k="10" />
<hkern u1="w" u2="&#x2e;" k="109" />
<hkern u1="w" u2="&#x2c;" k="109" />
<hkern u1="w" u2="&#x27;" k="-37" />
<hkern u1="w" u2="&#x22;" k="-37" />
<hkern u1="w" u2="x" k="31" />
<hkern u1="x" u2="&#x201d;" k="10" />
<hkern u1="x" u2="&#x2019;" k="10" />
<hkern u1="x" u2="&#x153;" k="49" />
<hkern u1="x" u2="&#xff;" k="31" />
<hkern u1="x" u2="&#xfd;" k="31" />
<hkern u1="x" u2="&#xfc;" k="39" />
<hkern u1="x" u2="&#xfb;" k="39" />
<hkern u1="x" u2="&#xfa;" k="39" />
<hkern u1="x" u2="&#xf9;" k="39" />
<hkern u1="x" u2="&#xf8;" k="49" />
<hkern u1="x" u2="&#xf6;" k="49" />
<hkern u1="x" u2="&#xf5;" k="49" />
<hkern u1="x" u2="&#xf4;" k="49" />
<hkern u1="x" u2="&#xf3;" k="49" />
<hkern u1="x" u2="&#xf2;" k="49" />
<hkern u1="x" u2="&#xf0;" k="49" />
<hkern u1="x" u2="&#xeb;" k="49" />
<hkern u1="x" u2="&#xea;" k="49" />
<hkern u1="x" u2="&#xe9;" k="49" />
<hkern u1="x" u2="&#xe8;" k="49" />
<hkern u1="x" u2="&#xe7;" k="49" />
<hkern u1="x" u2="&#xe6;" k="49" />
<hkern u1="x" u2="&#xe5;" k="49" />
<hkern u1="x" u2="&#xe4;" k="49" />
<hkern u1="x" u2="&#xe3;" k="49" />
<hkern u1="x" u2="&#xe2;" k="49" />
<hkern u1="x" u2="&#xe1;" k="49" />
<hkern u1="x" u2="&#xe0;" k="49" />
<hkern u1="x" u2="y" k="31" />
<hkern u1="x" u2="v" k="31" />
<hkern u1="x" u2="u" k="39" />
<hkern u1="x" u2="q" k="49" />
<hkern u1="x" u2="o" k="49" />
<hkern u1="x" u2="g" k="49" />
<hkern u1="x" u2="e" k="49" />
<hkern u1="x" u2="d" k="49" />
<hkern u1="x" u2="c" k="49" />
<hkern u1="x" u2="a" k="49" />
<hkern u1="x" u2="&#x27;" k="31" />
<hkern u1="x" u2="&#x22;" k="31" />
<hkern u1="x" u2="w" k="18" />
<hkern u1="x" u2="t" k="10" />
<hkern u1="x" u2="Y" k="92" />
<hkern u1="y" u2="&#xfc;" k="31" />
<hkern u1="y" u2="&#xfb;" k="31" />
<hkern u1="y" u2="&#xfa;" k="31" />
<hkern u1="y" u2="&#xf9;" k="31" />
<hkern u1="y" u2="u" k="31" />
<hkern u1="y" u2="x" k="31" />
<hkern u1="z" u2="w" k="18" />
<hkern u1="&#xc0;" u2="w" k="39" />
<hkern u1="&#xc0;" u2="t" k="10" />
<hkern u1="&#xc0;" u2="l" k="10" />
<hkern u1="&#xc0;" u2="j" k="-37" />
<hkern u1="&#xc0;" u2="f" k="16" />
<hkern u1="&#xc0;" u2="Y" k="166" />
<hkern u1="&#xc0;" u2="W" k="96" />
<hkern u1="&#xc0;" u2="V" k="143" />
<hkern u1="&#xc0;" u2="T" k="184" />
<hkern u1="&#xc0;" u2="D" k="39" />
<hkern u1="&#xc1;" u2="w" k="39" />
<hkern u1="&#xc1;" u2="t" k="10" />
<hkern u1="&#xc1;" u2="l" k="10" />
<hkern u1="&#xc1;" u2="j" k="-37" />
<hkern u1="&#xc1;" u2="f" k="16" />
<hkern u1="&#xc1;" u2="Y" k="166" />
<hkern u1="&#xc1;" u2="W" k="96" />
<hkern u1="&#xc1;" u2="V" k="143" />
<hkern u1="&#xc1;" u2="T" k="184" />
<hkern u1="&#xc1;" u2="D" k="39" />
<hkern u1="&#xc2;" u2="w" k="39" />
<hkern u1="&#xc2;" u2="t" k="10" />
<hkern u1="&#xc2;" u2="l" k="10" />
<hkern u1="&#xc2;" u2="j" k="-37" />
<hkern u1="&#xc2;" u2="f" k="16" />
<hkern u1="&#xc2;" u2="Y" k="166" />
<hkern u1="&#xc2;" u2="W" k="96" />
<hkern u1="&#xc2;" u2="V" k="143" />
<hkern u1="&#xc2;" u2="T" k="184" />
<hkern u1="&#xc2;" u2="D" k="39" />
<hkern u1="&#xc3;" u2="w" k="39" />
<hkern u1="&#xc3;" u2="t" k="10" />
<hkern u1="&#xc3;" u2="l" k="10" />
<hkern u1="&#xc3;" u2="j" k="-37" />
<hkern u1="&#xc3;" u2="f" k="16" />
<hkern u1="&#xc3;" u2="Y" k="166" />
<hkern u1="&#xc3;" u2="W" k="96" />
<hkern u1="&#xc3;" u2="V" k="143" />
<hkern u1="&#xc3;" u2="T" k="184" />
<hkern u1="&#xc3;" u2="D" k="39" />
<hkern u1="&#xc4;" u2="w" k="39" />
<hkern u1="&#xc4;" u2="t" k="10" />
<hkern u1="&#xc4;" u2="l" k="10" />
<hkern u1="&#xc4;" u2="j" k="-37" />
<hkern u1="&#xc4;" u2="f" k="16" />
<hkern u1="&#xc4;" u2="Y" k="166" />
<hkern u1="&#xc4;" u2="W" k="96" />
<hkern u1="&#xc4;" u2="V" k="143" />
<hkern u1="&#xc4;" u2="T" k="184" />
<hkern u1="&#xc4;" u2="D" k="39" />
<hkern u1="&#xc5;" u2="w" k="39" />
<hkern u1="&#xc5;" u2="t" k="10" />
<hkern u1="&#xc5;" u2="l" k="10" />
<hkern u1="&#xc5;" u2="j" k="-37" />
<hkern u1="&#xc5;" u2="f" k="16" />
<hkern u1="&#xc5;" u2="Y" k="166" />
<hkern u1="&#xc5;" u2="W" k="96" />
<hkern u1="&#xc5;" u2="V" k="143" />
<hkern u1="&#xc5;" u2="T" k="184" />
<hkern u1="&#xc5;" u2="D" k="39" />
<hkern u1="&#xd0;" u2="Z" k="57" />
<hkern u1="&#xd0;" u2="Y" k="117" />
<hkern u1="&#xd0;" u2="X" k="70" />
<hkern u1="&#xd0;" u2="V" k="39" />
<hkern u1="&#xd0;" u2="T" k="117" />
<hkern u1="&#xd0;" u2="J" k="70" />
<hkern u1="&#xd1;" u2="&#x201e;" k="78" />
<hkern u1="&#xd1;" u2="&#x201a;" k="78" />
<hkern u1="&#xd1;" u2="&#x2e;" k="78" />
<hkern u1="&#xd1;" u2="&#x2c;" k="78" />
<hkern u1="&#xd2;" u2="Z" k="57" />
<hkern u1="&#xd2;" u2="Y" k="117" />
<hkern u1="&#xd2;" u2="X" k="70" />
<hkern u1="&#xd2;" u2="V" k="39" />
<hkern u1="&#xd2;" u2="T" k="117" />
<hkern u1="&#xd2;" u2="J" k="70" />
<hkern u1="&#xd3;" u2="Z" k="57" />
<hkern u1="&#xd3;" u2="Y" k="117" />
<hkern u1="&#xd3;" u2="X" k="70" />
<hkern u1="&#xd3;" u2="V" k="39" />
<hkern u1="&#xd3;" u2="T" k="117" />
<hkern u1="&#xd3;" u2="J" k="70" />
<hkern u1="&#xd4;" u2="Z" k="57" />
<hkern u1="&#xd4;" u2="Y" k="117" />
<hkern u1="&#xd4;" u2="X" k="70" />
<hkern u1="&#xd4;" u2="V" k="39" />
<hkern u1="&#xd4;" u2="T" k="117" />
<hkern u1="&#xd4;" u2="J" k="70" />
<hkern u1="&#xd5;" u2="Z" k="57" />
<hkern u1="&#xd5;" u2="Y" k="117" />
<hkern u1="&#xd5;" u2="X" k="70" />
<hkern u1="&#xd5;" u2="V" k="39" />
<hkern u1="&#xd5;" u2="T" k="117" />
<hkern u1="&#xd5;" u2="J" k="70" />
<hkern u1="&#xd6;" u2="Z" k="57" />
<hkern u1="&#xd6;" u2="Y" k="117" />
<hkern u1="&#xd6;" u2="X" k="70" />
<hkern u1="&#xd6;" u2="V" k="39" />
<hkern u1="&#xd6;" u2="T" k="117" />
<hkern u1="&#xd6;" u2="J" k="70" />
<hkern u1="&#xd8;" u2="Z" k="57" />
<hkern u1="&#xd8;" u2="Y" k="117" />
<hkern u1="&#xd8;" u2="X" k="70" />
<hkern u1="&#xd8;" u2="V" k="39" />
<hkern u1="&#xd8;" u2="T" k="117" />
<hkern u1="&#xd8;" u2="J" k="70" />
<hkern u1="&#xd9;" u2="Z" k="31" />
<hkern u1="&#xd9;" u2="X" k="31" />
<hkern u1="&#xd9;" u2="J" k="39" />
<hkern u1="&#xda;" u2="Z" k="31" />
<hkern u1="&#xda;" u2="X" k="31" />
<hkern u1="&#xda;" u2="J" k="39" />
<hkern u1="&#xdb;" u2="Z" k="31" />
<hkern u1="&#xdb;" u2="X" k="31" />
<hkern u1="&#xdb;" u2="J" k="39" />
<hkern u1="&#xdc;" u2="Z" k="31" />
<hkern u1="&#xdc;" u2="X" k="31" />
<hkern u1="&#xdc;" u2="J" k="39" />
<hkern u1="&#xe8;" u2="x" k="31" />
<hkern u1="&#xe8;" u2="w" k="4" />
<hkern u1="&#xe9;" u2="x" k="31" />
<hkern u1="&#xe9;" u2="w" k="4" />
<hkern u1="&#xea;" u2="x" k="31" />
<hkern u1="&#xea;" u2="w" k="4" />
<hkern u1="&#xeb;" u2="x" k="31" />
<hkern u1="&#xeb;" u2="w" k="4" />
<hkern u1="&#xef;" u2="l" k="-80" />
<hkern u1="&#xef;" u2="k" k="-80" />
<hkern u1="&#xef;" u2="h" k="-80" />
<hkern u1="&#xef;" u2="b" k="-80" />
<hkern u1="&#xef;" u2="U" k="-80" />
<hkern u1="&#xef;" u2="R" k="-80" />
<hkern u1="&#xef;" u2="P" k="-80" />
<hkern u1="&#xef;" u2="N" k="-80" />
<hkern u1="&#xef;" u2="M" k="-80" />
<hkern u1="&#xef;" u2="L" k="-80" />
<hkern u1="&#xef;" u2="K" k="-80" />
<hkern u1="&#xef;" u2="H" k="-80" />
<hkern u1="&#xef;" u2="F" k="-80" />
<hkern u1="&#xef;" u2="E" k="-80" />
<hkern u1="&#xef;" u2="D" k="-80" />
<hkern u1="&#xef;" u2="B" k="-80" />
<hkern u1="&#xf1;" u2="w" k="10" />
<hkern u1="&#xf1;" u2="t" k="10" />
<hkern u1="&#xf2;" u2="x" k="41" />
<hkern u1="&#xf2;" u2="w" k="10" />
<hkern u1="&#xf2;" u2="f" k="10" />
<hkern u1="&#xf3;" u2="x" k="41" />
<hkern u1="&#xf3;" u2="w" k="10" />
<hkern u1="&#xf3;" u2="f" k="10" />
<hkern u1="&#xf4;" u2="x" k="41" />
<hkern u1="&#xf4;" u2="w" k="10" />
<hkern u1="&#xf4;" u2="f" k="10" />
<hkern u1="&#xf5;" u2="x" k="41" />
<hkern u1="&#xf5;" u2="w" k="10" />
<hkern u1="&#xf5;" u2="f" k="10" />
<hkern u1="&#xf6;" u2="x" k="41" />
<hkern u1="&#xf6;" u2="w" k="10" />
<hkern u1="&#xf6;" u2="f" k="10" />
<hkern u1="&#xfd;" u2="x" k="31" />
<hkern u1="&#xfe;" u2="x" k="41" />
<hkern u1="&#xfe;" u2="w" k="10" />
<hkern u1="&#xfe;" u2="f" k="10" />
<hkern u1="&#xff;" u2="x" k="31" />
<hkern u1="&#x153;" u2="x" k="31" />
<hkern u1="&#x153;" u2="w" k="4" />
<hkern u1="&#x2018;" u2="W" k="-84" />
<hkern u1="&#x2019;" u2="&#x153;" k="49" />
<hkern u1="&#x2019;" u2="&#xff;" k="-37" />
<hkern u1="&#x2019;" u2="&#xfd;" k="-37" />
<hkern u1="&#x2019;" u2="&#xf8;" k="49" />
<hkern u1="&#x2019;" u2="&#xf6;" k="49" />
<hkern u1="&#x2019;" u2="&#xf5;" k="49" />
<hkern u1="&#x2019;" u2="&#xf4;" k="49" />
<hkern u1="&#x2019;" u2="&#xf3;" k="49" />
<hkern u1="&#x2019;" u2="&#xf2;" k="49" />
<hkern u1="&#x2019;" u2="&#xf0;" k="49" />
<hkern u1="&#x2019;" u2="&#xeb;" k="49" />
<hkern u1="&#x2019;" u2="&#xea;" k="49" />
<hkern u1="&#x2019;" u2="&#xe9;" k="49" />
<hkern u1="&#x2019;" u2="&#xe8;" k="49" />
<hkern u1="&#x2019;" u2="&#xe7;" k="49" />
<hkern u1="&#x2019;" u2="&#xe6;" k="49" />
<hkern u1="&#x2019;" u2="&#xe5;" k="49" />
<hkern u1="&#x2019;" u2="&#xe4;" k="49" />
<hkern u1="&#x2019;" u2="&#xe3;" k="49" />
<hkern u1="&#x2019;" u2="&#xe2;" k="49" />
<hkern u1="&#x2019;" u2="&#xe1;" k="49" />
<hkern u1="&#x2019;" u2="&#xe0;" k="49" />
<hkern u1="&#x2019;" u2="y" k="-37" />
<hkern u1="&#x2019;" u2="v" k="-37" />
<hkern u1="&#x2019;" u2="s" k="31" />
<hkern u1="&#x2019;" u2="q" k="49" />
<hkern u1="&#x2019;" u2="o" k="49" />
<hkern u1="&#x2019;" u2="g" k="49" />
<hkern u1="&#x2019;" u2="e" k="49" />
<hkern u1="&#x2019;" u2="d" k="49" />
<hkern u1="&#x2019;" u2="c" k="49" />
<hkern u1="&#x2019;" u2="a" k="49" />
<hkern u1="&#x2019;" u2="&#xf1;" k="49" />
<hkern u1="&#x2019;" u2="r" k="31" />
<hkern u1="&#x2019;" u2="n" k="49" />
<hkern u1="&#x2019;" u2="l" k="-84" />
<hkern u1="&#x201c;" u2="W" k="-84" />
<hkern u1="&#xfb02;" u2="w" k="43" />
<hkern u1="&#xfb02;" u2="l" k="57" />
<hkern u1="&#xfb02;" u2="f" k="31" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="49" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="49" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="10" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="v,y,yacute,ydieresis" 	k="66" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="16" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="quotedbl,quotesingle" 	k="166" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="quoteright,quotedblright" 	k="88" />
<hkern g1="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	g2="s" 	k="-37" />
<hkern g1="C,Ccedilla" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="31" />
<hkern g1="C,Ccedilla" 	g2="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	k="10" />
<hkern g1="C,Ccedilla" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="31" />
<hkern g1="C,Ccedilla" 	g2="v,y,yacute,ydieresis" 	k="47" />
<hkern g1="C,Ccedilla" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="47" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="31" />
<hkern g1="E,Egrave,Eacute,Ecircumflex,Edieresis" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="10" />
<hkern g1="I,Igrave,Iacute,Icircumflex,Idieresis" 	g2="C,G,O,Q,Ccedilla,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash,OE" 	k="57" />
<hkern g1="I,Igrave,Iacute,Icircumflex,Idieresis" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="70" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="70" />
<hkern g1="D,O,Q,Eth,Ograve,Oacute,Ocircumflex,Otilde,Odieresis,Oslash" 	g2="I,Igrave,Iacute,Icircumflex,Idieresis" 	k="57" />
<hkern g1="S" 	g2="v,y,yacute,ydieresis" 	k="49" />
<hkern g1="S" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="31" />
<hkern g1="S" 	g2="comma,period,quotesinglbase,quotedblbase" 	k="31" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="78" />
<hkern g1="U,Ugrave,Uacute,Ucircumflex,Udieresis" 	g2="comma,period,quotesinglbase,quotedblbase" 	k="49" />
<hkern g1="c,ccedilla" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="39" />
<hkern g1="e,egrave,eacute,ecircumflex,edieresis,oe" 	g2="v,y,yacute,ydieresis" 	k="14" />
<hkern g1="e,egrave,eacute,ecircumflex,edieresis,oe" 	g2="quoteright,quotedblright" 	k="49" />
<hkern g1="h,m,n,ntilde" 	g2="v,y,yacute,ydieresis" 	k="18" />
<hkern g1="h,m,n,ntilde" 	g2="quotedbl,quotesingle" 	k="31" />
<hkern g1="h,m,n,ntilde" 	g2="quoteright,quotedblright" 	k="10" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,thorn" 	g2="v,y,yacute,ydieresis" 	k="18" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,thorn" 	g2="quotedbl,quotesingle" 	k="18" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,thorn" 	g2="quoteright,quotedblright" 	k="49" />
<hkern g1="b,o,p,ograve,oacute,ocircumflex,otilde,odieresis,thorn" 	g2="comma,period,quotesinglbase,quotedblbase" 	k="70" />
<hkern g1="quotedbl,quotesingle" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="109" />
<hkern g1="quotedbl,quotesingle" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="223" />
<hkern g1="s" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="10" />
<hkern g1="v,y,yacute,ydieresis" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="49" />
<hkern g1="v,y,yacute,ydieresis" 	g2="quotedbl,quotesingle" 	k="-37" />
<hkern g1="v,y,yacute,ydieresis" 	g2="quoteright,quotedblright" 	k="-84" />
<hkern g1="v,y,yacute,ydieresis" 	g2="comma,period,quotesinglbase,quotedblbase" 	k="166" />
<hkern g1="v,y,yacute,ydieresis" 	g2="z" 	k="10" />
<hkern g1="z" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="41" />
<hkern g1="z" 	g2="v,y,yacute,ydieresis" 	k="10" />
<hkern g1="z" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="53" />
<hkern g1="quoteleft,quotedblleft" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="70" />
<hkern g1="quoteleft,quotedblleft" 	g2="A,Agrave,Aacute,Acircumflex,Atilde,Adieresis,Aring" 	k="143" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="v,y,yacute,ydieresis" 	k="10" />
<hkern g1="a,agrave,aacute,acircumflex,atilde,adieresis,aring" 	g2="quotedbl,quotesingle" 	k="18" />
<hkern g1="l,uniFB02" 	g2="u,ugrave,uacute,ucircumflex,udieresis" 	k="70" />
<hkern g1="l,uniFB02" 	g2="v,y,yacute,ydieresis" 	k="53" />
<hkern g1="l,uniFB02" 	g2="a,c,d,e,g,o,q,agrave,aacute,acircumflex,atilde,adieresis,aring,ae,ccedilla,egrave,eacute,ecircumflex,edieresis,eth,ograve,oacute,ocircumflex,otilde,odieresis,oslash,oe" 	k="45" />
<hkern g1="l,uniFB02" 	g2="quoteright,quotedblright" 	k="88" />
<hkern g1="b,o,p" 	g2="z" 	k="41" />
<hkern g1="b,o,p" 	g2="v,y,yacute,ydieresis" 	k="49" />
</font>
</defs></svg> 