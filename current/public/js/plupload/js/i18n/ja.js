// Japanese
plupload.addI18n({
   'Select files' : 'ファイル選択',
   'Add files to the upload queue and click the start button.' : 'ファイルをアップロードキューに追加してスタートボタンをクリックしてください',
   'Filename' : 'ファイル名',
   'Status' : 'ステータス',
   'Size' : 'サイズ',
   'Add Files' : 'ファイルを追加',
   'Stop Upload' : 'アップロード停止',
   'Start Upload' : 'アップロード',
   'Add files' : 'ファイルを追加',
   'Add files.' : 'ファイルを追加',
   'Stop current upload' : '現在のアップロードを停止',
   'Start uploading queue' : 'アップロード',
   'Stop upload' : 'アップロード停止',
   'Start upload' : 'アップロード',
   'Uploaded %d/%d files': 'アップロード中 %d/%d ファイル',
   'N/A' : 'N/A',
   'Drag files here.' : 'ここにファイルをドラッグ',
   'File extension error.': 'ファイル拡張子エラー',
   'File size error.': 'ファイルサイズエラー',
   'File count error.': 'ファイル数エラー',
   'Init error.': 'イニシャライズエラー',
   'HTTP Error.': 'HTTP エラー',
   'Security error.': 'セキュリティエラー',
   'Generic error.': 'エラー',
   'IO error.': 'IO エラー',
   'File: %s': 'ファイル: %s',
   'Close': '閉じる',
   '%d files queued': '%d ファイルが追加されました',
   'Using runtime: ': 'モード: ',
   'File: %f, size: %s, max file size: %m': 'ファイル: %f, サイズ: %s, 最大ファイルサイズ: %m',
   'Upload element accepts only %d file(s) at a time. Extra files were stripped.': 'アップロード可能なファイル数は %d です。余分なファイルは削除されました',
   'Upload URL might be wrong or doesn\'t exist': 'アップロード先の URL が存在しません',
   'Error: File too large: ': 'エラー: サイズが大きすぎます: ',
   'Error: Invalid file extension: ': 'エラー: 拡張子が許可されていません: '
});
