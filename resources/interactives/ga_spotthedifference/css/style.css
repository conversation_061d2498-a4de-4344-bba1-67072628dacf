#verifyMatch:hover {
    border-bottom-color: #f9b46e;
}

.big-btn {
    background-color: #f9b46e;
    border-bottom: solid 3px #e59751;
}

.content-holder {
    padding: 50px 0;
    text-align: center;
    width: 100%;
    margin: 0 auto;
}
.content-holder:after {
    content:"";
    display:table;
    clear:both;
}

.diff-image-holder {
    background-color: #fff;
    width: 49%;
    float: left;
    padding: 1% 0;
}

.diff-image-holder img {
    width: 100%;
}

.content-holder .diff-image-holder:last-child {
    margin-left: 0;
    float: right;
}

.area:hover {
    background: red;
}

.zoom-images img {
    width: 120px;
    border-radius: 50%;
    border: 4px solid rgba(93,68,88,0.2);
    position: absolute;
    display: none;
}

.zoom-images .difference-frames {
    top: 42%;
    left: 22%;
}

.zoom-images .difference-window {
    top: 41%;
    left: 12%;
}

.zoom-images .difference-fruit {
    top: 42%;
    left: 32%;
}

.zoom-images .difference-furniture {
    top: 58%;
    left: 11%;
}

.zoom-images .difference-lamp {
    top: 45%;
    left: 5%;
    }

.zoom-images .difference-tv {
    top: 52%;
    left: 22%;
}

.diff-img,
map area {
    cursor: pointer;
}