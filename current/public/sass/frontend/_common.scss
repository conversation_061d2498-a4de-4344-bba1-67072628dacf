
/*
|--------------------------------------------------------------------------
| Headings
|--------------------------------------------------------------------------
*/

h1 {
     font-family: 'CambridgeRound-Semibold', "Helvetica Neue", Helvetica, Arial, sans-serif;
     font-size: 40px;
     line-height: 50px;
     letter-spacing: 0.030em;
}

h2 {
     font-family: 'CambridgeRound-Semibold', "Helvetica Neue", Helvetica, Arial, sans-serif;
     font-size: 32px;
     line-height: 42px;
     letter-spacing: 0.050em;
}

h3 {
     font-family: 'CambridgeRound-Regular', "Helvetica Neue", Helvetica, Arial, sans-serif;
     font-size: 18px;
     line-height: 26px;
     letter-spacing: 0.020em;
}

h4 {
     font-family: 'CambridgeRound-Regular', "Helvetica Neue", Helvetica, Arial, sans-serif;
     font-size: 16px;
     line-height: 22px;
     letter-spacing: 0.020em;
}

/*
|--------------------------------------------------------------------------
| Links
|--------------------------------------------------------------------------
*/


a {
     color: $grey;
}

.row {
     @include make-row();
}


/*
|--------------------------------------------------------------------------
| Container
|--------------------------------------------------------------------------
*/

.container {
     @include clearfix();
}

/*
|--------------------------------------------------------------------------
| Forms
|--------------------------------------------------------------------------
*/


.field-wrap {
     @include clearfix();
     margin-bottom: 20px;
     position: relative;
     overflow: hidden;
     p {
          font-size: 12px;
          line-height: 20px;
          color: $dark-blue;
     }
}

.field-wrap label {
     width: auto;
     color: #404f70;
     position: absolute;
     left: 0;
     top: -1px;
     padding: 10px 11px;
     border-right: solid 2px #404f70;
     opacity: 0;
     @include transition(all 0.2s ease);
     &.focused {
          opacity: 1;
          border-right-color: $blue;
     }
}

.ninja-forms-all-fields-wrap label {
     width: auto;
}

.ie-lt10 .field-wrap label {
     position: static;
     opacity: 1;
     display: block;
     padding: 10px 0;
     border: 0;
}

.field {
     width: 100%;
     padding: 10px;
     background-color: transparent;
     border: solid 2px #404f70;
     border-radius: $border-radius;
     color: #404f70;
     @include placeholder($color: #404f70);
     @include transition(all 0.2s ease);

     &:focus {
          outline: none;
     }

     &.field-login {
          border-color: $blue;
          @include placeholder($color: $blue);
          padding-left: 50px;
     }

}

.textarea {
     width: 100%;
     height: 250px;
     padding: 10px;
     background-color: transparent;
     border: solid 2px #404f70;
     border-radius: $border-radius;
     color: #404f70;
}


.form-section {
     padding: 50px 0;
     border-bottom: solid 1px #ddd;
     &.last {
          border-bottom: 0;
     }
}

.form-heading {
     font-family: 'CambridgeRound-semibold', "Helvetica Neue", Helvetica, Arial, sans-serif;
     @include clearfix();
     margin-bottom: 20px;
     .title {
          font-size: 24px;
          line-height: 50px;
          color: $blue;
     }
     .num {
          width: 50px;
          height: 50px;
          font-size: 36px;
          line-height: 50px;
          color: #fff;
          float: left;
          border-radius: 100%;
          text-align: center;
          background-color: $blue;
          margin-right: 20px;
     }
}

.check {
     @include clearfix();
     line-height: 20px;
     margin-bottom: 20px;
     .box {
          cursor: pointer;
          position: relative;
          float: left;
          width: 20px;
          height: 20px;
          border: solid 2px $dark-blue;
          margin-right: 20px;
          border-radius: $border-radius;
          img { display: none; }
          &.checked img {
               display: block;
               position: absolute;
               left: 3px;
               top: -3px;
               z-index: 999;
          }
     }
     input {
          display: none;
     }
}

.submit-wrap {
     text-align: center;
     margin-top: 40px;
}


/*
|--------------------------------------------------------------------------
| Buttons
|--------------------------------------------------------------------------
*/


.btn {
     display: inline-block;
     border: 1px solid;
     background-color: transparent;
     border-radius: $border-radius;
     padding: 12px;
     font-size: 16px;
     text-decoration: none;
     color: $white;
     height: 47px;
     font-weight: normal;
     line-height: 22px;

     &.btn-block {
          width: 100%;
     }

     &.btn-wide {
          width: 200px;
     }

     &.btn-large {
          padding: 15px 40px;
          font-size: 22px;
     }

     &.btn-gold {
          background-color: $gold;
          border-color: $gold;
     }
     &.btn-gold:hover {
          color: $gold;
          background-color: transparent;
     }
     &.btn-gold.btn-header:hover {
          color: $white;
          background-color: $gold;
     }

     &.btn-white {
          border-color: $white;
          background-color: $white;
          color: $grey;
     }
     &.btn-white:hover {
          color: $grey;
     }

     &.btn-white-inv, &.btn-purple-inv{
          border-color: $white;
          background-color: transparent;
          color: $white;
     }
     &.btn-white-inv:hover {
          background-color: $white;
          color: $dark-blue;
     }
     &.btn-purple-inv:hover {
          background-color: $white;
          color: $purple;
     }

     &.btn-blue {
          background-color: transparent;
          color: $dark-blue;
     }
     &.btn-blue:hover {
          background-color: $dark-blue;
          color: $white;
     }

     &.btn-header:hover {
          opacity: 0.85 !important;
     }
}

/*
|--------------------------------------------------------------------------
| Horizontal Rule
|--------------------------------------------------------------------------
*/

hr {
     border: 0;
     height: 3px;
     &.short {
          width: 165px;
     }
     &.small {
          margin: 15px auto;
     }
     &.gold {
          background-color: $dark-gold;
          margin: 5px auto;
     }
}

@media( min-width: 980px ) {
     hr {
          margin: 30px 0;
     }
}



/*
|--------------------------------------------------------------------------
| Footer
|--------------------------------------------------------------------------
*/


footer {
     color: #fff;

     .container{
          background-color: $dark-blue;
          width: 100%;
          text-align: center;
     }

     .copy{
          font-size: 14px;
          letter-spacing: 0.28px;
          line-height: 20px;
     }

     .footer-top{
          height: 100px;
          margin-bottom: 1px;

          .useful-links {
               margin: 0;
               padding: 0;
               list-style: none;
               position: relative;
               top: 30px;

               li {
                    box-sizing: border-box;
                    display: inline-block;
                    margin: 10px;
                    border-bottom: solid 2px transparent;
                    cursor: pointer;
               }
               li::after {
                    content: "";
                    border: 2px solid;
                    border-width: 0 0 0 2px;
                    position: relative;
                    left: 12px;
               }
               li:last-child::after {
                    content: none;
               }
               li:hover{
                    border-bottom: solid 2px $white;
                    padding-bottom: 3px;
               }
               a  {
                    list-style-position: outside;
                    list-style-type: none;
                    color: #fff;
                    text-decoration: none;
                    font-size: 16px;
                    letter-spacing: 0.32px;
                    line-height: 22px;
               }
          }
     }

     .footer-bottom{
          padding-top: 12px;
          height: 49px;

          .small-print{
               font-family: 'CambridgeRound-Light', "Helvetica Neue", Helvetica, Arial, sans-serif;
               font-size: 14px;
               line-height: 20px;
               letter-spacing: 0.020em;
               a {
                    color: $white;
                    text-decoration: none;
               }
          }

     }
}

@media( min-width: 980px ) {
     footer.copyright {
     }
     footer.copyright, .page-wrap:after {
          height: 100px;
     }
     .page-wrap {
          min-height: 100%;
          /* equal to footer height */
          margin-bottom: -100px;
     }

     .page-wrap:after {
          content: "";
          display: block;
     }
}


/*
|--------------------------------------------------------------------------
| Common Elements
|--------------------------------------------------------------------------
*/

blockquote {
     padding-left: 20px;
     font-style: italic;
     border-left: solid 2px $purple;
     p:before {
          content: open-quote;
          color: $purple;
     }
     p:after {
          content: close-quote;
          color: $purple;
     }
}

.text-spacer{
     margin: 0 auto 52px;
     height: 2px;
     width: 80px;
     background-color: $white;
}