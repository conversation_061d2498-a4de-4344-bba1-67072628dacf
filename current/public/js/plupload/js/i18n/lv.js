// .lv file like language pack
plupload.addI18n({
    'Select files' : 'Izvēlieties failus',
    'Add files to the upload queue and click the start button.' : 'Pieveinojiet failus rindai un klikšķiniet uz "Sākt augšupielādi" pogas.',
    'Filename' : 'Faila nosaukums',
    'Status' : 'Statuss',
    'Size' : 'Izmērs',
    'Add files' : 'Pievienot failus',
    'Stop current upload' : 'Apturēt pašreizējo augšupielādi',
    'Start uploading queue' : 'Sākt augšupielādi',
    'Drag files here.' : 'Ievelciet failus šeit',
    'Start upload' : 'Sākt augšupielādi',
    'Uploaded %d/%d files': 'Augšupielādēti %d/%d faili',
    'Stop upload': 'Pārtraukt augšupielādi',
    'Start upload': 'Sākt augšupielādi',
    '%d files queued': '%d faili pievienoti rindai',
    'File: %s': 'Fails: %s',
    'Close': 'Aizvērt',
    'Using runtime: ': 'Lieto saskarni: ',
    'File: %f, size: %s, max file size: %m': 'Fails: %f, izmērs: %s, maksimālais faila izmērs: %m',
    'Upload element accepts only %d file(s) at a time. Extra files were stripped.': 'Iespējams ielādēt tikai %d failus vienā reizē. Atlikušie faili netika pievienoti',
    'Upload URL might be wrong or doesn\'t exist': 'Augšupielādes URL varētu būt nepareizs vai neeksistē',
    'Error: File too large: ': 'Kļūda: Fails pārāk liels: ',
    'Error: Invalid file extension: ': 'Kļūda: Nekorekts faila paplašinājums:',
    'File extension error.': 'Faila paplašinājuma kļūda.',
    'File size error.': 'Faila izmēra kļūda.',
    'File count error.': 'Failu skaita kļūda',
    'Init error.': 'Inicializācijas kļūda.',
    'HTTP Error.': 'HTTP kļūda.',
    'Security error.': 'Drošības kļūda.',
    'Generic error.': 'Vispārēja rakstura kļūda.',
    'IO error.': 'Ievades/Izvades kļūda.'
});